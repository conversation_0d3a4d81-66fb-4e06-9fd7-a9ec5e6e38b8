from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
import glob
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv
import uuid
import time
import database  # Import database module for PINE collection integration
from openai import OpenAI
import requests

# Import service processors
try:
    from services.youtube_processor import process_youtube_url
    from services.article_processor import process_article_url
    from services.document_processor import SUPPORTED_EXTENSIONS, process_document_file
    from services.pdf_processor import process_pdf_file
    from services.audio_proccessor import process_audio_file
    print("✅ All service processors imported successfully")
except ImportError as e:
    print(f"⚠️ Error importing service processors: {e}")

# Load environment variables
load_dotenv()

# Configuration
FAISS_DATA_DIR = os.getenv("FAISS_DATA_DIR", "faiss_data")
OUTPUT_DIR = os.path.join(os.getcwd(), FAISS_DATA_DIR)
YOUTUBE_DIR = os.path.join(OUTPUT_DIR, "youtube")
ARTICLES_DIR = os.path.join(OUTPUT_DIR, "articles")
DOCUMENTS_DIR = os.path.join(OUTPUT_DIR, "documents")
PDFS_DIR = os.path.join(OUTPUT_DIR, "pdfs")
AUDIO_DIR = os.path.join(OUTPUT_DIR, "audio")

# DeepSeek API Configuration
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
DEEPSEEK_BASE_URL = "https://api.deepseek.com"

# Initialize Flask app
app = Flask(__name__)
CORS(app, resources={
    r"/api/*": {
        "origins": [
            "http://localhost:3000",
            "http://localhost:3001",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "http://**************:3000",
            "http://**************:3001"
        ],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"],
        "supports_credentials": True
    }
})

# Initialize embedding model
embedder = SentenceTransformer("all-MiniLM-L6-v2")

# Initialize DeepSeek client if API key is available
deepseek_client = None
if DEEPSEEK_API_KEY:
    try:
        deepseek_client = OpenAI(
            api_key=DEEPSEEK_API_KEY,
            base_url=DEEPSEEK_BASE_URL
        )
        print("✅ DeepSeek API client initialized")
    except Exception as e:
        print(f"⚠️ Failed to initialize DeepSeek client: {e}")
else:
    print("⚠️ DeepSeek API key not found")

def load_all_indices():
    """Load all FAISS indices and metadata from both YouTube and articles directories"""
    indices_data = []
    
    # Load YouTube indices
    youtube_files = glob.glob(os.path.join(YOUTUBE_DIR, "*_index.faiss"))
    for index_file in youtube_files:
        try:
            base_name = os.path.basename(index_file).replace("_index.faiss", "")
            metadata_file = os.path.join(YOUTUBE_DIR, f"{base_name}_metadata.json")
            
            if os.path.exists(metadata_file):
                index = faiss.read_index(index_file)
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                indices_data.append({
                    'index': index,
                    'metadata': metadata,
                    'type': 'youtube',
                    'id': base_name
                })
        except Exception as e:
            print(f"Error loading YouTube index {index_file}: {e}")
    
    # Load Article indices
    article_files = glob.glob(os.path.join(ARTICLES_DIR, "*_index.faiss"))
    for index_file in article_files:
        try:
            base_name = os.path.basename(index_file).replace("_index.faiss", "")
            metadata_file = os.path.join(ARTICLES_DIR, f"{base_name}_metadata.json")

            if os.path.exists(metadata_file):
                index = faiss.read_index(index_file)
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                indices_data.append({
                    'index': index,
                    'metadata': metadata,
                    'type': 'article',
                    'id': base_name
                })
        except Exception as e:
            print(f"Error loading article index {index_file}: {e}")

    # Load Document indices
    document_files = glob.glob(os.path.join(DOCUMENTS_DIR, "*_index.faiss"))
    for index_file in document_files:
        try:
            base_name = os.path.basename(index_file).replace("_index.faiss", "")
            metadata_file = os.path.join(DOCUMENTS_DIR, f"{base_name}_metadata.json")

            if os.path.exists(metadata_file):
                index = faiss.read_index(index_file)
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                indices_data.append({
                    'index': index,
                    'metadata': metadata,
                    'type': 'document',
                    'id': base_name
                })
        except Exception as e:
            print(f"Error loading document index {index_file}: {e}")

    # Load PDF indices
    pdf_files = glob.glob(os.path.join(PDFS_DIR, "*_index.faiss"))
    for index_file in pdf_files:
        try:
            base_name = os.path.basename(index_file).replace("_index.faiss", "")
            metadata_file = os.path.join(PDFS_DIR, f"{base_name}_metadata.json")

            if os.path.exists(metadata_file):
                index = faiss.read_index(index_file)
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                indices_data.append({
                    'index': index,
                    'metadata': metadata,
                    'type': 'pdf',
                    'id': base_name
                })
        except Exception as e:
            print(f"Error loading PDF index {index_file}: {e}")

    # Load Audio indices
    audio_files = glob.glob(os.path.join(AUDIO_DIR, "*_index.faiss"))
    for index_file in audio_files:
        try:
            base_name = os.path.basename(index_file).replace("_index.faiss", "")
            metadata_file = os.path.join(AUDIO_DIR, f"{base_name}_metadata.json")

            if os.path.exists(metadata_file):
                index = faiss.read_index(index_file)
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                indices_data.append({
                    'index': index,
                    'metadata': metadata,
                    'type': 'audio',
                    'id': base_name
                })
        except Exception as e:
            print(f"Error loading audio index {index_file}: {e}")

    return indices_data

def search_specific_index(query, index_name, k=5):
    """Search within a specific index"""
    indices_data = load_all_indices()
    results = []

    if not indices_data:
        return []

    # Find the specific index
    target_index = None
    for idx_data in indices_data:
        if idx_data['id'] == index_name:
            target_index = idx_data
            break

    if not target_index:
        print(f"Index '{index_name}' not found")
        return []

    # Embed the query
    query_vector = embedder.encode([query])[0]
    query_embedding = np.array([query_vector]).astype("float32")
    faiss.normalize_L2(query_embedding)

    try:
        index = target_index['index']
        metadata = target_index['metadata']

        # Search this index
        distances, indices = index.search(query_embedding, min(k, index.ntotal))

        for rank, idx in enumerate(indices[0]):
            if idx < len(metadata) and idx >= 0:
                meta = metadata[idx]
                result = {
                    'score': float(distances[0][rank]),
                    'text': meta.get('chunk_text', ''),
                    'source_type': meta.get('source_type', target_index['type']),
                    'url': meta.get('url', ''),
                    'title': meta.get('title', 'Unknown'),
                    'date': meta.get('record_date', ''),
                    'category': meta.get('category', ''),
                    'vector_id': meta.get('vector_id', ''),
                    'index_name': index_name
                }
                results.append(result)
    except Exception as e:
        print(f"Error searching index {index_name}: {e}")

    # Sort by score (higher is better for cosine similarity)
    results.sort(key=lambda x: x['score'], reverse=True)
    return results

def generate_deepseek_response(query, search_results):
    """Generate enhanced response using DeepSeek API"""
    if not deepseek_client:
        return None

    # Prepare context from search results
    context_parts = []
    for i, result in enumerate(search_results[:3]):  # Use top 3 results
        context_parts.append(f"Source {i+1} ({result.get('source_type', 'unknown')}):\n{result.get('text', '')}")

    context = "\n\n".join(context_parts)

    # Create prompt for DeepSeek
    prompt = f"""Based on the following search results, provide a comprehensive and accurate answer to the user's question.

User Question: {query}

Search Results:
{context}

Please provide a detailed response that:
1. Directly answers the user's question
2. References relevant information from the search results
3. Maintains accuracy and avoids speculation
4. Is well-structured and easy to understand

Response:"""

    try:
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a helpful AI assistant that provides accurate answers based on provided search results."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.7
        )

        return {
            'content': response.choices[0].message.content,
            'model': 'deepseek-chat',
            'sources_used': len(search_results)
        }
    except Exception as e:
        print(f"Error generating DeepSeek response: {e}")
        return None

def search_all_indices(query, k=5):
    """Search across all loaded indices"""
    indices_data = load_all_indices()
    all_results = []
    
    if not indices_data:
        return []
    
    # Embed the query
    query_vector = embedder.encode([query])[0]
    query_embedding = np.array([query_vector]).astype("float32")
    faiss.normalize_L2(query_embedding)
    
    for idx_data in indices_data:
        try:
            index = idx_data['index']
            metadata = idx_data['metadata']
            
            # Search this index
            distances, indices = index.search(query_embedding, min(k, index.ntotal))
            
            for rank, idx in enumerate(indices[0]):
                if idx < len(metadata) and idx >= 0:
                    meta = metadata[idx]
                    result = {
                        'score': float(distances[0][rank]),
                        'text': meta.get('chunk_text', ''),
                        'source_type': meta.get('source_type', idx_data['type']),
                        'url': meta.get('url', ''),
                        'title': meta.get('title', 'Unknown'),
                        'date': meta.get('record_date', ''),
                        'category': meta.get('category', ''),
                        'vector_id': meta.get('vector_id', '')
                    }
                    all_results.append(result)
        except Exception as e:
            print(f"Error searching index {idx_data['id']}: {e}")
    
    # Sort by score (higher is better for cosine similarity)
    all_results.sort(key=lambda x: x['score'], reverse=True)
    return all_results[:k]

@app.route('/')
def index():
    """Serve the main HTML page"""
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return """
        <h1>FAISS Content Indexer</h1>
        <p>Error: index.html file not found. Please make sure the HTML file is in the same directory as this script.</p>
        """

@app.route('/api/process_youtube', methods=['POST'])
def process_youtube():
    """Enhanced YouTube URL processing endpoint with PINE collection integration"""
    try:
        data = request.get_json()
        url = data.get('url', '').strip()
        index_name = data.get('index_name', 'default')
        client_email = data.get('client_email', '')

        if not url:
            return jsonify({'success': False, 'error': 'URL is required'})

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Process the YouTube URL with index_name
        success = process_youtube_url(url, index_name=index_name)

        if success:
            # Store in PINE collection if client email provided
            if client_email:
                try:
                    db_success, db_message, db_info = database.create_pine_entry(
                        api_key="all-MiniLM-L6-v2",
                        index_name=index_name,
                        client=client_email
                    )
                    if db_success:
                        print(f"✅ Stored YouTube processing in PINE collection: {db_message}")
                except Exception as e:
                    print(f"⚠️ Failed to store in PINE collection: {e}")

            return jsonify({
                'success': True,
                'message': 'YouTube video processed successfully',
                'upload_id': upload_id,
                'index_name': index_name
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to process YouTube video'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_article', methods=['POST'])
def process_article():
    """Enhanced Article URL processing endpoint with PINE collection integration"""
    try:
        data = request.get_json()
        url = data.get('url', '').strip()
        index_name = data.get('index_name', 'default')
        client_email = data.get('client_email', '')

        if not url:
            return jsonify({'success': False, 'error': 'URL is required'})

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Process the article URL with index_name
        success = process_article_url(url, index_name=index_name)

        if success:
            # Store in PINE collection if client email provided
            if client_email:
                try:
                    db_success, db_message, _ = database.create_pine_entry(
                        api_key="all-MiniLM-L6-v2",
                        index_name=index_name,
                        client=client_email
                    )
                    if db_success:
                        print(f"✅ Stored Article processing in PINE collection: {db_message}")
                except Exception as e:
                    print(f"⚠️ Failed to store in PINE collection: {e}")

            return jsonify({
                'success': True,
                'message': 'Article processed successfully',
                'upload_id': upload_id,
                'index_name': index_name
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to process article'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_document', methods=['POST'])
def process_document():
    """Enhanced Document processing endpoint with PINE collection integration"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in SUPPORTED_EXTENSIONS:
            return jsonify({
                'success': False,
                'error': f'Unsupported file type: {file_ext}. Supported: {list(SUPPORTED_EXTENSIONS.keys())}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Save uploaded file temporarily
        temp_filename = f"temp_{upload_id}_{file.filename}"
        temp_path = os.path.join(DOCUMENTS_DIR, temp_filename)

        # Ensure documents directory exists
        os.makedirs(DOCUMENTS_DIR, exist_ok=True)

        # Save the file
        file.save(temp_path)

        try:
            # Process the document using imported function with index_name
            success = process_document_file(temp_path, original_filename=file.filename, index_name=index_name)

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message, _ = database.create_pine_entry(
                            api_key="all-MiniLM-L6-v2",
                            index_name=index_name,
                            client=client_email
                        )
                        if db_success:
                            print(f"✅ Stored Document processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'Document processed and indexed successfully',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process document'})

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {temp_path}: {e}")

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_pdf', methods=['POST'])
def process_pdf():
    """Enhanced PDF file processing endpoint with PINE collection integration"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No PDF file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No PDF file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext != '.pdf':
            return jsonify({
                'success': False,
                'error': f'Only PDF files are supported. Got: {file_ext}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Save uploaded file temporarily
        temp_filename = f"temp_{upload_id}_{file.filename}"
        temp_path = os.path.join(PDFS_DIR, temp_filename)

        # Ensure PDFs directory exists
        os.makedirs(PDFS_DIR, exist_ok=True)

        # Save the file
        file.save(temp_path)

        try:
            # Process the PDF using imported function with index_name
            success = process_pdf_file(temp_path, original_filename=file.filename, index_name=index_name)

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message, _ = database.create_pine_entry(
                            api_key="all-MiniLM-L6-v2",
                            index_name=index_name,
                            client=client_email
                        )
                        if db_success:
                            print(f"✅ Stored PDF processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'PDF processed and indexed successfully',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process PDF'})

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {temp_path}: {e}")

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_audio', methods=['POST'])
def process_audio():
    """Enhanced Audio file processing endpoint with PINE collection integration"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No audio file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No audio file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        supported_audio_formats = {'.mp3', '.wav', '.m4a', '.flac', '.ogg'}
        if file_ext not in supported_audio_formats:
            return jsonify({
                'success': False,
                'error': f'Unsupported audio format: {file_ext}. Supported: {list(supported_audio_formats)}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Save uploaded file temporarily
        temp_filename = f"temp_{upload_id}_{file.filename}"
        temp_path = os.path.join(AUDIO_DIR, temp_filename)

        # Ensure audio directory exists
        os.makedirs(AUDIO_DIR, exist_ok=True)

        # Save the file
        file.save(temp_path)

        try:
            # Process the audio file using imported function with index_name
            success = process_audio_file(temp_path, original_filename=file.filename, index_name=index_name)

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message, _ = database.create_pine_entry(
                            api_key="all-MiniLM-L6-v2",
                            index_name=index_name,
                            client=client_email
                        )
                        if db_success:
                            print(f"✅ Stored Audio processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'Audio file processed and indexed successfully',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process audio file'})

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {temp_path}: {e}")

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/search', methods=['POST'])
def search():
    """Enhanced search endpoint with DeepSeek integration"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        k = data.get('k', 5)
        index_name = data.get('index_name', None)
        use_deepseek = data.get('use_deepseek', False)

        if not query:
            return jsonify({'results': [], 'error': 'Query is required'})

        # Search across all indices or specific index
        if index_name:
            results = search_specific_index(query, index_name, k)
        else:
            results = search_all_indices(query, k)

        response_data = {
            'results': results,
            'query': query,
            'index_name': index_name
        }

        # Enhance response with DeepSeek if requested and available
        if use_deepseek and deepseek_client and results:
            try:
                enhanced_response = generate_deepseek_response(query, results)
                response_data['enhanced_response'] = enhanced_response
            except Exception as e:
                print(f"⚠️ DeepSeek enhancement failed: {e}")
                response_data['deepseek_error'] = str(e)

        return jsonify(response_data)

    except Exception as e:
        return jsonify({'results': [], 'error': str(e)})

@app.route('/supported_formats')
def supported_formats():
    """Get supported document formats"""
    return jsonify({
        'supported_extensions': SUPPORTED_EXTENSIONS,
        'note': 'Document upload and processing available'
    })

@app.route('/status')
def status():
    """Get system status"""
    try:
        indices_data = load_all_indices()

        youtube_count = sum(1 for idx in indices_data if idx['type'] == 'youtube')
        article_count = sum(1 for idx in indices_data if idx['type'] == 'article')
        document_count = sum(1 for idx in indices_data if idx['type'] == 'document')
        pdf_count = sum(1 for idx in indices_data if idx['type'] == 'pdf')
        audio_count = sum(1 for idx in indices_data if idx['type'] == 'audio')
        total_vectors = sum(idx['index'].ntotal for idx in indices_data)

        return jsonify({
            'youtube_indices': youtube_count,
            'article_indices': article_count,
            'document_indices': document_count,
            'pdf_indices': pdf_count,
            'audio_indices': audio_count,
            'total_vectors': total_vectors,
            'status': 'healthy'
        })

    except Exception as e:
        return jsonify({'status': 'error', 'error': str(e)})

if __name__ == '__main__':
    # Ensure directories exist
    os.makedirs(YOUTUBE_DIR, exist_ok=True)
    os.makedirs(ARTICLES_DIR, exist_ok=True)
    os.makedirs(DOCUMENTS_DIR, exist_ok=True)
    os.makedirs(PDFS_DIR, exist_ok=True)
    os.makedirs(AUDIO_DIR, exist_ok=True)

    print("🚀 Starting FAISS Content Indexer Server...")
    print(f"📁 YouTube directory: {YOUTUBE_DIR}")
    print(f"📁 Articles directory: {ARTICLES_DIR}")
    print(f"📁 Documents directory: {DOCUMENTS_DIR}")
    print(f"📁 PDFs directory: {PDFS_DIR}")
    print(f"📁 Audio directory: {AUDIO_DIR}")
    print(f"📄 Supported formats: {list(SUPPORTED_EXTENSIONS.keys())}")
    print("📤 Document upload functionality available")
    print("📄 PDF upload functionality available")
    print("🎵 Audio upload functionality available (MP3, WAV, M4A, FLAC, OGG)")

    # Load and display current status
    try:
        indices_data = load_all_indices()
        youtube_count = sum(1 for idx in indices_data if idx['type'] == 'youtube')
        article_count = sum(1 for idx in indices_data if idx['type'] == 'article')
        document_count = sum(1 for idx in indices_data if idx['type'] == 'document')
        pdf_count = sum(1 for idx in indices_data if idx['type'] == 'pdf')
        audio_count = sum(1 for idx in indices_data if idx['type'] == 'audio')
        print(f"📊 Loaded {youtube_count} YouTube, {article_count} article, {document_count} document, {pdf_count} PDF, and {audio_count} audio indices")
    except Exception as e:
        print(f"⚠️ Error loading indices: {e}")

    app.run(debug=True, host='0.0.0.0', port=5000)
