import React, { useState } from 'react';
import { CacheService } from './services/CacheService';
import { TranslationService } from './services/TranslationService';

interface TranslationDemoProps {
  className?: string;
}

export const TranslationDemo: React.FC<TranslationDemoProps> = ({ className = '' }) => {
  const [testQuery, setTestQuery] = useState('What is artificial intelligence?');
  const [targetLanguage, setTargetLanguage] = useState('ta');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'ta', name: 'Tamil' },
    { code: 'hi', name: 'Hindi' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
    { code: 'de', name: 'German' },
    { code: 'zh', name: 'Chinese' },
    { code: 'ja', name: 'Japanese' },
    { code: 'ko', name: 'Korean' },
    { code: 'ar', name: 'Arabic' }
  ];

  const testQueries = [
    'What is artificial intelligence?',
    'How does machine learning work?',
    'What are the benefits of renewable energy?',
    'Explain quantum computing',
    'What is climate change?'
  ];

  const handleTestTranslation = async () => {
    setIsLoading(true);
    try {
      // Test language detection
      const detectedLanguage = TranslationService.detectLanguage(testQuery);
      
      // Create a mock response for testing
      const mockResponse = {
        ai_response: 'Artificial intelligence (AI) is a branch of computer science that aims to create intelligent machines that can perform tasks that typically require human intelligence, such as learning, reasoning, problem-solving, perception, and language understanding.',
        related_questions: [
          'What are the different types of AI?',
          'How is AI used in everyday life?',
          'What are the ethical concerns about AI?',
          'What is the difference between AI and machine learning?',
          'What are the future prospects of AI?'
        ],
        sentence_analysis: [
          {
            sentence: 'AI is transforming various industries.',
            url: 'https://example.com/ai-industries',
            summary: 'Article about AI impact on industries'
          }
        ]
      };

      // Test translation
      const translatedResponse = await TranslationService.translateResponse(mockResponse, targetLanguage);
      
      // Test caching with language support
      const cacheContext = 'demo|<EMAIL>|translation-test';
      
      // Check if response is already cached
      const cachedResponse = CacheService.getCachedResponse(testQuery, cacheContext, targetLanguage);
      
      let cacheHit = false;
      if (cachedResponse) {
        cacheHit = true;
        console.log('Cache hit! Using cached response.');
        // Apply artificial delay for cached responses
        await CacheService.applyCachedResponseDelay();
      } else {
        console.log('Cache miss. Caching new response.');
        // Cache the translated response
        CacheService.setCachedResponse(testQuery, translatedResponse, cacheContext, targetLanguage);
      }

      // Get cache statistics
      const stats = CacheService.getCacheStats();
      const translationStats = TranslationService.getCacheStats();

      setResults({
        originalQuery: testQuery,
        detectedLanguage,
        targetLanguage,
        originalResponse: mockResponse,
        translatedResponse,
        cacheHit,
        translationApplied: translatedResponse.translation_applied
      });

      setCacheStats({
        cache: stats,
        translation: translationStats
      });

    } catch (error) {
      console.error('Translation test failed:', error);
      setResults({
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearCache = () => {
    CacheService.clearCache();
    TranslationService.clearCache();
    setCacheStats(null);
    console.log('All caches cleared');
  };

  const handleTestCachedResponse = async () => {
    setIsLoading(true);
    try {
      const cacheContext = 'demo|<EMAIL>|translation-test';
      
      // Try to get cached response with delay
      const cachedResponse = await CacheService.getCachedResponseWithDelay(
        testQuery, 
        cacheContext, 
        targetLanguage
      );

      if (cachedResponse) {
        setResults({
          ...results,
          cachedResponseTest: {
            success: true,
            message: 'Successfully retrieved cached response with 2-second delay',
            response: cachedResponse
          }
        });
      } else {
        setResults({
          ...results,
          cachedResponseTest: {
            success: false,
            message: 'No cached response found for this query and language combination'
          }
        });
      }
    } catch (error) {
      console.error('Cached response test failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`p-6 bg-white rounded-lg shadow-lg ${className}`}>
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        Translation & Caching Demo
      </h2>

      {/* Test Controls */}
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Test Query
          </label>
          <select
            value={testQuery}
            onChange={(e) => setTestQuery(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {testQueries.map((query) => (
              <option key={query} value={query}>
                {query}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Language
          </label>
          <select
            value={targetLanguage}
            onChange={(e) => setTargetLanguage(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {languages.map((lang) => (
              <option key={lang.code} value={lang.code}>
                {lang.name} ({lang.code})
              </option>
            ))}
          </select>
        </div>

        <div className="flex gap-3">
          <button
            onClick={handleTestTranslation}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Testing...' : 'Test Translation & Caching'}
          </button>

          <button
            onClick={handleTestCachedResponse}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Test Cached Response (2s delay)
          </button>

          <button
            onClick={handleClearCache}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Clear Cache
          </button>
        </div>
      </div>

      {/* Results Display */}
      {results && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Test Results</h3>
          
          {results.error ? (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800">Error: {results.error}</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-gray-50 rounded-md">
                  <h4 className="font-medium text-gray-800 mb-2">Original Response</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    <strong>Query:</strong> {results.originalQuery}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <strong>Detected Language:</strong> {results.detectedLanguage}
                  </p>
                  <p className="text-sm text-gray-600">
                    {results.originalResponse?.ai_response?.substring(0, 150)}...
                  </p>
                </div>

                <div className="p-4 bg-blue-50 rounded-md">
                  <h4 className="font-medium text-gray-800 mb-2">Translated Response</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    <strong>Target Language:</strong> {results.targetLanguage}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <strong>Translation Applied:</strong> {results.translationApplied ? 'Yes' : 'No'}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <strong>Cache Hit:</strong> {results.cacheHit ? 'Yes' : 'No'}
                  </p>
                  <p className="text-sm text-gray-600">
                    {results.translatedResponse?.ai_response?.substring(0, 150)}...
                  </p>
                </div>
              </div>

              {results.cachedResponseTest && (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <h4 className="font-medium text-gray-800 mb-2">Cached Response Test</h4>
                  <p className={`text-sm ${results.cachedResponseTest.success ? 'text-green-600' : 'text-red-600'}`}>
                    {results.cachedResponseTest.message}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Cache Statistics */}
      {cacheStats && (
        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Cache Statistics</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Response Cache</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>Total Queries: {cacheStats.cache.totalQueries}</li>
                <li>Cache Hits: {cacheStats.cache.cacheHits}</li>
                <li>Cache Misses: {cacheStats.cache.cacheMisses}</li>
                <li>Hit Rate: {cacheStats.cache.hitRate}%</li>
                <li>Cache Size: {cacheStats.cache.cacheSize} entries</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Translation Cache</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>Cache Size: {cacheStats.translation.size} entries</li>
                <li>Sample Keys: {cacheStats.translation.keys.slice(0, 3).join(', ')}</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TranslationDemo;
