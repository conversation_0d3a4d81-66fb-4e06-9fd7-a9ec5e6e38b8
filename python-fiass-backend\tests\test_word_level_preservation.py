#!/usr/bin/env python3
"""
Test script to verify word-level capital letter preservation
Only continuous capital letter words should be preserved, rest should be translated
"""

import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.translation_service import TranslationService

def test_word_level_preservation():
    """Test that only continuous capital letter words are preserved, rest are translated"""
    
    print("🧪 Testing Word-Level Capital Letter Preservation")
    print("=" * 60)
    
    # Initialize translation service
    translation_service = TranslationService()
    
    # Test cases for word-level preservation
    test_cases = [
        {
            "text": "The iPhone uses IOS operating system",
            "source_lang": "en",
            "target_lang": "ta",
            "description": "Mixed: iPhone (single caps) + IOS (continuous caps)",
            "expected_behavior": "Should translate iPhone but preserve IOS",
            "continuous_caps_words": ["IOS"],
            "should_skip_entire_sentence": False  # New implementation does word-level preservation
        },
        {
            "text": "Microsoft uses API for cloud services",
            "source_lang": "en",
            "target_lang": "te",
            "description": "Mixed: Microsoft (single caps) + API (continuous caps)",
            "expected_behavior": "Should translate Microsoft but preserve API",
            "continuous_caps_words": ["API"],
            "should_skip_entire_sentence": False  # New implementation does word-level preservation
        },
        {
            "text": "The REST API documentation is helpful",
            "source_lang": "en",
            "target_lang": "kn",
            "description": "Mixed: REST, API (continuous caps) + other words",
            "expected_behavior": "Should preserve REST, API but translate other words",
            "continuous_caps_words": ["REST", "API"],
            "should_skip_entire_sentence": False  # New implementation does word-level preservation
        },
        {
            "text": "Python programming language is popular",
            "source_lang": "en",
            "target_lang": "hi",
            "description": "Only single capitals (Python)",
            "expected_behavior": "Should translate everything including Python",
            "continuous_caps_words": [],
            "should_skip_entire_sentence": False
        },
        {
            "text": "what is machine learning",
            "source_lang": "en",
            "target_lang": "ta",
            "description": "All lowercase",
            "expected_behavior": "Should translate everything",
            "continuous_caps_words": [],
            "should_skip_entire_sentence": False
        }
    ]
    
    print(f"Testing {len(test_cases)} word-level preservation cases...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['description']}")
        print(f"Original: '{test_case['text']}'")
        print(f"Language: {test_case['source_lang']} -> {test_case['target_lang']}")
        print(f"Expected: {test_case['expected_behavior']}")
        print(f"Continuous caps words: {test_case['continuous_caps_words']}")
        
        # Test continuous capital letter detection
        has_continuous_capitals = translation_service._has_continuous_capital_letters(test_case['text'])
        should_skip = translation_service._should_skip_translation(
            test_case['text'], 
            test_case['source_lang'], 
            test_case['target_lang']
        )
        
        print(f"Has continuous capitals: {has_continuous_capitals}")
        print(f"Should skip entire sentence: {should_skip}")
        
        # Test actual translation
        try:
            result = translation_service.translate_text(
                test_case['text'], 
                test_case['target_lang'], 
                test_case['source_lang']
            )
            
            translated_text = result['translated_text']
            provider = result.get('translation_provider', 'unknown')
            
            print(f"Translated: '{translated_text}'")
            print(f"Provider: {provider}")
            
            # Analyze the result
            if provider == 'skipped_capital_letters':
                print("🔒 CURRENT BEHAVIOR: Entire sentence skipped due to continuous capitals")
                if test_case['should_skip_entire_sentence']:
                    print("✅ MATCHES EXPECTED BEHAVIOR")
                else:
                    print("❌ UNEXPECTED - Should do word-level preservation")
            else:
                print("🌐 CURRENT BEHAVIOR: Word-level preservation applied")
                if not test_case['should_skip_entire_sentence']:
                    print("✅ MATCHES EXPECTED BEHAVIOR - Word-level preservation working!")
                else:
                    print("❌ UNEXPECTED BEHAVIOR")
            
            # Check if continuous capital words are preserved
            if test_case['continuous_caps_words']:
                preserved_count = sum(1 for word in test_case['continuous_caps_words'] if word in translated_text)
                print(f"Continuous caps preserved: {preserved_count}/{len(test_case['continuous_caps_words'])}")
                
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
        
        print("-" * 60)
        print()

def test_ideal_word_level_behavior():
    """Demonstrate what ideal word-level preservation would look like"""
    
    print("💡 Ideal Word-Level Preservation Behavior")
    print("=" * 60)
    
    ideal_cases = [
        {
            "input": "The iPhone uses IOS operating system",
            "ideal_output_ta": "ஐபோன் IOS இயக்க முறைமையைப் பயன்படுத்துகிறது",
            "explanation": "iPhone translated, IOS preserved"
        },
        {
            "input": "Microsoft uses API for cloud services", 
            "ideal_output_te": "మైక్రోసాఫ్ట్ క్లౌడ్ సేవల కోసం API ఉపయోగిస్తుంది",
            "explanation": "Microsoft translated, API preserved"
        },
        {
            "input": "The REST API documentation is helpful",
            "ideal_output_kn": "REST API ದಾಖಲೀಕರಣವು ಸಹಾಯಕವಾಗಿದೆ", 
            "explanation": "REST and API preserved, other words translated"
        }
    ]
    
    print("These examples show what word-level preservation should achieve:")
    print()
    
    for i, case in enumerate(ideal_cases, 1):
        print(f"Example {i}:")
        print(f"Input:  '{case['input']}'")
        
        # Get the ideal output
        if 'ideal_output_ta' in case:
            ideal_output = case['ideal_output_ta']
        elif 'ideal_output_te' in case:
            ideal_output = case['ideal_output_te']
        elif 'ideal_output_kn' in case:
            ideal_output = case['ideal_output_kn']
        else:
            ideal_output = "No ideal output defined"
            
        print(f"Ideal:  '{ideal_output}'")
        print(f"Logic:  {case['explanation']}")
        print()

def analyze_current_vs_ideal():
    """Analyze current implementation vs ideal word-level preservation"""
    
    print("📊 Current Implementation vs Ideal Behavior Analysis")
    print("=" * 60)
    
    print("CURRENT IMPLEMENTATION:")
    print("✅ Correctly detects continuous capital letters (API, REST, IOS, etc.)")
    print("✅ Skips translation for entire sentences containing continuous capitals")
    print("✅ Translates sentences with only single capitals (Microsoft, iPhone)")
    print("✅ Translates sentences with no capitals")
    print()
    
    print("LIMITATION:")
    print("❌ Skips ENTIRE sentence if ANY continuous capital word is found")
    print("❌ Cannot do word-level preservation within a sentence")
    print()
    
    print("IDEAL BEHAVIOR (Word-Level Preservation):")
    print("💡 Should preserve only continuous capital words (API, REST, IOS)")
    print("💡 Should translate all other words in the same sentence")
    print("💡 Should maintain sentence structure and word order")
    print()
    
    print("TO ACHIEVE WORD-LEVEL PRESERVATION:")
    print("🔧 Need to implement word-by-word processing")
    print("🔧 Need to replace continuous capital words with placeholders")
    print("🔧 Need to translate the modified sentence")
    print("🔧 Need to restore continuous capital words in final result")

if __name__ == "__main__":
    test_word_level_preservation()
    print()
    test_ideal_word_level_behavior()
    print()
    analyze_current_vs_ideal()
    
    print("🏁 Word-level preservation testing completed!")