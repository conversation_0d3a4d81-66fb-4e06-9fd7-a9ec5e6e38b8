/**
 * Google Translate Service using @vitalets/google-translate-api
 * This service handles translation for the improved flow:
 * Query language detection → FAISS search → Response translation
 */

const { translate } = require('@vitalets/google-translate-api');

class GoogleTranslateService {
    constructor() {
        this.supportedLanguages = {
            'kannada': 'kn',
            'tamil': 'ta', 
            'telugu': 'te',
            'oriya': 'or',
            'odia': 'or',
            'english': 'en',
            'hindi': 'hi'
        };
        
        this.languageNames = {
            'kn': 'Kannada',
            'ta': 'Tamil',
            'te': 'Telugu', 
            'or': 'Oriya',
            'en': 'English',
            'hi': 'Hindi'
        };
    }

    /**
     * Detect language of input text
     * @param {string} text - Text to detect language for
     * @returns {Promise<{language: string, confidence: number}>}
     */
    async detectLanguage(text) {
        try {
            if (!text || !text.trim()) {
                return { language: 'en', confidence: 0 };
            }

            // Use Unicode ranges for better detection of Indian languages
            if (this._isKannadaText(text)) {
                return { language: 'kn', confidence: 0.9 };
            }
            if (this._isTamilText(text)) {
                return { language: 'ta', confidence: 0.9 };
            }
            if (this._isTeluguText(text)) {
                return { language: 'te', confidence: 0.9 };
            }
            if (this._isOriyaText(text)) {
                return { language: 'or', confidence: 0.9 };
            }

            // Fallback to Google Translate detection for other languages
            try {
                const result = await translate(text, { to: 'en' });
                return { 
                    language: result.from.language.iso || 'en', 
                    confidence: 0.7 
                };
            } catch (error) {
                console.warn('Google Translate detection failed:', error.message);
                return { language: 'en', confidence: 0.5 };
            }
        } catch (error) {
            console.error('Language detection error:', error);
            return { language: 'en', confidence: 0 };
        }
    }

    /**
     * Translate text from source language to target language
     * @param {string} text - Text to translate
     * @param {string} targetLang - Target language code
     * @param {string} sourceLang - Source language code (optional)
     * @returns {Promise<{success: boolean, translatedText: string, originalText: string, error?: string}>}
     */
    async translateText(text, targetLang, sourceLang = null) {
        try {
            if (!text || !text.trim()) {
                return {
                    success: false,
                    error: 'Empty text provided',
                    originalText: text,
                    translatedText: text
                };
            }

            // If source and target are the same, return original text
            if (sourceLang && sourceLang === targetLang) {
                return {
                    success: true,
                    originalText: text,
                    translatedText: text,
                    skipped: true
                };
            }

            console.log(`🌐 Translating text from ${sourceLang || 'auto'} to ${targetLang}`);
            console.log(`📝 Original text: ${text.substring(0, 100)}...`);

            // Preserve capital words and special characters
            const { modifiedText, preservedWords } = this._preserveSpecialWords(text);

            // Perform translation
            const options = { to: targetLang };
            if (sourceLang) {
                options.from = sourceLang;
            }

            const result = await translate(modifiedText, options);
            let translatedText = result.text;

            // Restore preserved words
            translatedText = this._restoreSpecialWords(translatedText, preservedWords);

            console.log(`✅ Translation successful: ${translatedText.substring(0, 100)}...`);

            return {
                success: true,
                originalText: text,
                translatedText: translatedText,
                detectedSourceLanguage: result.from?.language?.iso || sourceLang,
                targetLanguage: targetLang
            };

        } catch (error) {
            console.error('Translation error:', error);
            return {
                success: false,
                error: error.message,
                originalText: text,
                translatedText: text // Return original text as fallback
            };
        }
    }

    /**
     * Translate entire financial query response
     * @param {Object} responseData - Response data from financial_query endpoint
     * @param {string} targetLanguage - Target language for translation
     * @returns {Promise<Object>} - Translated response data
     */
    async translateFinancialResponse(responseData, targetLanguage) {
        try {
            if (!responseData || typeof responseData !== 'object') {
                throw new Error('Invalid response data provided');
            }

            console.log(`🔄 Translating financial response to ${targetLanguage}`);
            
            const translatedResponse = { ...responseData };
            
            // Translate main AI response
            if (responseData.ai_response) {
                const aiTranslation = await this.translateText(
                    responseData.ai_response, 
                    targetLanguage, 
                    'en'
                );
                if (aiTranslation.success) {
                    translatedResponse.ai_response = aiTranslation.translatedText;
                    console.log('✅ AI response translated successfully');
                } else {
                    console.warn('⚠️ AI response translation failed:', aiTranslation.error);
                }
            }

            // Translate related questions
            if (responseData.related_questions && Array.isArray(responseData.related_questions)) {
                const translatedQuestions = [];
                for (const question of responseData.related_questions) {
                    if (question && question.trim()) {
                        const questionTranslation = await this.translateText(
                            question, 
                            targetLanguage, 
                            'en'
                        );
                        translatedQuestions.push(
                            questionTranslation.success ? 
                            questionTranslation.translatedText : 
                            question
                        );
                    } else {
                        translatedQuestions.push(question);
                    }
                }
                translatedResponse.related_questions = translatedQuestions;
                console.log(`✅ ${translatedQuestions.length} related questions translated`);
            }

            // Translate other text fields if present
            const fieldsToTranslate = ['summary', 'context', 'explanation'];
            for (const field of fieldsToTranslate) {
                if (responseData[field] && typeof responseData[field] === 'string') {
                    const fieldTranslation = await this.translateText(
                        responseData[field], 
                        targetLanguage, 
                        'en'
                    );
                    if (fieldTranslation.success) {
                        translatedResponse[field] = fieldTranslation.translatedText;
                        console.log(`✅ ${field} translated successfully`);
                    }
                }
            }

            // Add translation metadata
            translatedResponse.translation_metadata = {
                translated_by: '@vitalets/google-translate-api',
                target_language: targetLanguage,
                target_language_name: this.languageNames[targetLanguage] || targetLanguage,
                translation_timestamp: new Date().toISOString(),
                fields_translated: ['ai_response', 'related_questions', ...fieldsToTranslate].filter(
                    field => responseData[field]
                )
            };

            console.log('🎉 Financial response translation completed successfully');
            return translatedResponse;

        } catch (error) {
            console.error('Financial response translation error:', error);
            // Return original response with error metadata
            return {
                ...responseData,
                translation_error: {
                    message: error.message,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }

    /**
     * Check if text contains Kannada characters
     */
    _isKannadaText(text) {
        const kannadaPattern = /[\u0C80-\u0CFF]/;
        return kannadaPattern.test(text);
    }

    /**
     * Check if text contains Tamil characters
     */
    _isTamilText(text) {
        const tamilPattern = /[\u0B80-\u0BFF]/;
        return tamilPattern.test(text);
    }

    /**
     * Check if text contains Telugu characters
     */
    _isTeluguText(text) {
        const teluguPattern = /[\u0C00-\u0C7F]/;
        return teluguPattern.test(text);
    }

    /**
     * Check if text contains Oriya characters
     */
    _isOriyaText(text) {
        const oriyaPattern = /[\u0B00-\u0B7F]/;
        return oriyaPattern.test(text);
    }

    /**
     * Preserve special words (capital letters, technical terms) before translation
     */
    _preserveSpecialWords(text) {
        const specialWordPattern = /\b[A-Z]{2,}\b|\b[A-Z][a-z]*[A-Z][A-Za-z]*\b/g;
        const preservedWords = [];
        let modifiedText = text;
        let match;
        let index = 0;

        while ((match = specialWordPattern.exec(text)) !== null) {
            const word = match[0];
            const placeholder = `SPECIALWORD${index}PLACEHOLDER`;
            preservedWords.push({ word, placeholder });
            modifiedText = modifiedText.replace(word, placeholder);
            index++;
        }

        return { modifiedText, preservedWords };
    }

    /**
     * Restore special words after translation
     */
    _restoreSpecialWords(text, preservedWords) {
        let restoredText = text;
        
        for (const { word, placeholder } of preservedWords) {
            // Try exact match first
            if (restoredText.includes(placeholder)) {
                restoredText = restoredText.replace(placeholder, word);
            } else {
                // Try case-insensitive match
                const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
                restoredText = restoredText.replace(regex, word);
            }
        }

        return restoredText;
    }

    /**
     * Get language name from code
     */
    getLanguageName(languageCode) {
        return this.languageNames[languageCode] || languageCode;
    }

    /**
     * Get language code from name
     */
    getLanguageCode(languageName) {
        const lowerName = languageName.toLowerCase();
        return this.supportedLanguages[lowerName] || languageName;
    }
}

module.exports = new GoogleTranslateService();