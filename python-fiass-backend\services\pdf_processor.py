import os
import json
import datetime
import faiss
import numpy as np
from dotenv import load_dotenv
from sentence_transformers import SentenceTransformer
import hashlib
from pathlib import Path
import asyncio
import concurrent.futures
from typing import Optional, Dict, Any, List, Tuple, Union
import tempfile
import threading
import io
import time

# Import language detection utilities
from language_utils import (
    detect_language_from_text,
    get_index_name_for_language,
    get_language_statistics
)

# PDF processing libraries with priority order
PDF_LIBRARIES = []
try:
    import pdfplumber
    PDF_LIBRARIES.append('pdfplumber')
    print("✅ pdfplumber available for PDF processing")
except ImportError:
    pass

try:
    import PyPDF2
    PDF_LIBRARIES.append('PyPDF2')
    print("✅ PyPDF2 available for PDF processing")
except ImportError:
    pass

try:
    import pymupdf as fitz  # PyMuPDF - fastest option
    PDF_LIBRARIES.append('pymupdf')
    print("✅ PyMuPDF available for PDF processing")
except ImportError:
    pass

PDF_AVAILABLE = len(PDF_LIBRARIES) > 0
if not PDF_AVAILABLE:
    print("❌ No PDF processing library available. Install pdfplumber, PyPDF2, or PyMuPDF")

# Load environment variables
load_dotenv()

# Configuration
FAISS_DATA_DIR = os.getenv("FAISS_DATA_DIR", "faiss_data")
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), FAISS_DATA_DIR)
PDFS_DIR = os.path.join(OUTPUT_DIR, "pdfs")
EMBED_MODEL = "all-MiniLM-L6-v2"
MAX_CHUNK_SIZE = int(os.getenv("MAX_CHUNK_SIZE", "1000"))
CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "100"))
MAX_WORKERS = int(os.getenv("MAX_WORKERS", "4"))

# Ensure directories exist
os.makedirs(PDFS_DIR, exist_ok=True)

# Initialize embedding model
embedder = SentenceTransformer(EMBED_MODEL)

# Thread-safe progress tracking
progress_tracker = {}
progress_lock = threading.Lock()

def get_pdf_id(file_path):
    """Generate a unique ID for the PDF based on file path and content"""
    file_name = os.path.basename(file_path)
    # Use file name and modification time for uniqueness
    try:
        mtime = os.path.getmtime(file_path)
        unique_string = f"{file_name}_{mtime}"
    except:
        unique_string = file_name
    return hashlib.md5(unique_string.encode()).hexdigest()[:12]

def update_progress(process_id: str, progress: int, message: str = ""):
    """Update progress for a processing task"""
    with progress_lock:
        progress_tracker[process_id] = {
            'progress': progress,
            'message': message,
            'timestamp': time.time()
        }

def get_progress(process_id: str) -> Dict[str, Any]:
    """Get progress for a processing task"""
    with progress_lock:
        return progress_tracker.get(process_id, {'progress': 0, 'message': 'Not found'})

def extract_text_with_pymupdf(file_path: str = None, file_content: bytes = None) -> Optional[str]:
    """Extract text using PyMuPDF (fastest option)"""
    try:
        import pymupdf as fitz

        if file_content:
            doc = fitz.open(stream=file_content, filetype="pdf")
        else:
            doc = fitz.open(file_path)

        text = ""
        total_pages = len(doc)
        print(f"📄 Processing PDF with {total_pages} pages using PyMuPDF...")

        for page_num in range(total_pages):
            page = doc[page_num]
            page_text = page.get_text()
            if page_text.strip():
                text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
            print(f"📖 Processed page {page_num + 1}/{total_pages}")

        doc.close()
        return text.strip()
    except Exception as e:
        print(f"❌ PyMuPDF extraction failed: {e}")
        return None

def extract_text_with_pdfplumber(file_path: str = None, file_content: bytes = None) -> Optional[str]:
    """Extract text using pdfplumber (good quality)"""
    try:
        import pdfplumber

        if file_content:
            pdf_file = io.BytesIO(file_content)
            with pdfplumber.open(pdf_file) as pdf:
                text = ""
                total_pages = len(pdf.pages)
                print(f"📄 Processing PDF with {total_pages} pages using pdfplumber...")

                for page_num, page in enumerate(pdf.pages, 1):
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\n--- Page {page_num} ---\n{page_text}\n"
                    print(f"📖 Processed page {page_num}/{total_pages}")

                return text.strip()
        else:
            with pdfplumber.open(file_path) as pdf:
                text = ""
                total_pages = len(pdf.pages)
                print(f"📄 Processing PDF with {total_pages} pages using pdfplumber...")

                for page_num, page in enumerate(pdf.pages, 1):
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\n--- Page {page_num} ---\n{page_text}\n"
                    print(f"📖 Processed page {page_num}/{total_pages}")

                return text.strip()
    except Exception as e:
        print(f"❌ pdfplumber extraction failed: {e}")
        return None

def extract_text_with_pypdf2(file_path: str = None, file_content: bytes = None) -> Optional[str]:
    """Extract text using PyPDF2 (fallback option)"""
    try:
        import PyPDF2

        if file_content:
            pdf_file = io.BytesIO(file_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
        else:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

        text = ""
        total_pages = len(pdf_reader.pages)
        print(f"📄 Processing PDF with {total_pages} pages using PyPDF2...")

        for page_num, page in enumerate(pdf_reader.pages, 1):
            page_text = page.extract_text()
            if page_text:
                text += f"\n--- Page {page_num} ---\n{page_text}\n"
            print(f"📖 Processed page {page_num}/{total_pages}")

        return text.strip()
    except Exception as e:
        print(f"❌ PyPDF2 extraction failed: {e}")
        return None

def extract_text_from_pdf(file_path: str = None, file_content: bytes = None, process_id: str = None) -> Optional[str]:
    """
    Extract text from PDF using the best available library.
    Tries libraries in order of performance: PyMuPDF > pdfplumber > PyPDF2
    """
    if not PDF_AVAILABLE:
        print("❌ No PDF processing library available")
        return None

    if not file_path and not file_content:
        print("❌ Either file_path or file_content must be provided")
        return None

    if process_id:
        update_progress(process_id, 10, "Starting PDF text extraction...")

    # Try libraries in order of preference
    extraction_methods = []

    if 'pymupdf' in PDF_LIBRARIES:
        extraction_methods.append(('PyMuPDF', extract_text_with_pymupdf))
    if 'pdfplumber' in PDF_LIBRARIES:
        extraction_methods.append(('pdfplumber', extract_text_with_pdfplumber))
    if 'PyPDF2' in PDF_LIBRARIES:
        extraction_methods.append(('PyPDF2', extract_text_with_pypdf2))

    for method_name, extraction_func in extraction_methods:
        try:
            print(f"🔄 Trying {method_name} for text extraction...")
            if process_id:
                update_progress(process_id, 30, f"Extracting text with {method_name}...")

            text = extraction_func(file_path, file_content)
            if text and len(text.strip()) > 0:
                print(f"✅ Successfully extracted text using {method_name}")
                if process_id:
                    update_progress(process_id, 60, f"Text extraction completed with {method_name}")
                return text
            else:
                print(f"⚠️ {method_name} returned empty text, trying next method...")
        except Exception as e:
            print(f"❌ {method_name} failed: {e}")
            continue

    # If all methods failed
    source = "file content" if file_content else f"file {file_path}"
    print(f"❌ All PDF extraction methods failed for {source}")
    if process_id:
        update_progress(process_id, 0, "PDF text extraction failed")
    return None

def chunk_text_advanced(text: str, chunk_size: int = None, overlap: int = None) -> List[str]:
    """
    Advanced text chunking with overlap and smart boundary detection.

    Args:
        text: Input text to chunk
        chunk_size: Maximum size of each chunk
        overlap: Number of characters to overlap between chunks

    Returns:
        List of text chunks
    """
    chunk_size = chunk_size or MAX_CHUNK_SIZE
    overlap = overlap or CHUNK_OVERLAP

    if len(text) <= chunk_size:
        return [text.strip()] if text.strip() else []

    chunks = []
    start = 0
    text_length = len(text)

    while start < text_length:
        # Calculate end position
        end = min(start + chunk_size, text_length)

        # If not at the end of text, try to find a good breaking point
        if end < text_length:
            # Look for sentence boundaries first
            sentence_end = text.rfind('.', start, end)
            if sentence_end > start + chunk_size // 2:
                end = sentence_end + 1
            else:
                # Look for paragraph boundaries
                para_end = text.rfind('\n\n', start, end)
                if para_end > start + chunk_size // 2:
                    end = para_end + 2
                else:
                    # Look for any whitespace
                    space_end = text.rfind(' ', start, end)
                    if space_end > start + chunk_size // 2:
                        end = space_end + 1

        # Extract chunk
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        # Move start position with overlap
        if end >= text_length:
            break
        start = max(start + 1, end - overlap)

    return chunks

def chunk_text(text: str, size: int = 500) -> List[str]:
    """Legacy chunking function for backward compatibility"""
    return chunk_text_advanced(text, chunk_size=size, overlap=CHUNK_OVERLAP)

async def process_chunks_async(chunks: List[str], embedder, process_id: str = None) -> Tuple[List[np.ndarray], List[Dict[str, Any]]]:
    """
    Process text chunks asynchronously to generate embeddings.

    Args:
        chunks: List of text chunks
        embedder: Embedding model
        process_id: Optional process ID for progress tracking

    Returns:
        Tuple of (vectors, metadata_list)
    """
    def embed_chunk(chunk_data):
        chunk_idx, chunk = chunk_data
        try:
            # Generate embedding
            vec_list = embedder.encode([chunk])
            vec = np.array(vec_list[0], dtype="float32")
            return chunk_idx, vec, None
        except Exception as e:
            return chunk_idx, None, str(e)

    # Process chunks in batches using ThreadPoolExecutor
    vectors = [None] * len(chunks)
    errors = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # Submit all tasks
        future_to_idx = {
            executor.submit(embed_chunk, (idx, chunk)): idx
            for idx, chunk in enumerate(chunks)
        }

        completed = 0
        for future in concurrent.futures.as_completed(future_to_idx):
            chunk_idx, vec, error = future.result()

            if error:
                errors.append(f"Chunk {chunk_idx}: {error}")
                print(f"❌ Error processing chunk {chunk_idx}: {error}")
            else:
                vectors[chunk_idx] = vec
                print(f"📝 Embedded chunk {chunk_idx + 1}/{len(chunks)}")

            completed += 1
            if process_id:
                progress = 60 + (completed / len(chunks)) * 30  # 60-90% range
                update_progress(process_id, int(progress), f"Processing chunk {completed}/{len(chunks)}")

    # Filter out failed embeddings
    valid_vectors = [v for v in vectors if v is not None]

    if errors:
        print(f"⚠️ {len(errors)} chunks failed to process:")
        for error in errors[:5]:  # Show first 5 errors
            print(f"   {error}")
        if len(errors) > 5:
            print(f"   ... and {len(errors) - 5} more errors")

    return valid_vectors, errors

def get_pdf_info(file_path=None, filename=None, file_size=None):
    """Get basic PDF file information"""
    try:
        if file_path:
            # Get info from file path
            file_stat = os.stat(file_path)
            return {
                "name": os.path.basename(file_path),
                "size": file_stat.st_size,
                "extension": ".pdf",
                "type": "PDF Document",
                "modified": datetime.datetime.fromtimestamp(file_stat.st_mtime).isoformat()
            }
        else:
            # Get info from provided parameters
            return {
                "name": filename or "uploaded.pdf",
                "size": file_size or 0,
                "extension": ".pdf",
                "type": "PDF Document",
                "modified": datetime.datetime.now().isoformat()
            }
    except Exception as e:
        source = f"file {file_path}" if file_path else "file content"
        print(f"Error getting PDF info for {source}: {e}")
        return None

async def process_pdf_file_async(
    file_path: str = None,
    file_content: bytes = None,
    original_filename: str = None,
    index_name: str = "default",
    process_id: str = None
) -> Dict[str, Any]:
    """
    Asynchronously process a PDF file and add to selected FAISS index.

    Args:
        file_path: Path to PDF file
        file_content: PDF file content as bytes
        original_filename: Original filename for uploaded files
        index_name: Target FAISS index name
        process_id: Optional process ID for progress tracking

    Returns:
        Dict with success status and details
    """
    try:
        if process_id:
            update_progress(process_id, 5, "Starting PDF processing...")

        if file_path:
            print(f"📄 Processing PDF from file: {file_path}")
        else:
            print(f"📄 Processing PDF from memory: {original_filename}")
        print(f"🎯 Target index: {index_name}")

        # Validate input
        if not file_path and not file_content:
            error_msg = "Either file_path or file_content must be provided"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

        if file_path:
            # Check if file exists and is PDF
            if not os.path.exists(file_path):
                error_msg = f"File not found: {file_path}"
                print(f"❌ {error_msg}")
                return {"success": False, "error": error_msg}

            if not file_path.lower().endswith('.pdf'):
                error_msg = f"Not a PDF file: {file_path}"
                print(f"❌ {error_msg}")
                return {"success": False, "error": error_msg}

            # Get file information
            file_info = get_pdf_info(file_path=file_path)
        else:
            # Get file information from content
            file_info = get_pdf_info(
                filename=original_filename or "uploaded.pdf",
                file_size=len(file_content) if file_content else 0
            )

        if not file_info:
            error_msg = "Could not get PDF file information"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

        print(f"📊 PDF info: {file_info['name']} ({file_info['size']} bytes)")

        # Extract text from PDF
        if process_id:
            update_progress(process_id, 10, "Extracting text from PDF...")

        if file_path:
            pdf_text = extract_text_from_pdf(file_path=file_path, process_id=process_id)
        else:
            pdf_text = extract_text_from_pdf(file_content=file_content, process_id=process_id)

        if not pdf_text:
            error_msg = "Could not extract text from PDF"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

        print(f"✅ Extracted text: {len(pdf_text)} characters")

        # Generate PDF ID
        if file_path:
            pdf_id = get_pdf_id(file_path)
        else:
            # Generate ID from filename and content
            unique_string = f"{original_filename or 'uploaded.pdf'}_{len(file_content) if file_content else 0}"
            pdf_id = hashlib.md5(unique_string.encode()).hexdigest()[:12]

        return {
            "success": True,
            "pdf_text": pdf_text,
            "pdf_id": pdf_id,
            "file_info": file_info
        }

    except Exception as e:
        error_msg = f"Error in PDF processing: {str(e)}"
        print(f"❌ {error_msg}")
        if process_id:
            update_progress(process_id, 0, f"Error: {error_msg}")
        return {"success": False, "error": error_msg}

def process_pdf_file(file_path=None, file_content=None, original_filename=None, index_name="default", process_id=None, user_selected_language=None):
    """
    Synchronous wrapper for PDF processing with enhanced capabilities and multi-language support.
    Process a PDF file and add to selected FAISS index.
    """
    try:
        if process_id:
            update_progress(process_id, 5, "Starting PDF processing...")

        if file_path:
            print(f"📄 Processing PDF from file: {file_path}")
        else:
            print(f"📄 Processing PDF from memory: {original_filename}")
        print(f"🎯 Target index: {index_name}")
        if user_selected_language:
            print(f"🌐 User selected language: {user_selected_language}")

        # Validate input
        if not file_path and not file_content:
            print("❌ Either file_path or file_content must be provided")
            return False

        if file_path:
            # Check if file exists and is PDF
            if not os.path.exists(file_path):
                print(f"❌ File not found: {file_path}")
                return False

            if not file_path.lower().endswith('.pdf'):
                print(f"❌ Not a PDF file: {file_path}")
                return False

            # Get file information
            file_info = get_pdf_info(file_path=file_path)
        else:
            # Get file information from content
            file_info = get_pdf_info(
                filename=original_filename or "uploaded.pdf",
                file_size=len(file_content) if file_content else 0
            )

        if not file_info:
            print("❌ Could not get PDF file information")
            return False

        print(f"📊 PDF info: {file_info['name']} ({file_info['size']} bytes)")

        # Extract text from PDF
        if process_id:
            update_progress(process_id, 10, "Extracting text from PDF...")

        if file_path:
            pdf_text = extract_text_from_pdf(file_path=file_path, process_id=process_id)
        else:
            pdf_text = extract_text_from_pdf(file_content=file_content, process_id=process_id)

        if not pdf_text:
            print("❌ Could not extract text from PDF")
            return False

        print(f"✅ Extracted text: {len(pdf_text)} characters")

        # Detect language from PDF content
        detected_language = detect_language_from_text(pdf_text)
        print(f"🔍 Detected language: {detected_language}")

        # Get language statistics for detailed analysis
        lang_stats = get_language_statistics(pdf_text)
        if lang_stats:
            print("📊 Language distribution:")
            for lang, ratio in sorted(lang_stats.items(), key=lambda x: x[1], reverse=True):
                print(f"   {lang}: {ratio:.3f} ({ratio*100:.1f}%)")

        # Determine final index name based on language detection and user preference
        if index_name == "default" or index_name == "auto":
            # Auto-detect index based on content language
            final_index_name = get_index_name_for_language(detected_language, user_selected_language)
        else:
            # Use user-specified index
            final_index_name = index_name
            print(f"🎯 Using user-specified index: {final_index_name}")

        print(f"📁 Final target index: {final_index_name}")

        # Generate PDF ID
        if file_path:
            pdf_id = get_pdf_id(file_path)
        else:
            # Generate ID from filename and content
            unique_string = f"{original_filename or 'uploaded.pdf'}_{len(file_content) if file_content else 0}"
            pdf_id = hashlib.md5(unique_string.encode()).hexdigest()[:12]

        # Load or create the target FAISS index
        if process_id:
            update_progress(process_id, 70, "Loading FAISS index...")

        # Import FAISS index management functions
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from full_code import load_faiss_index, create_faiss_index

        # Try to load existing index using final index name
        faiss_index, existing_metadata, success = load_faiss_index(final_index_name)

        if not success or faiss_index is None:
            print(f"📁 Creating new FAISS index: {final_index_name}")
            # Create new index if it doesn't exist
            dummy_embedding = embedder.encode(["hello world"])
            dim = len(dummy_embedding[0])
            result = create_faiss_index(final_index_name, dimension=dim, embed_model=EMBED_MODEL)
            if not result.get("success"):
                print(f"❌ Failed to create index: {result.get('error')}")
                return False

            # Load the newly created index
            faiss_index, existing_metadata, success = load_faiss_index(final_index_name)
            if not success:
                print(f"❌ Failed to load newly created index")
                return False

        print(f"✅ Loaded FAISS index: {final_index_name} with {len(existing_metadata)} existing entries")

        # Process PDF chunks with enhanced chunking
        if process_id:
            update_progress(process_id, 75, "Creating text chunks...")

        chunks = chunk_text_advanced(pdf_text)
        record_date = datetime.datetime.now().isoformat()

        print(f"📝 Processing {len(chunks)} chunks...")

        # Use async processing for better performance
        if process_id:
            update_progress(process_id, 80, "Generating embeddings...")

        # Process chunks synchronously for now (can be made async later)
        new_vectors = []
        new_metadata = []

        for chunk_idx, chunk in enumerate(chunks):
            # Create embedding for this chunk
            vec_list = embedder.encode([chunk])
            vec = np.array(vec_list[0], dtype="float32")
            new_vectors.append(vec)

            # Create URL based on source
            if file_path:
                url = f"file://{file_path}"
            else:
                url = f"upload://{original_filename or file_info['name']}"

            new_metadata.append({
                "chunk_text": chunk,
                "record_date": record_date,
                "category": "pdf",
                "url": url,
                "pdf_id": pdf_id,
                "title": original_filename or file_info["name"],
                "file_name": file_info["name"],
                "file_type": file_info["type"],
                "file_extension": file_info["extension"],
                "file_size": file_info["size"],
                "vector_id": f"pdf-{pdf_id}-chunk-{chunk_idx}",
                "source_type": "pdf",
                "upload_source": "pdf_upload",
                "detected_language": detected_language,
                "index_name": final_index_name,
                "language_stats": lang_stats,
                "user_selected_language": user_selected_language
            })

            print(f"📝 Embedded chunk {chunk_idx + 1}/{len(chunks)}: {chunk[:60]}...")

            if process_id:
                progress = 80 + (chunk_idx / len(chunks)) * 15  # 80-95% range
                update_progress(process_id, int(progress), f"Processing chunk {chunk_idx + 1}/{len(chunks)}")

        if not new_vectors:
            print("❌ No content to index")
            return False

        # Stack & normalize for cosine similarity
        if process_id:
            update_progress(process_id, 95, "Saving to FAISS index...")

        xb = np.vstack(new_vectors)
        faiss.normalize_L2(xb)

        # Add vectors to the existing index
        faiss_index.add(xb)

        # Combine existing and new metadata
        combined_metadata = existing_metadata + new_metadata

        # Save updated index and metadata
        index_dir = os.path.join(OUTPUT_DIR, final_index_name)
        os.makedirs(index_dir, exist_ok=True)

        faiss_file_path = os.path.join(index_dir, f"{final_index_name}.faiss")
        metadata_file_path = os.path.join(index_dir, f"{final_index_name}.json")

        # Save FAISS index
        faiss.write_index(faiss_index, faiss_file_path)
        print(f"🧠 Updated FAISS index saved to {faiss_file_path}")

        # Save metadata
        with open(metadata_file_path, "w", encoding="utf-8") as f:
            json.dump(combined_metadata, f, ensure_ascii=False, indent=2)
        print(f"🗃️ Updated metadata saved to {metadata_file_path}")

        if process_id:
            update_progress(process_id, 100, "PDF processing completed successfully!")

        print(f"✅ Finished: Added {len(new_vectors)} chunks to index '{final_index_name}' (total: {faiss_index.ntotal})")
        print(f"🌐 Content language: {detected_language}")
        print(f"📁 Stored in index: {final_index_name}")
        return True

    except Exception as e:
        error_msg = f"Error in PDF processing: {str(e)}"
        print(f"❌ {error_msg}")
        if process_id:
            update_progress(process_id, 0, f"Error: {error_msg}")
        return False

def main():
    print("📄 PDF Processor")
    print("=" * 50)
    print(f"📄 PDF processing: {'✅' if PDF_AVAILABLE else '❌'}")
    print(f"📁 PDFs directory: {PDFS_DIR}")

    # Test with the provided PDF file
    test_file = r"C:\Users\<USER>\Downloads\python-fiass\full_stack_java.pdf"
    if os.path.exists(test_file):
        print(f"\n🧪 Testing with: {test_file}")
        success = process_pdf_file(test_file)
        if success:
            print("🎉 PDF processed successfully!")
        else:
            print("❌ Failed to process PDF")
    else:
        print(f"❌ Test file not found: {test_file}")
        # Allow manual input
        test_file = input("\nEnter PDF file path: ").strip()
        if test_file and os.path.exists(test_file):
            success = process_pdf_file(test_file)
            if success:
                print("🎉 PDF processed successfully!")
            else:
                print("❌ Failed to process PDF")
        else:
            print("❌ File not found or invalid path")

if __name__ == "__main__":
    main()
