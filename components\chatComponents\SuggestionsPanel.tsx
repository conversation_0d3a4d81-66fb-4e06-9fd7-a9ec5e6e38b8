import React, { useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>bulb, PiSparkle } from 'react-icons/pi';

interface SuggestionsPanelProps {
  showSuggestions: boolean;
  setShowSuggestions: (show: boolean) => void;
  selectedLanguage: string;
  onSelectSuggestion: (suggestion: string) => void;
}

const SuggestionsPanel: React.FC<SuggestionsPanelProps> = ({
  showSuggestions,
  setShowSuggestions,
  selectedLanguage,
  onSelectSuggestion
}) => {
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Sample recommended suggestions
  const englishSuggestions = [
    "How does the new tax regime (post-2023) compare with the old tax regime in terms of benefits for salaried individuals?",
    "How has the rise of UPI (Unified Payments Interface) transformed retail banking and cashless transactions in India?",
    "What factors should a retail investor in India consider before investing in IPOs?",
    "How effective has the Pradhan Mantri Jan Dhan Yojana (PMJDY) been in achieving financial inclusion in rural India?",
    "How are fintech startups like <PERSON><PERSON> and <PERSON>row<PERSON> changing the investment landscape for young Indians?"
  ];

  // Tamil financial suggestions
  const tamilSuggestions = [
    "இந்தியாவில் டிஜிட்டல் வாலட் மற்றும் மொபைல் பேமெண்ட் பயன்பாடுகள் நிதி சேவைகளை அணுகுவதை எவ்வாறு மாற்றியுள்ளன?",
    "நீண்ட கால ஓய்வூதிய திட்டங்களில் முதலீடு செய்வதற்கான சிறந்த வழிகள் என்ன மற்றும் அவற்றின் வரி நன்மைகள் என்ன?",
    "சிறு மற்றும் நடுத்தர தொழில்களுக்கு (SMEs) இந்தியாவில் கிடைக்கும் நிதி ஆதரவு திட்டங்கள் என்னென்ன?",
    "பங்குச் சந்தை முதலீட்டிற்கும் தங்கம் மற்றும் நிலம் போன்ற பாரம்பரிய முதலீடுகளுக்கும் இடையே உள்ள முக்கிய வேறுபாடுகள் என்ன?",
    "இந்தியாவில் கிரிப்டோகரன்சி மற்றும் டிஜிட்டல் சொத்துக்களுக்கான தற்போதைய ஒழுங்குமுறை நிலைப்பாடு என்ன?"
  ];

  // Telugu financial suggestions
  const teluguSuggestions = [
    "భారతదేశంలో మ్యూచువల్ ఫండ్స్ లో పెట్టుబడి పెట్టడానికి ఉత్తమ వ్యూహాలు ఏమిటి మరియు వాటి ప్రయోజనాలు ఏమిటి?",
    "వ్యక్తిగత ఆర్థిక ప్రణాళిక కోసం డిజిటల్ టూల్స్ మరియు యాప్‌లు ఎలా ఉపయోగపడతాయి?",
    "భారతదేశంలో స్టార్టప్‌లకు వెంచర్ క్యాపిటల్ మరియు ఏంజెల్ ఇన్వెస్టర్‌ల నుండి నిధులు సేకరించడం ఎలా?",
    "రియల్ ఎస్టేట్ పెట్టుబడులకు REIT (రియల్ ఎస్టేట్ ఇన్వెస్ట్‌మెంట్ ట్రస్ట్‌లు) ఎలా ప్రత్యామ్నాయంగా పనిచేస్తాయి?",
    "భారతదేశంలో ఫినాన్షియల్ లిటరసీని మెరుగుపరచడానికి ప్రభుత్వం మరియు ప్రైవేట్ రంగం తీసుకుంటున్న చర్యలు ఏమిటి?"
  ];

  // Kannada financial suggestions
  const kannadaSuggestions = [
    "ಭಾರತದಲ್ಲಿ ಸಣ್ಣ ಉಳಿತಾಯ ಯೋಜನೆಗಳು ಮತ್ತು ಸರ್ಕಾರಿ ಬಾಂಡ್‌ಗಳಲ್ಲಿ ಹೂಡಿಕೆ ಮಾಡುವುದರ ಪ್ರಯೋಜನಗಳು ಯಾವುವು?",
    "ಹಣದುಬ್ಬರದ ಸಮಯದಲ್ಲಿ ಹಣಕಾಸು ಸ್ಥಿರತೆಯನ್ನು ಕಾಪಾಡಿಕೊಳ್ಳಲು ಉತ್ತಮ ಆರ್ಥಿಕ ತಂತ್ರಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಸ್ವಯಂ ಉದ್ಯೋಗಿಗಳು ಮತ್ತು ಫ್ರೀಲ್ಯಾನ್ಸರ್‌ಗಳಿಗೆ ಲಭ್ಯವಿರುವ ತೆರಿಗೆ ಯೋಜನೆ ಮತ್ತು ಹಣಕಾಸು ಸಾಧನಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಹೊಸ ಪೆನ್ಷನ್ ಯೋಜನೆ (NPS) ಮತ್ತು ಅಟಲ್ ಪೆನ್ಷನ್ ಯೋಜನೆ (APY) ನಡುವಿನ ವ್ಯತ್ಯಾಸಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಮಹಿಳಾ ಉದ್ಯಮಿಗಳಿಗೆ ಲಭ್ಯವಿರುವ ವಿಶೇಷ ಹಣಕಾಸು ಯೋಜನೆಗಳು ಮತ್ತು ಸಾಲ ಕಾರ್ಯಕ್ರಮಗಳು ಯಾವುವು?"
  ];

  // Get suggestions based on selected language
  const getSuggestionsByLanguage = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return tamilSuggestions;
      case "Telugu":
        return teluguSuggestions;
      case "Kannada":
        return kannadaSuggestions;
      default:
        return englishSuggestions;
    }
  };

  const recommendedSuggestions = getSuggestionsByLanguage();

  // Handle click outside to close suggestions
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [setShowSuggestions]);

  const getLanguageColor = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return "purple";
      case "Telugu":
        return "green";
      case "Kannada":
        return "orange";
      default:
        return "blue";
    }
  };

  const getLanguageText = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return {
          title: "பரிந்துரைக்கப்பட்ட கேள்விகள்",
          subtitle: "தொடங்குவதற்கு ஒரு கேள்வியைத் தேர்ந்தெடுக்கவும்"
        };
      case "Telugu":
        return {
          title: "సిఫార్సు చేయబడిన ప్రశ్నలు",
          subtitle: "ప్రారంభించడానికి ఒక ప్రశ్నను ఎంచుకోండి"
        };
      case "Kannada":
        return {
          title: "ಶಿಫಾರಸು ಮಾಡಿದ ಪ್ರಶ್ನೆಗಳು",
          subtitle: "ಪ್ರಾರಂಭಿಸಲು ಒಂದು ಪ್ರಶ್ನೆಯನ್ನು ಆಯ್ಕೆಮಾಡಿ"
        };
      default:
        return {
          title: "Recommended Questions",
          subtitle: "Choose a question to get started"
        };
    }
  };

  if (!showSuggestions) return null;

  const color = getLanguageColor();
  const text = getLanguageText();

  return (
    <div 
      ref={suggestionsRef}
      className="absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-lg z-50 max-h-96 overflow-y-auto animate-fadeIn"
    >
      <div className="p-4">
        <div className="flex items-center gap-2 mb-3">
          <PiLightbulb className={`w-5 h-5 text-${color}-500`} />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            {text.title}
          </h3>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
          {text.subtitle}
        </p>
        <div className="space-y-2">
          {recommendedSuggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => onSelectSuggestion(suggestion)}
              className={`
                w-full text-left p-3 rounded-lg border border-transparent transition-all
                hover:bg-${color}-50 hover:border-${color}-200 dark:hover:bg-${color}-900/20
                text-sm text-gray-700 dark:text-gray-300
                flex items-start gap-3
              `}
            >
              <PiSparkle className={`w-4 h-4 text-${color}-500 flex-shrink-0 mt-0.5`} />
              <span className="leading-relaxed">{suggestion}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SuggestionsPanel;
