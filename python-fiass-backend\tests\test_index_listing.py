#!/usr/bin/env python3
"""
Test script to verify that the FAISS index listing is working correctly.
"""

import requests
import json

BASE_URL = "http://localhost:5010"

def test_list_all_indexes():
    """Test listing all indexes (no user filter)."""
    print("🔍 Testing: List all FAISS indexes (no user filter)")
    
    try:
        response = requests.get(f"{BASE_URL}/api/list-faiss-indexes")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Found {data.get('total_count', 0)} indexes")
            print(f"📋 Indexes: {data.get('indexes', [])}")
            return True
        else:
            print(f"❌ Failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_list_user_indexes(user_email):
    """Test listing indexes for a specific user."""
    print(f"\n🔍 Testing: List FAISS indexes for user '{user_email}'")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/list-faiss-indexes",
            headers={"Content-Type": "application/json"},
            json={"email": user_email}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Found {data.get('total_count', 0)} indexes for user")
            print(f"📋 Indexes: {data.get('indexes', [])}")
            print(f"👤 Filtered by user: {data.get('filtered_by_user', False)}")
            return True
        else:
            print(f"❌ Failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_robin_index_query():
    """Test querying the robin index to make sure it works."""
    print(f"\n🔍 Testing: Query robin index")
    
    try:
        response = requests.post(
            f"{BASE_URL}/financial_query",
            headers={"Content-Type": "application/json"},
            json={
                "query": "What is this data about?",
                "index_name": "robin",
                "client_email": "<EMAIL>"
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Robin index query worked")
            print(f"📝 Response preview: {data.get('ai_response', '')[:100]}...")
            return True
        else:
            print(f"❌ Failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing FAISS Index Listing Functionality")
    print("=" * 50)
    
    # Test 1: List all indexes
    success1 = test_list_all_indexes()
    
    # Test 2: List <NAME_EMAIL> (should include robin)
    success2 = test_list_user_indexes("<EMAIL>")
    
    # Test 3: List indexes for another user (should only include default)
    success3 = test_list_user_indexes("<EMAIL>")
    
    # Test 4: Test querying the robin index
    success4 = test_robin_index_query()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   List all indexes: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   List <EMAIL> indexes: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"   List <EMAIL> indexes: {'✅ PASS' if success3 else '❌ FAIL'}")
    print(f"   Query robin index: {'✅ PASS' if success4 else '❌ FAIL'}")
    
    if all([success1, success2, success3, success4]):
        print("\n🎉 All tests passed! The robin index should now be visible in your frontend.")
    else:
        print("\n⚠️ Some tests failed. Please check the backend server and try again.")