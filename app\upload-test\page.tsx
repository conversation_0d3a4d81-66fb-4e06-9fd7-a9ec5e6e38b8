'use client';

import React from 'react';
import UploadTestComponent from '../../components/UploadTestComponent';
import { Toaster } from 'react-hot-toast';

const UploadTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Upload Functionality Test
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Test the PDF/Document upload functionality and backend connectivity
          </p>
        </div>
        
        <UploadTestComponent />
      </div>
      
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#4ade80',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
    </div>
  );
};

export default UploadTestPage;
