#!/usr/bin/env python3
"""
Test script to verify continuous capital letter translation skipping
"""

import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.translation_service import TranslationService

def test_continuous_capital_letter_skipping():
    """Test that translation is skipped for texts containing continuous capital letters"""
    
    print("🧪 Testing Continuous Capital Letter Translation Skipping")
    print("=" * 60)
    
    # Initialize translation service
    translation_service = TranslationService()
    
    # Test cases with various capital letter patterns
    test_cases = [
        {
            "text": "What is API and how does REST work?",
            "source_lang": "en",
            "target_lang": "ta",
            "description": "Continuous caps words (API, REST)",
            "should_skip": True
        },
        {
            "text": "Microsoft Azure provides cloud services",
            "source_lang": "en", 
            "target_lang": "te",
            "description": "Proper nouns (Microsoft, Azure) - single capitals",
            "should_skip": False
        },
        {
            "text": "The iPhone uses iOS operating system",
            "source_lang": "en",
            "target_lang": "kn", 
            "description": "Mixed case words (iPhone, iOS) - single capitals",
            "should_skip": False
        },
        {
            "text": "how are you today",
            "source_lang": "en",
            "target_lang": "ta",
            "description": "No capital letters",
            "should_skip": False
        },
        {
            "text": "what is machine learning",
            "source_lang": "en",
            "target_lang": "te",
            "description": "All lowercase",
            "should_skip": False
        },
        {
            "text": "JSON data format is used in REST APIs",
            "source_lang": "en",
            "target_lang": "hi",
            "description": "Multiple continuous caps (JSON, REST, APIs)",
            "should_skip": True
        },
        {
            "text": "What about GitHub and GitLab?",
            "source_lang": "en",
            "target_lang": "ta",
            "description": "CamelCase words (GitHub, GitLab) - single capitals",
            "should_skip": False
        }
    ]
    
    print(f"Running {len(test_cases)} test cases...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['description']}")
        print(f"Original: {test_case['text']}")
        print(f"Language: {test_case['source_lang']} -> {test_case['target_lang']}")
        print(f"Expected to skip: {test_case['should_skip']}")
        
        # Test continuous capital letter detection
        has_continuous_capitals = translation_service._has_continuous_capital_letters(test_case['text'])
        should_skip = translation_service._should_skip_translation(
            test_case['text'], 
            test_case['source_lang'], 
            test_case['target_lang']
        )
        
        print(f"Has continuous capital letters: {has_continuous_capitals}")
        print(f"Should skip translation: {should_skip}")
        
        # Test full translation
        try:
            result = translation_service.translate_text(
                test_case['text'], 
                test_case['target_lang'], 
                test_case['source_lang']
            )
            
            translated_text = result['translated_text']
            provider = result.get('translation_provider', 'unknown')
            
            print(f"Translated: {translated_text}")
            print(f"Provider: {provider}")
            
            # Check if translation was handled correctly
            if test_case['should_skip']:
                if translated_text == test_case['text'] and provider in ['skipped_capital_letters', 'same_language']:
                    print("✅ PASS - Translation correctly skipped")
                else:
                    print("❌ FAIL - Translation should have been skipped")
            else:
                if translated_text != test_case['text'] and provider not in ['skipped_capital_letters', 'same_language']:
                    print("✅ PASS - Translation performed as expected")
                else:
                    print("❌ FAIL - Translation should have been performed")
                
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
        
        print("-" * 50)
        print()

def test_continuous_capital_detection_methods():
    """Test the continuous capital letter detection methods"""
    
    print("🔧 Testing Continuous Capital Letter Detection Methods")
    print("=" * 60)
    
    translation_service = TranslationService()
    
    test_cases = [
        ("The API uses JSON format", True, "Contains API, JSON (continuous capitals)"),
        ("Microsoft Azure cloud", False, "Contains Microsoft, Azure (single capitals only)"),
        ("iPhone and iOS system", False, "Contains iPhone, iOS (single capitals only)"),
        ("hello world today", False, "All lowercase"),
        ("how are you", False, "All lowercase"),
        ("What about this", False, "Contains What (single capital W)"),
        ("REST API and JSON", True, "Multiple continuous capitals (REST, API, JSON)"),
        ("github and gitlab", False, "All lowercase"),
        ("GitHub and GitLab", False, "CamelCase words (single capitals only)"),
        ("HTTP and HTTPS protocols", True, "Contains HTTP, HTTPS (continuous capitals)"),
        ("SQL database with XML", True, "Contains SQL, XML (continuous capitals)"),
        ("Python and JavaScript", False, "Contains Python, JavaScript (single capitals only)")
    ]
    
    print(f"Testing {len(test_cases)} detection cases...\n")
    
    for i, (text, expected, description) in enumerate(test_cases, 1):
        print(f"Test {i}: {description}")
        print(f"Text: '{text}'")
        print(f"Expected has continuous capitals: {expected}")
        
        has_continuous_capitals = translation_service._has_continuous_capital_letters(text)
        print(f"Detected has continuous capitals: {has_continuous_capitals}")
        
        if has_continuous_capitals == expected:
            print("✅ PASS - Detection correct")
        else:
            print("❌ FAIL - Detection incorrect")
        
        print("-" * 30)

if __name__ == "__main__":
    test_continuous_capital_detection_methods()
    print()
    test_continuous_capital_letter_skipping()
    
    print("🏁 Testing completed!")