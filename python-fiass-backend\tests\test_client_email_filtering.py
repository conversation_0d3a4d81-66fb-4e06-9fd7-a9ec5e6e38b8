#!/usr/bin/env python3
"""
Test script to verify client email filtering functionality for FAISS indexes.
This script tests the complete flow of uploading CSV files with client emails
and verifying that indexes are properly filtered by client email.
"""

import sys
import os
import requests
import json
import tempfile
import csv

# Add the python-fiass-backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

# Import our modules
from database import init_db, get_connection
from pine_filter_service import PineFilterService

# Backend URL
BACKEND_URL = "http://localhost:5010"

def create_test_csv(filename, data):
    """Create a test CSV file with the given data."""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(data)
    print(f"✅ Created test CSV file: {filename}")

def test_upload_csv(file_path, client_email, index_name):
    """Test uploading a CSV file with client email."""
    print(f"\n🔄 Testing CSV upload for client: {client_email}, index: {index_name}")
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'client': client_email,
                'index_name': index_name,
                'embed_model': 'all-MiniLM-L6-v2'
            }
            
            response = requests.post(f"{BACKEND_URL}/api/upload-csv", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Upload successful for {client_email}")
                print(f"   Index: {result.get('data', {}).get('index_name', 'N/A')}")
                print(f"   Vectors: {result.get('data', {}).get('vector_count', 'N/A')}")
                return True
            else:
                print(f"❌ Upload failed for {client_email}: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error uploading for {client_email}: {e}")
        return False

def test_list_indexes(client_email):
    """Test listing indexes for a specific client."""
    print(f"\n🔍 Testing index listing for client: {client_email}")
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/list-faiss-indexes",
            json={'email': client_email},
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            indexes = result.get('indexes', [])
            print(f"✅ Found {len(indexes)} indexes for {client_email}: {indexes}")
            return indexes
        else:
            print(f"❌ Failed to list indexes for {client_email}: {response.status_code}")
            print(f"   Response: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Error listing indexes for {client_email}: {e}")
        return []

def test_database_filtering():
    """Test database-level filtering using PineFilterService."""
    print(f"\n🗄️  Testing database-level filtering...")
    
    test_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    
    for email in test_emails:
        success, message, indexes = PineFilterService.get_user_indices(email)
        if success:
            print(f"✅ {email}: Found {len(indexes)} indexes - {indexes}")
        else:
            print(f"❌ {email}: {message}")

def main():
    """Main test function."""
    print("🚀 Starting Client Email Filtering Test")
    print("=" * 50)
    
    # Initialize database
    print("📊 Initializing database...")
    init_db()
    
    # Test data
    test_clients = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>'
    ]
    
    # Create test CSV files
    test_data = [
        ['Name', 'Description', 'Category'],
        ['Product A', 'High quality product A', 'Electronics'],
        ['Product B', 'Affordable product B', 'Home'],
        ['Product C', 'Premium product C', 'Electronics']
    ]
    
    temp_files = []
    
    try:
        # Create test CSV files for each client
        for i, client in enumerate(test_clients):
            temp_file = f"test_data_client_{i+1}.csv"
            create_test_csv(temp_file, test_data)
            temp_files.append(temp_file)
            
            # Upload CSV for each client with unique index names
            index_name = f"test_index_client_{i+1}"
            success = test_upload_csv(temp_file, client, index_name)
            
            if not success:
                print(f"⚠️  Upload failed for {client}, continuing with other tests...")
        
        print("\n" + "=" * 50)
        print("🔍 Testing Index Filtering")
        print("=" * 50)
        
        # Test index listing for each client
        for client in test_clients:
            indexes = test_list_indexes(client)
            
            # Verify that each client only sees their own indexes
            expected_index = f"test_index_client_{test_clients.index(client) + 1}"
            if expected_index in indexes:
                print(f"✅ Filtering working correctly for {client}")
            else:
                print(f"❌ Filtering issue for {client} - expected {expected_index}")
        
        # Test database-level filtering
        test_database_filtering()
        
        print("\n" + "=" * 50)
        print("📋 Test Summary")
        print("=" * 50)
        
        # Check database state
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT email, index_name, file_name FROM pine_collection ORDER BY email")
        rows = cursor.fetchall()
        
        print(f"📊 Pine Collection Contents ({len(rows)} entries):")
        for row in rows:
            print(f"   📧 {row[0]} -> 📁 {row[1]} ({row[2]})")
        
        conn.close()
        
        print("\n✅ Client Email Filtering Test Completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        
    finally:
        # Clean up temporary files
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    print(f"🧹 Cleaned up: {temp_file}")
            except Exception as e:
                print(f"⚠️  Could not clean up {temp_file}: {e}")

if __name__ == "__main__":
    main()