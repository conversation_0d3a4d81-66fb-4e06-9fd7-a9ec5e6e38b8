import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import HideStaticIndicator from "../components/HideStaticIndicator";
import ErrorSuppressor from "../components/ErrorSuppressor";

const InterFont = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Queryone",
  description: "Advanced AI Chat Interface with Bot Creation",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Prevent browser extensions from interfering during hydration
              if (typeof window !== 'undefined') {
                window.__REACT_HYDRATION_STARTED__ = true;
              }
              
              // Hide static indicator and suppress console errors immediately
              (function() {
                // Suppress console errors that show in UI
                const originalError = console.error;
                const originalWarn = console.warn;
                
                console.error = function(...args) {
                  const message = args.join(' ');
                  const suppressPatterns = [
                    'Warning: Extra attributes from the server',
                    'Warning: Prop',
                    'Warning: React does not recognize',
                    'Hydration failed',
                    'Text content does not match',
                    'nextjs-static-indicator',
                    'static-indicator'
                  ];
                  
                  if (!suppressPatterns.some(pattern => message.includes(pattern))) {
                    originalError.apply(console, args);
                  }
                };
                
                console.warn = function(...args) {
                  const message = args.join(' ');
                  const suppressPatterns = [
                    'Warning: Extra attributes from the server',
                    'Warning: Prop',
                    'Warning: React does not recognize',
                    'nextjs-static-indicator'
                  ];
                  
                  if (!suppressPatterns.some(pattern => message.includes(pattern))) {
                    originalWarn.apply(console, args);
                  }
                };
                
                const hideIndicator = () => {
                  const style = document.createElement('style');
                  style.textContent = \`
                    .nextjs-toast,
                    .nextjs-static-indicator-toast-wrapper,
                    [data-nextjs-toast-wrapper="true"],
                    [data-nextjs-toast],
                    div[role="status"]:has(.nextjs-static-indicator-toast-wrapper),
                    div[role="status"].nextjs-toast,
                    nextjs-portal,
                    [data-nextjs-dialog-overlay],
                    [data-nextjs-toast-errors] {
                      display: none !important;
                      visibility: hidden !important;
                      opacity: 0 !important;
                    }
                  \`;
                  document.head.appendChild(style);
                };
                
                if (document.readyState === 'loading') {
                  document.addEventListener('DOMContentLoaded', hideIndicator);
                } else {
                  hideIndicator();
                }
              })();
            `,
          }}
        />
      </head>
      <body className={`${InterFont.variable} `} suppressHydrationWarning>
        <ErrorSuppressor />
        <HideStaticIndicator />
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
