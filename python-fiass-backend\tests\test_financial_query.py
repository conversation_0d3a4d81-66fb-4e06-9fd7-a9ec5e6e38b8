#!/usr/bin/env python3
"""
Simple test script to verify the /financial_query endpoint works correctly
with dynamic index selection from PINE collection.
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_EMAIL = "<EMAIL>"  # Replace with your test email

def test_debug_endpoint():
    """Test the debug endpoint to see available indexes for a user."""
    print("🔍 Testing debug endpoint...")
    
    url = f"{BASE_URL}/api/debug/user-indexes"
    params = {"client_email": TEST_EMAIL}
    
    try:
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        
        if response.ok:
            data = response.json()
            print("✅ Debug endpoint response:")
            print(f"   - User Email: {data.get('client_email')}")
            print(f"   - PINE API Status: {data.get('pine_api_status')}")
            print(f"   - User Indexes: {[idx['index_name'] for idx in data.get('user_indexes', [])]}")
            print(f"   - Available FAISS Files: {data.get('available_faiss_files', [])}")
            return data.get('user_indexes', [])
        else:
            print(f"❌ Debug endpoint failed: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Error testing debug endpoint: {e}")
        return []

def test_financial_query(index_name="default"):
    """Test the financial query endpoint with a specific index."""
    print(f"\n💬 Testing financial query with index: {index_name}")
    
    url = f"{BASE_URL}/financial_query"
    payload = {
        "query": "What are Aleutian Clouds?",
        "client_email": TEST_EMAIL,
        "index_name": index_name,
        "language": "English"
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        
        if response.ok:
            data = response.json()
            print("✅ Financial query response:")
            print(f"   - Query: {data.get('query', 'N/A')}")
            print(f"   - Index Used: {data.get('index_used', 'N/A')}")
            print(f"   - Search Engine: {data.get('search_engine', 'N/A')}")
            print(f"   - AI Response Length: {len(data.get('ai_response', ''))}")
            print(f"   - Retrieved Documents: {len(data.get('retrieved_documents', []))}")
            
            # Check sentence analysis
            sentence_analysis = data.get('sentence_analysis', [])
            print(f"   - Sentence Analysis Count: {len(sentence_analysis)}")
            
            if sentence_analysis:
                print("   📋 First sentence analysis:")
                first_sentence = sentence_analysis[0]
                print(f"      - Sentence: {first_sentence.get('sentence', 'N/A')[:50]}...")
                print(f"      - Source Title: {first_sentence.get('source_title', 'N/A')}")
                print(f"      - Source Type: {first_sentence.get('source_type', 'N/A')}")
                print(f"      - Summary: {first_sentence.get('summary', 'N/A')}")
                print(f"      - URL: {first_sentence.get('url', 'N/A')}")
            
            return True
        else:
            print(f"❌ Financial query failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing financial query: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Financial Query Tests")
    print("=" * 50)
    
    # Test debug endpoint first
    user_indexes = test_debug_endpoint()
    
    # Test with default index
    test_financial_query("default")
    
    # Test with user-specific indexes if available
    if user_indexes:
        for idx_info in user_indexes[:2]:  # Test first 2 user indexes
            index_name = idx_info.get('index_name')
            if index_name and index_name != 'default':
                test_financial_query(index_name)
    
    print("\n" + "=" * 50)
    print("🏁 Tests completed!")

if __name__ == "__main__":
    main()