#!/usr/bin/env python3
"""
Test script for translation functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.translation_service import translation_service

def test_basic_translation():
    """Test basic translation functionality"""
    print("🧪 Testing Basic Translation...")
    
    # Test English to Telugu
    test_text = "Hello, how are you?"
    result = translation_service.translate_text(test_text, 'te', 'en')
    print(f"EN->TE: '{test_text}' -> '{result['translated_text']}'")
    print(f"Provider: {result['translation_provider']}")
    print()
    
    # Test a longer financial text
    financial_text = """Based on general knowledge, here are some effective strategies for investing in mutual funds in India and their benefits:

**Strategies for Investing in Mutual Funds:**

1. Systematic Investment Plan (SIP):
   • Invest a fixed amount regularly (monthly/quarterly) to benefit from rupee cost averaging.
   • Reduces market timing risk.

2. Diversification:
   • Invest across different categories (equity, debt, hybrid) to minimize risk.
   • Example: Large-cap, mid-cap, and sectoral funds.

3. Long-Term Investing:
   • Staying invested for 5+ years helps ride out market volatility and benefit from compounding.

4. Asset Allocation:
   • Adjust equity-debt ratio based on risk appetite (e.g., 60% equity, 40% debt for moderate risk).

5. Tax-Efficient Funds:
   • ELSS (Equity-Linked Savings Scheme) for tax deductions under Section 80C.

**Benefits of Mutual Funds:**

• Professional Management: Managed by experts."""
    
    print("🧪 Testing Long Text Translation...")
    result = translation_service._translate_long_text(financial_text, 'en', 'te')
    print(f"Long text translation result (first 200 chars):")
    print(f"'{result[:200]}...'")
    print()

def test_fallback_patterns():
    """Test fallback pattern matching"""
    print("🧪 Testing Fallback Patterns...")
    
    test_phrases = [
        "Based on the provided information",
        "Systematic Investment Plan",
        "mutual funds",
        "Professional Management"
    ]
    
    for phrase in test_phrases:
        result = translation_service._get_fallback_translations(phrase, 'en', 'te')
        print(f"Fallback EN->TE: '{phrase}' -> '{result}'")
    print()

def test_mymemory_api():
    """Test MyMemory API directly"""
    print("🧪 Testing MyMemory API...")
    
    test_text = "Hello, how are you?"
    result = translation_service._translate_with_mymemory(test_text, 'en', 'te')
    print(f"MyMemory EN->TE: '{test_text}' -> '{result}'")
    print()

def test_libretranslate_api():
    """Test LibreTranslate API directly"""
    print("🧪 Testing LibreTranslate API...")
    
    test_text = "Hello, how are you?"
    result = translation_service._translate_with_libretranslate(test_text, 'en', 'te')
    print(f"LibreTranslate EN->TE: '{test_text}' -> '{result}'")
    print()

def test_response_translation():
    """Test full response translation"""
    print("🧪 Testing Response Translation...")
    
    sample_response = {
        'ai_response': """Based on general knowledge, here are some effective strategies for investing in mutual funds in India and their benefits:

**Strategies for Investing in Mutual Funds:**

1. Systematic Investment Plan (SIP):
   • Invest a fixed amount regularly (monthly/quarterly) to benefit from rupee cost averaging.
   • Reduces market timing risk.

2. Diversification:
   • Invest across different categories (equity, debt, hybrid) to minimize risk.

**Benefits of Mutual Funds:**

• Professional Management: Managed by experts.""",
        'related_questions': [
            'What are the best mutual fund categories for beginners?',
            'How to choose between SIP and lump sum investment?',
            'What are the tax implications of mutual fund investments?'
        ]
    }
    
    translated_response = translation_service.translate_response_data(sample_response, 'te')
    
    print("Translated AI Response (first 300 chars):")
    print(f"'{translated_response['ai_response'][:300]}...'")
    print()
    
    print("Translated Related Questions:")
    for i, question in enumerate(translated_response['related_questions']):
        print(f"{i+1}. {question}")
    print()

if __name__ == "__main__":
    print("🚀 Starting Translation Tests...")
    print("=" * 50)
    
    try:
        test_basic_translation()
        test_fallback_patterns()
        test_mymemory_api()
        test_libretranslate_api()
        test_response_translation()
        
        print("✅ All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
