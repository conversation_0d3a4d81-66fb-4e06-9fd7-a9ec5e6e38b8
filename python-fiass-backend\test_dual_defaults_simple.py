#!/usr/bin/env python3
"""
Simple test to verify dual default index configuration.
"""

import os
import sys

# Add the parent directory to the path so we can import from full_code
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_configuration():
    """Test the basic configuration."""
    print("🧪 Testing Dual Default Index Configuration")
    print("=" * 50)
    
    try:
        from full_code import AVAILABLE_DEFAULT_INDEXES, PRIMARY_DEFAULT_INDEX, get_available_default_index
        
        print(f"✅ Available default indexes: {AVAILABLE_DEFAULT_INDEXES}")
        print(f"✅ Primary default index: {PRIMARY_DEFAULT_INDEX}")
        print(f"✅ First available default: {get_available_default_index()}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_directory_structure():
    """Test the directory structure."""
    print("\n🧪 Testing Directory Structure")
    print("=" * 50)
    
    try:
        from full_code import FAISS_DATA_DIR, AVAILABLE_DEFAULT_INDEXES
        
        print(f"📁 FAISS data directory: {FAISS_DATA_DIR}")
        
        for default_index in AVAILABLE_DEFAULT_INDEXES:
            index_dir = os.path.join(FAISS_DATA_DIR, default_index)
            if os.path.exists(index_dir):
                print(f"✅ {default_index} directory exists: {index_dir}")
                
                # List files in the directory
                files = os.listdir(index_dir)
                print(f"   📄 Files: {files}")
                
                # Check for expected files
                expected_faiss = f"{default_index}.faiss"
                expected_json = f"{default_index}.json"
                
                if expected_faiss in files and expected_json in files:
                    print(f"   ✅ Standard files found: {expected_faiss}, {expected_json}")
                else:
                    print(f"   ⚠️  Standard files not found, checking for other patterns...")
                    faiss_files = [f for f in files if f.endswith('.faiss')]
                    json_files = [f for f in files if f.endswith('.json')]
                    print(f"   📄 FAISS files: {faiss_files}")
                    print(f"   📄 JSON files: {json_files}")
            else:
                print(f"❌ {default_index} directory does not exist: {index_dir}")
        
        return True
    except Exception as e:
        print(f"❌ Directory structure test failed: {e}")
        return False

def test_index_loading():
    """Test loading both default indexes."""
    print("\n🧪 Testing Index Loading")
    print("=" * 50)
    
    try:
        from full_code import load_faiss_index, AVAILABLE_DEFAULT_INDEXES
        
        for default_index in AVAILABLE_DEFAULT_INDEXES:
            print(f"\n🔍 Testing {default_index} index:")
            try:
                faiss_index, metadata, success = load_faiss_index(default_index)
                if success and faiss_index is not None:
                    print(f"   ✅ Successfully loaded {default_index} index")
                    print(f"   📊 Vectors: {faiss_index.ntotal}")
                    print(f"   📄 Metadata entries: {len(metadata) if metadata else 0}")
                else:
                    print(f"   ❌ Failed to load {default_index} index")
            except Exception as e:
                print(f"   ❌ Error loading {default_index} index: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Index loading test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Dual Default Index Tests")
    print("=" * 60)
    
    tests = [
        test_configuration,
        test_directory_structure,
        test_index_loading
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"✅ Passed: {sum(results)}/{len(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All tests passed! Dual default index support is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the output above for details.")

if __name__ == "__main__":
    main()