"""
Optimized Tamil Text Processing Module
Addresses issues with small responses and slow processing for Tamil content.
"""

import re
import unicodedata
from typing import Dict, List, Optional, Tuple
import time

class OptimizedTamilProcessor:
    """
    Optimized processor for Tamil text that preserves content while improving speed.
    """
    
    def __init__(self):
        # Tamil Unicode ranges
        self.tamil_range = re.compile(r'[\u0B80-\u0BFF]')
        
        # Patterns that should be preserved in Tamil text
        self.preserve_patterns = [
            r'[\u0B80-\u0BFF]+',  # Tamil characters
            r'\d+',  # Numbers
            r'[A-Za-z]+',  # English words
            r'[.!?]',  # Punctuation
        ]
        
        # Only remove truly problematic patterns
        self.problematic_patterns = [
            r'__[A-Z_]+__',  # Placeholder patterns like __CAPITAL_WORD__
            r'\[CAPITAL_WORD\]',  # Bracket placeholders
            r'\d+_\[CAPITAL_WORD\]_CAPWORD',  # Complex placeholders
            r'\*\.?\d*\*',  # Asterisk patterns like *.21*
        ]
        
        # Performance tracking
        self.processing_stats = {
            'total_processed': 0,
            'avg_processing_time': 0.0,
            'content_preserved_ratio': 0.0
        }
    
    def is_tamil_text(self, text: str) -> bool:
        """Quick check if text contains Tamil characters."""
        return bool(self.tamil_range.search(text))
    
    def clean_tamil_text_optimized(self, text: str) -> str:
        """
        Optimized Tamil text cleaning that preserves content while removing only
        genuine corruption patterns.
        
        Args:
            text: Input Tamil text
            
        Returns:
            str: Cleaned text with maximum content preservation
        """
        start_time = time.time()
        original_length = len(text)
        
        if not text or not isinstance(text, str):
            return text
        
        # Skip processing if text is too short
        if len(text.strip()) < 10:
            return text
        
        # Only process if it contains Tamil characters
        if not self.is_tamil_text(text):
            return text
        
        cleaned_text = text
        
        # Step 1: Remove only genuine problematic patterns
        for pattern in self.problematic_patterns:
            cleaned_text = re.sub(pattern, '', cleaned_text)
        
        # Step 2: Fix only obvious repetition issues (be very conservative)
        # Only remove if the same word appears 3+ times consecutively
        cleaned_text = re.sub(r'(\b[\u0B80-\u0BFF]+\b)(\s+\1){2,}', r'\1', cleaned_text)
        
        # Step 3: Clean up excessive whitespace
        cleaned_text = re.sub(r'\s{3,}', ' ', cleaned_text)
        cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
        
        # Step 4: Fix broken sentence structure only if clearly corrupted
        # Only fix if there are scattered numbers breaking sentences
        cleaned_text = re.sub(r'\.(\d+)\.(\d+)\.', '. ', cleaned_text)
        cleaned_text = re.sub(r'(\d+)\.(\d+)', '', cleaned_text)
        
        # Step 5: Normalize Unicode (important for Tamil)
        cleaned_text = unicodedata.normalize('NFC', cleaned_text)
        
        # Final cleanup
        cleaned_text = cleaned_text.strip()
        
        # Update performance stats
        processing_time = time.time() - start_time
        self.processing_stats['total_processed'] += 1
        self.processing_stats['avg_processing_time'] = (
            (self.processing_stats['avg_processing_time'] * (self.processing_stats['total_processed'] - 1) + processing_time) /
            self.processing_stats['total_processed']
        )
        self.processing_stats['content_preserved_ratio'] = len(cleaned_text) / original_length if original_length > 0 else 1.0
        
        return cleaned_text
    
    def detect_tamil_corruption_conservative(self, text: str) -> Tuple[bool, str, Dict]:
        """
        Conservative corruption detection for Tamil text that avoids false positives.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Tuple[bool, str, Dict]: (is_corrupted, cleaned_text, details)
        """
        if not text or len(text.strip()) < 10:
            return False, text, {'reason': 'text_too_short'}
        
        # Only flag as corrupted if we find genuine corruption patterns
        corruption_indicators = []
        
        # Check for placeholder patterns
        for pattern in self.problematic_patterns:
            if re.search(pattern, text):
                corruption_indicators.append(f'placeholder_pattern: {pattern}')
        
        # Check for excessive repetition (very conservative threshold)
        words = text.split()
        if len(words) > 5:
            word_freq = {}
            for word in words:
                if len(word) > 2:  # Only count meaningful words
                    word_freq[word.lower()] = word_freq.get(word.lower(), 0) + 1
            
            # Only flag if a word appears more than 30% of the time
            max_freq = max(word_freq.values()) if word_freq else 0
            repetition_ratio = max_freq / len(words) if len(words) > 0 else 0
            
            if repetition_ratio > 0.3:
                corruption_indicators.append(f'excessive_repetition: {repetition_ratio:.2f}')
        
        # Only consider corrupted if we have clear indicators
        is_corrupted = len(corruption_indicators) > 0
        
        if is_corrupted:
            cleaned_text = self.clean_tamil_text_optimized(text)
        else:
            cleaned_text = text
        
        return is_corrupted, cleaned_text, {
            'indicators': corruption_indicators,
            'confidence': len(corruption_indicators) * 0.3,  # Lower confidence scores
            'reason': 'conservative_detection'
        }
    
    def optimize_tamil_response_generation(self, context_docs: List, query: str) -> str:
        """
        Optimized response generation for Tamil queries.
        
        Args:
            context_docs: Retrieved documents
            query: Original Tamil query
            
        Returns:
            str: Optimized Tamil response
        """
        # Combine context more efficiently
        context_texts = []
        for doc in context_docs[:5]:  # Limit to top 5 for speed
            if hasattr(doc, 'metadata') and 'chunk_text' in doc.metadata:
                text = doc.metadata['chunk_text']
                # Only clean if absolutely necessary
                if any(re.search(pattern, text) for pattern in self.problematic_patterns):
                    text = self.clean_tamil_text_optimized(text)
                context_texts.append(text)
        
        context = '\n\n'.join(context_texts)
        
        # Return the context directly for now - in production, this would go to LLM
        return context
    
    def get_performance_stats(self) -> Dict:
        """Get current performance statistics."""
        return self.processing_stats.copy()

# Global instance
optimized_tamil_processor = OptimizedTamilProcessor()

def clean_text_content_optimized(content: str) -> str:
    """
    Drop-in replacement for the existing cleanTextContent function
    with optimized Tamil processing.
    """
    if not content or typeof content !== 'string':
        return content
    
    # Use optimized processor for Tamil content
    if optimized_tamil_processor.is_tamil_text(content):
        return optimized_tamil_processor.clean_tamil_text_optimized(content)
    
    # For non-Tamil content, use minimal cleaning
    return content.strip()

# Test function
def test_optimized_processing():
    """Test the optimized processing with your example text."""
    test_text = """நெருக்கமான மதிப்பீடுகள் 1995 இல் கண்கவர் செயல்திறன் மூலம் வெற்றி நிரூபிக்கப்பட்டுள்ளது, இது பாரிஸ் ஏர் ஷோவில் கண்கவர் செயல்திறன் திறனைக் காட்டியது, பாரம்பரிய ஸ்டால் கட்டுப்படுத்தப்படுகிறது அப்பால் (*ஸ்டால.30.30ப.30.30ட.30.30எஃப், பக.30.208, 354*).112. 3. 4. நெறிப்படுத்தப்பட்ட விவரக்குறிப்புகள் நடைமுறைகள் அதிகாரத்துவ. இணை இருப்பிட குழுக்கள் தகவல்தொடர்பு சிக்கலைத் 220, 336*). ### முதன்மை குறிக்கோள்கள் குறிக்கோள்கள் 1. 2.208, 354*).1127ஒத்துழைப்பு செயல்திறனை. யு. ஆயினும்கூட, மரபு நவீன சூப்பர்மேன்யூவலபிள். (ஆதாரங்கள்:பறக்கும் பி. டி. எஃப்*, 220, 235, 336,"""
    
    processor = OptimizedTamilProcessor()
    
    print("Original text length:", len(test_text))
    print("Original text:", test_text[:100] + "...")
    
    start_time = time.time()
    is_corrupted, cleaned_text, details = processor.detect_tamil_corruption_conservative(test_text)
    processing_time = time.time() - start_time
    
    print(f"\nProcessing time: {processing_time:.4f} seconds")
    print(f"Corruption detected: {is_corrupted}")
    print(f"Cleaned text length: {len(cleaned_text)}")
    print(f"Content preserved: {len(cleaned_text)/len(test_text)*100:.1f}%")
    print(f"Cleaned text: {cleaned_text[:200]}...")
    print(f"Corruption details: {details}")

if __name__ == "__main__":
    test_optimized_processing()