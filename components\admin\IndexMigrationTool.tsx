/**
 * Index Migration Tool Component
 * 
 * This component helps users migrate their existing FAISS indexes to the new
 * email-based filtering system by associating them with their email in the PINE collection.
 */

import React, { useState, useEffect } from 'react';

// Backend configuration
const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || process.env.BACKEND_URL || 'http://localhost:5010';

interface MigrationResponse {
  success: boolean;
  message: string;
  migrated_count: number;
  total_available: number;
  migrated_indexes: string[];
  errors?: string[];
  error?: string;
}

interface DebugResponse {
  success: boolean;
  total_records: number;
  records: Array<{
    id: number;
    index_name: string;
    email: string;
    upload_date: string;
  }>;
  error?: string;
}

const IndexMigrationTool: React.FC = () => {
  const [userEmail, setUserEmail] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [migrationResult, setMigrationResult] = useState<MigrationResponse | null>(null);
  const [debugData, setDebugData] = useState<DebugResponse | null>(null);
  const [showDebugData, setShowDebugData] = useState<boolean>(false);

  // Get user email from localStorage on component mount
  useEffect(() => {
    const email = localStorage.getItem('user_email');
    if (email) {
      setUserEmail(email);
    }
  }, []);

  // Function to migrate user indexes
  const handleMigrateIndexes = async () => {
    if (!userEmail.trim()) {
      alert('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setMigrationResult(null);

    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/migrate-user-indexes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: userEmail.trim() })
      });

      const data: MigrationResponse = await response.json();
      setMigrationResult(data);

      if (data.success) {
        // Refresh the page or update the index list
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      setMigrationResult({
        success: false,
        message: 'Failed to migrate indexes',
        migrated_count: 0,
        total_available: 0,
        migrated_indexes: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to debug PINE collection
  const handleDebugCollection = async () => {
    setIsLoading(true);
    setDebugData(null);

    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/debug-pine-collection`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const data: DebugResponse = await response.json();
      setDebugData(data);
      setShowDebugData(true);
    } catch (error) {
      setDebugData({
        success: false,
        total_records: 0,
        records: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
        🔧 Index Migration Tool
      </h2>
      
      <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
        <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
          ⚠️ Why do I need this?
        </h3>
        <p className="text-yellow-700 dark:text-yellow-300 text-sm">
          If you're seeing all indexes instead of just your own, it means your existing indexes 
          need to be migrated to the new email-based filtering system. This tool will associate 
          your existing FAISS indexes with your email address for proper data isolation.
        </p>
      </div>

      {/* Email Input Section */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Your Email Address
        </label>
        <input
          type="email"
          value={userEmail}
          onChange={(e) => setUserEmail(e.target.value)}
          placeholder="Enter your email address"
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                   bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                   focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Migration Button */}
      <div className="mb-6">
        <button
          onClick={handleMigrateIndexes}
          disabled={isLoading || !userEmail.trim()}
          className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 
                   disabled:opacity-50 disabled:cursor-not-allowed
                   flex items-center gap-2"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Migrating...
            </>
          ) : (
            <>
              🚀 Migrate My Indexes
            </>
          )}
        </button>
      </div>

      {/* Migration Result */}
      {migrationResult && (
        <div className={`mb-6 p-4 rounded-lg ${
          migrationResult.success 
            ? 'bg-green-50 dark:bg-green-900 text-green-700 dark:text-green-300'
            : 'bg-red-50 dark:bg-red-900 text-red-700 dark:text-red-300'
        }`}>
          <h3 className="font-semibold mb-2">
            {migrationResult.success ? '✅ Migration Successful!' : '❌ Migration Failed'}
          </h3>
          <p className="mb-2">{migrationResult.message}</p>
          
          {migrationResult.success && (
            <div className="text-sm">
              <p>• Migrated {migrationResult.migrated_count} out of {migrationResult.total_available} available indexes</p>
              {migrationResult.migrated_indexes.length > 0 && (
                <p>• Migrated indexes: {migrationResult.migrated_indexes.join(', ')}</p>
              )}
              <p className="mt-2 font-medium">🔄 Page will refresh automatically in 2 seconds...</p>
            </div>
          )}
          
          {migrationResult.errors && migrationResult.errors.length > 0 && (
            <div className="mt-2">
              <p className="font-medium">Errors:</p>
              <ul className="list-disc list-inside text-sm">
                {migrationResult.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Debug Section */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">
          🔍 Debug Information
        </h3>
        
        <button
          onClick={handleDebugCollection}
          disabled={isLoading}
          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 
                   disabled:opacity-50 disabled:cursor-not-allowed mb-4"
        >
          {isLoading ? 'Loading...' : 'View PINE Collection Data'}
        </button>

        {showDebugData && debugData && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            {debugData.success ? (
              <div>
                <h4 className="font-semibold mb-2 text-gray-800 dark:text-gray-200">
                  PINE Collection Records ({debugData.total_records} total)
                </h4>
                
                {debugData.records.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-300 dark:border-gray-600">
                          <th className="text-left py-2 text-gray-700 dark:text-gray-300">ID</th>
                          <th className="text-left py-2 text-gray-700 dark:text-gray-300">Index Name</th>
                          <th className="text-left py-2 text-gray-700 dark:text-gray-300">Email</th>
                          <th className="text-left py-2 text-gray-700 dark:text-gray-300">Upload Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {debugData.records.map((record) => (
                          <tr key={record.id} className="border-b border-gray-200 dark:border-gray-600">
                            <td className="py-2 text-gray-600 dark:text-gray-400">{record.id}</td>
                            <td className="py-2 text-gray-800 dark:text-gray-200 font-mono">{record.index_name}</td>
                            <td className="py-2 text-gray-600 dark:text-gray-400">{record.email}</td>
                            <td className="py-2 text-gray-600 dark:text-gray-400">{record.upload_date}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-600 dark:text-gray-400">No records found in PINE collection.</p>
                )}
              </div>
            ) : (
              <div className="text-red-600 dark:text-red-400">
                Error: {debugData.error}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
        <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
          📋 How to use this tool:
        </h4>
        <ol className="text-blue-700 dark:text-blue-300 text-sm space-y-1 list-decimal list-inside">
          <li>Enter your email address (the one you use to log in)</li>
          <li>Click "Migrate My Indexes" to associate existing indexes with your email</li>
          <li>The page will refresh automatically after successful migration</li>
          <li>You should now see only your own indexes in the interface</li>
          <li>Use "View PINE Collection Data" to verify the migration worked</li>
        </ol>
      </div>
    </div>
  );
};

export default IndexMigrationTool;
