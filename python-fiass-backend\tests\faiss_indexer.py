import os
import json
import datetime
import faiss
import numpy as np
import pandas as pd
import gc
from dotenv import load_dotenv
from langchain_huggingface.embeddings import HuggingFaceEmbeddings

# Load environment variables
load_dotenv()

# Configuration
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
EXCEL_FILE_PATH     = os.getenv("EXCEL_FILE_PATH", os.path.join(SCRIPT_DIR, "dinamalar_dataset_first_500.xlsx"))
OUTPUT_DIR          = os.path.join(SCRIPT_DIR, "faiss_data", "default")
INDEX_SAVE_PATH     = os.path.join(OUTPUT_DIR, "news_index.faiss")
METADATA_SAVE_PATH  = os.path.join(OUTPUT_DIR, "news_metadata.json")
EMBED_MODEL         = "all-MiniLM-L6-v2"
NLIST               = 100
BATCH_SIZE          = 100  # vector batch size
CHUNK_SIZE          = 1000  # Excel chunk read size

os.makedirs(OUTPUT_DIR, exist_ok=True)

print("🤖 Initializing embedding model...")
embedder = HuggingFaceEmbeddings(model_name=EMBED_MODEL)

# Determine embedding dimension
dummy_embedding = embedder.embed_documents(["hello world"])
dim = len(dummy_embedding[0])
print(f"📐 Embedding dimension: {dim}")

quantizer = faiss.IndexFlatIP(dim)
index = faiss.IndexIVFFlat(quantizer, dim, NLIST, faiss.METRIC_INNER_PRODUCT)

metadata_store = []
train_vectors = []
index_trained = False

def chunk_text(text: str, size: int = 500):
    chunks, start = [], 0
    while start < len(text):
        end = min(start + size, len(text))
        if end < len(text):
            while end > start and not text[end].isspace():
                end -= 1
            if end == start:
                end = min(start + size, len(text))
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        start = end
    return chunks

def process_batch(batch_vectors, batch_metadata):
    if not batch_vectors:
        return
    xb = np.vstack(batch_vectors).astype('float32')
    faiss.normalize_L2(xb)
    global index_trained

    if not index.is_trained and not index_trained:
        print("🎯 Training FAISS IVF index...")
        train_sample = np.vstack(train_vectors)[:min(10000, len(train_vectors))].astype('float32')
        faiss.normalize_L2(train_sample)
        index.train(train_sample)
        index_trained = True

    index.add(xb)
    metadata_store.extend(batch_metadata)
    print(f"✅ Processed batch: {len(batch_vectors)} vectors added")

# Start processing
print("⚙️  Building FAISS index from Excel file…")
try:
    print(f"🔍 Reading Excel file: {EXCEL_FILE_PATH}")
    # Read the entire Excel file since chunksize is not supported
    df = pd.read_excel(EXCEL_FILE_PATH, engine='openpyxl')
    print(f"📊 Columns: {list(df.columns)}")
    print(f"📈 Total rows: {len(df)}")

except Exception as e:
    print(f"❌ Error reading Excel file: {e}")
    print("💡 Ensure file exists and `openpyxl` is installed.")
    exit(1)

# Process data in chunks manually
row_offset = 0
total_rows = len(df)

for start_idx in range(0, total_rows, CHUNK_SIZE):
    end_idx = min(start_idx + CHUNK_SIZE, total_rows)
    chunk = df.iloc[start_idx:end_idx]
    print(f"🔄 Processing chunk {start_idx//CHUNK_SIZE + 1}: rows {start_idx+1} to {end_idx}")
    batch_vectors = []
    batch_metadata = []

    for row_idx, row in chunk.iterrows():
        text = ""
        for col in ["news_article", "news_title", "Content", "content", "Text", "text", "Article", "article", "News", "news"]:
            if col in chunk.columns:
                val = str(row.get(col, "")).strip()
                if val and val.lower() != "nan":
                    text = val
                    break

        # Extract title and article separately for metadata storage
        news_title = "N/A"
        news_article = "N/A"

        # Extract news title
        if "news_title" in chunk.columns:
            title_val = str(row.get("news_title", "")).strip()
            if title_val and title_val.lower() != "nan":
                news_title = title_val

        # Extract news article
        if "news_article" in chunk.columns:
            article_val = str(row.get("news_article", "")).strip()
            if article_val and article_val.lower() != "nan":
                news_article = article_val

        # Combine title + article if available for embedding text
        if "news_title" in chunk.columns and "news_article" in chunk.columns:
            title = str(row.get("news_title", "")).strip()
            article = str(row.get("news_article", "")).strip()
            if title.lower() != "nan" and article.lower() != "nan":
                text = f"{title}. {article}"
            elif title.lower() != "nan":
                text = title
            elif article.lower() != "nan":
                text = article

        if not text or text.lower() == "nan":
            print(f"⚠️  Skipping row {start_idx + row_idx}: No content")
            continue

        category = "unknown"
        for col in ["news_category", "Sentiment", "sentiment", "Category", "category", "Label", "label"]:
            if col in chunk.columns:
                val = str(row.get(col, "")).strip()
                if val and val.lower() != "nan":
                    category = val
                    break

        record_date = datetime.datetime.now().isoformat()
        chunks = chunk_text(text)

        for chunk_idx, chunk_text_str in enumerate(chunks):
            vec_list = embedder.embed_documents([chunk_text_str])[0]
            vec = np.array(vec_list, dtype="float32")
            batch_vectors.append(vec)
            train_vectors.append(vec)

            url = "N/A"
            for col in ["URL", "url", "Link", "link", "Source", "source"]:
                if col in chunk.columns:
                    url_val = str(row.get(col, "")).strip()
                    if url_val and url_val.lower() != "nan":
                        url = url_val
                        break

            summary = "N/A"
            for col in ["Summary", "summary", "Abstract", "abstract", "Description", "description"]:
                if col in chunk.columns:
                    sum_val = str(row.get(col, "")).strip()
                    if sum_val and sum_val.lower() != "nan":
                        summary = sum_val
                        break

            batch_metadata.append({
                "chunk_text": chunk_text_str,
                "title": news_title,  # Unified title field for consistency with other processors
                "news_title": news_title,
                "news_article": news_article,
                "record_date": record_date,
                "category": category,
                "url": url,
                "summary": summary,
                "vector_id": f"news-{start_idx + row_idx}-chunk-{chunk_idx}",
                "source_type": "news",
                "upload_source": "excel_upload"
            })

            print(f"🔤 Embedded chunk: {chunk_text_str[:60]}...")

        # Process batch if size reached
        if len(batch_vectors) >= BATCH_SIZE:
            process_batch(batch_vectors, batch_metadata)
            batch_vectors.clear()
            batch_metadata.clear()
            gc.collect()

    # Final batch in chunk
    process_batch(batch_vectors, batch_metadata)
    gc.collect()

if index.ntotal == 0:
    print("❌ No vectors were created! Ensure content columns exist.")
    exit(1)

faiss.write_index(index, INDEX_SAVE_PATH)
print(f"🧠 FAISS index saved at: {INDEX_SAVE_PATH}")

with open(METADATA_SAVE_PATH, "w", encoding="utf-8") as f:
    json.dump(metadata_store, f, ensure_ascii=False, indent=2)
print(f"🗃️  Metadata saved at: {METADATA_SAVE_PATH}")

print(f"✅ Finished: {index.ntotal} total vectors indexed.")
