#!/usr/bin/env python3
"""
Setup script for Enhanced Indian Language Translation Models
This script will download and cache the required models for better performance
"""

import os
import sys
import subprocess
import torch
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    
    requirements = [
        "torch>=1.9.0",
        "transformers>=4.21.0", 
        "sentencepiece>=0.1.97",
        "datasets>=2.0.0",
        "accelerate>=0.20.0",
        "sacremoses>=0.0.53",
        "tokenizers>=0.13.0",
        "psutil>=5.8.0",
        "tqdm>=4.64.0"
    ]
    
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", req])
            print(f"✅ Installed: {req}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {req}: {e}")
            return False
    
    return True

def download_models():
    """Download and cache the translation models"""
    print("🤖 Downloading Indian language translation models...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForSeq2SeqLM, MBart50TokenizerFast, MBartForConditionalGeneration
        
        models_to_download = [
            {
                "name": "AI4Bharat IndicTrans2",
                "model_name": "ai4bharat/indictrans2-en-indic-1B",
                "tokenizer_class": AutoTokenizer,
                "model_class": AutoModelForSeq2SeqLM
            },
            {
                "name": "Facebook mBART-50",
                "model_name": "facebook/mbart-large-50-many-to-many-mmt", 
                "tokenizer_class": MBart50TokenizerFast,
                "model_class": MBartForConditionalGeneration
            }
        ]
        
        for model_info in models_to_download:
            try:
                print(f"📥 Downloading {model_info['name']}...")
                
                # Download tokenizer
                tokenizer = model_info["tokenizer_class"].from_pretrained(
                    model_info["model_name"],
                    trust_remote_code=True if "indictrans2" in model_info["model_name"] else False
                )
                print(f"✅ Tokenizer downloaded: {model_info['name']}")
                
                # Download model (this will cache it locally)
                model = model_info["model_class"].from_pretrained(
                    model_info["model_name"],
                    trust_remote_code=True if "indictrans2" in model_info["model_name"] else False
                )
                print(f"✅ Model downloaded: {model_info['name']}")
                
                # Clear memory
                del model
                del tokenizer
                
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    
            except Exception as e:
                print(f"❌ Failed to download {model_info['name']}: {e}")
                print("⚠️ This model will be downloaded on first use")
        
        print("✅ Model download process completed!")
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        return False
    except Exception as e:
        print(f"❌ Error downloading models: {e}")
        return False

def test_translation_service():
    """Test the enhanced translation service"""
    print("🧪 Testing enhanced translation service...")
    
    try:
        from services.enhanced_indian_translation import enhanced_indian_translator
        
        # Test cases
        test_cases = [
            ("Hello world", "en", "ta", "Tamil"),
            ("Good morning", "en", "te", "Telugu"), 
            ("Thank you", "en", "kn", "Kannada"),
            ("How are you?", "en", "or", "Oriya")
        ]
        
        success_count = 0
        
        for text, src, tgt, lang_name in test_cases:
            try:
                print(f"🔄 Testing {lang_name} translation: '{text}'")
                result = enhanced_indian_translator.translate_with_indian_models(text, src, tgt)
                
                if result.get("success"):
                    print(f"✅ {lang_name}: {result['translated_text']}")
                    print(f"   Model: {result['model_used']}, Time: {result['translation_time']}s")
                    success_count += 1
                else:
                    print(f"❌ {lang_name} translation failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ Error testing {lang_name}: {e}")
        
        print(f"\n📊 Test Results: {success_count}/{len(test_cases)} translations successful")
        
        # Print statistics
        stats = enhanced_indian_translator.get_translation_statistics()
        print(f"📈 Translation Statistics: {stats}")
        
        return success_count > 0
        
    except ImportError:
        print("❌ Enhanced translation service not available")
        return False
    except Exception as e:
        print(f"❌ Error testing translation service: {e}")
        return False

def check_system_requirements():
    """Check system requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("❌ Python 3.7+ required")
        return False
    print(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check available memory
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"💾 Available RAM: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️ Warning: Less than 4GB RAM available. Models may run slowly.")
        elif memory_gb >= 8:
            print("✅ Sufficient RAM for optimal performance")
    except ImportError:
        print("⚠️ Could not check memory requirements")
    
    # Check GPU availability
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
        print(f"🚀 GPU available: {gpu_name} ({gpu_count} device(s))")
        print("✅ GPU acceleration will be used for faster translations")
    else:
        print("💻 No GPU detected - using CPU (translations will be slower)")
    
    return True

def main():
    """Main setup function"""
    print("🚀 Setting up Enhanced Indian Language Translation Service")
    print("=" * 60)
    
    # Check system requirements
    if not check_system_requirements():
        print("❌ System requirements not met")
        return False
    
    print("\n" + "=" * 60)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        return False
    
    print("\n" + "=" * 60)
    
    # Download models
    if not download_models():
        print("⚠️ Some models failed to download but service may still work")
    
    print("\n" + "=" * 60)
    
    # Test the service
    if test_translation_service():
        print("\n🎉 Setup completed successfully!")
        print("✅ Enhanced Indian language translation service is ready to use")
        
        print("\n📋 Next steps:")
        print("1. Restart your Flask application")
        print("2. The enhanced translation will be used automatically for Tamil, Telugu, Kannada, and Oriya")
        print("3. Monitor the logs for translation performance improvements")
        
        return True
    else:
        print("\n⚠️ Setup completed with warnings")
        print("The service is installed but some features may not work optimally")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)