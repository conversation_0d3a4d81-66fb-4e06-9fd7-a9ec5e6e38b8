// Debug script to understand the placeholder issue

const testText = `வழங்கப்பட்ட சூழலின் சூழலின், __capital_word_0lsetwvr__ பணிக்குழு (__capital_word_0lsetwvr__wg) முதலில் ஒரு விரிவான பகுப்பாய்வு திட்டமிடல் செயல்முறை விமானத்தின் வடிவமைப்பை மாற்ற.`;

console.log("Original text:", testText);
console.log("Contains __capital_word_:", testText.includes("__capital_word_"));
console.log("Contains lsetwvr:", testText.includes("lsetwvr"));

// Extract all placeholder patterns
const placeholderPattern = /__capital_word_\w+__/gi;
const placeholders = testText.match(placeholderPattern);
console.log("Found placeholders:", placeholders);

// Check if these are backend placeholders (999{i}999 pattern)
const backendPlaceholderPattern = /999\d+999/g;
const backendPlaceholders = testText.match(backendPlaceholderPattern);
console.log("Found backend placeholders:", backendPlaceholders);

// Check for continuous capital words that should be preserved
const continuousCapitalPattern = /\b[A-Z]{2,}\b/g;
const capitalWords = testText.match(continuousCapitalPattern);
console.log("Found continuous capital words:", capitalWords);

// Simulate the cleanTextContent function logic
function simulateCleanTextContent(content) {
    console.log("\n--- Simulating cleanTextContent ---");
    
    // Check if the content already contains backend placeholders (999{i}999 pattern)
    const backendPlaceholderRegex = /999\d+999/g;
    const hasBackendPlaceholders = backendPlaceholderRegex.test(content);
    
    console.log("Has backend placeholders:", hasBackendPlaceholders);
    
    // Check for the mysterious placeholders
    const mysteriousPlaceholderRegex = /__capital_word_\w+__/g;
    const hasMysteriousPlaceholders = mysteriousPlaceholderRegex.test(content);
    
    console.log("Has mysterious placeholders:", hasMysteriousPlaceholders);
    
    if (hasMysteriousPlaceholders) {
        console.log("❌ ISSUE: Text contains unknown placeholder format!");
        console.log("Expected backend format: 999{i}999");
        console.log("Found format: __capital_word_{random}__");
        
        // Try to extract what the original words might have been
        const placeholderMatches = content.match(mysteriousPlaceholderRegex);
        console.log("Mysterious placeholders found:", placeholderMatches);
        
        // This suggests the placeholders are not being restored properly
        // or they're coming from a different source
    }
    
    return content;
}

simulateCleanTextContent(testText);

console.log("\n--- Analysis ---");
console.log("1. The text contains placeholders in format: __capital_word_{random}__");
console.log("2. This is NOT the backend format (999{i}999)");
console.log("3. This suggests either:");
console.log("   a) Frontend is creating its own placeholders");
console.log("   b) There's another system processing the text");
console.log("   c) The placeholders are not being restored properly");
console.log("4. The 'lsetwvr' part looks like a random string identifier");