import React from 'react';

const NewsCardSkeleton: React.FC = () => {
  return (
    <div className="border border-primaryColor/20 p-0 rounded-lg overflow-hidden bg-white dark:bg-n0">
      <div className="h-[200px] w-full bg-primaryColor/5 animate-pulse"></div>
      <div className="p-5">
        <div className="flex justify-between items-center mb-3">
          <div className="h-4 w-24 bg-primaryColor/5 animate-pulse rounded-md"></div>
          <div className="h-6 w-24 bg-primaryColor/5 animate-pulse rounded-md"></div>
        </div>
        <div className="h-6 w-full bg-primaryColor/5 animate-pulse rounded-md mb-3"></div>
        <div className="h-4 w-full bg-primaryColor/5 animate-pulse rounded-md mb-1"></div>
        <div className="h-4 w-full bg-primaryColor/5 animate-pulse rounded-md mb-1"></div>
        <div className="h-4 w-3/4 bg-primaryColor/5 animate-pulse rounded-md mb-4"></div>
        <div className="h-5 w-32 bg-primaryColor/5 animate-pulse rounded-md"></div>
      </div>
    </div>
  );
};

export default NewsCardSkeleton;
