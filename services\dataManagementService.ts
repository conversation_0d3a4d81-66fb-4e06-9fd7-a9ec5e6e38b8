/**
 * Data Management Service for FAISS Index Data
 * Handles fetching, filtering, and exporting JSON data from FAISS indexes
 */

// Backend configuration
const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || process.env.BACKEND_URL || 'http://localhost:5010';

export interface IndexDataRow {
  id: string;
  [key: string]: any;
}

export interface DataFetchResponse {
  success: boolean;
  data: IndexDataRow[];
  total: number;
  error?: string;
}

export interface ExportOptions {
  format: 'csv' | 'json';
  filename?: string;
  filteredData?: IndexDataRow[];
}

/**
 * Fetch JSON data for a specific FAISS index
 * @param indexName - Name of the FAISS index
 * @param limit - Maximum number of rows to fetch (default: 1000)
 * @param offset - Number of rows to skip (default: 0)
 * @returns Promise with data fetch response
 */
export const fetchIndexData = async (
  indexName: string,
  limit: number = 1000,
  offset: number = 0
): Promise<DataFetchResponse> => {
  try {
    // First try to get the JSON metadata file directly
    const response = await fetch(`${BACKEND_BASE_URL}/api/get-faiss-metadata/${indexName}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.success) {
      // Transform the data to ensure each row has a unique ID
      const transformedData = result.data.map((row: any, index: number) => ({
        id: row.id || `row_${offset + index}`,
        ...row
      }));

      return {
        success: true,
        data: transformedData,
        total: result.total || transformedData.length
      };
    } else {
      return {
        success: false,
        data: [],
        total: 0,
        error: result.error || 'Failed to fetch data'
      };
    }
  } catch (error) {
    console.error('Error fetching index data:', error);
    return {
      success: false,
      data: [],
      total: 0,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete specific rows from a FAISS index
 * @param indexName - Name of the FAISS index
 * @param rowIds - Array of row IDs to delete
 * @returns Promise with deletion result
 */
export const deleteIndexRows = async (
  indexName: string,
  rowIds: string[]
): Promise<{ success: boolean; deletedCount: number; error?: string }> => {
  try {
    const response = await fetch(`${BACKEND_BASE_URL}/api/delete-faiss-rows/${indexName}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        row_ids: rowIds
      })
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        deletedCount: result.deleted_count || rowIds.length
      };
    } else {
      return {
        success: false,
        deletedCount: 0,
        error: result.error || 'Failed to delete rows'
      };
    }
  } catch (error) {
    console.error('Error deleting index rows:', error);
    return {
      success: false,
      deletedCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Export data to CSV format
 * @param data - Array of data rows to export
 * @param filename - Optional filename for the export
 */
export const exportToCSV = (data: IndexDataRow[], filename?: string): void => {
  if (data.length === 0) {
    console.warn('No data to export');
    return;
  }

  // Get all unique keys from the data
  const allKeys = Array.from(new Set(data.flatMap(row => Object.keys(row))));
  
  // Create CSV header
  const csvHeader = allKeys.join(',');
  
  // Create CSV rows
  const csvRows = data.map(row => {
    return allKeys.map(key => {
      const value = row[key];
      // Handle values that might contain commas or quotes
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value || '';
    }).join(',');
  });

  // Combine header and rows
  const csvContent = [csvHeader, ...csvRows].join('\n');
  
  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename || `index_data_${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * Export data to JSON format
 * @param data - Array of data rows to export
 * @param filename - Optional filename for the export
 */
export const exportToJSON = (data: IndexDataRow[], filename?: string): void => {
  if (data.length === 0) {
    console.warn('No data to export');
    return;
  }

  const jsonContent = JSON.stringify(data, null, 2);
  
  // Create and download the file
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename || `index_data_${new Date().toISOString().split('T')[0]}.json`);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * Filter data based on search term across all fields
 * @param data - Array of data rows to filter
 * @param searchTerm - Search term to filter by
 * @returns Filtered array of data rows
 */
export const filterData = (data: IndexDataRow[], searchTerm: string): IndexDataRow[] => {
  if (!searchTerm.trim()) {
    return data;
  }

  const searchLower = searchTerm.toLowerCase().trim();
  
  return data.filter(row => {
    return Object.values(row).some(value => {
      if (value === null || value === undefined) return false;
      return String(value).toLowerCase().includes(searchLower);
    });
  });
};

/**
 * Get unique values for a specific column (for filter dropdowns)
 * @param data - Array of data rows
 * @param columnKey - Key of the column to get unique values for
 * @returns Array of unique values
 */
export const getUniqueColumnValues = (data: IndexDataRow[], columnKey: string): string[] => {
  const values = data
    .map(row => row[columnKey])
    .filter(value => value !== null && value !== undefined)
    .map(value => String(value));
  
  return Array.from(new Set(values)).sort();
};
