#!/usr/bin/env python3
"""
Enhanced Features Test Script
Tests the improved Character Diversity Analysis and Related Questions Generation
"""

import requests
import json
import time
import os
from datetime import datetime

# Configuration
BASE_URL = os.getenv("BACKEND_URL", "http://localhost:5010")
TEST_CASES = {
    "english": {
        "query": "What are the latest trends in cryptocurrency investments?",
        "answer": "Cryptocurrency investments are showing increased institutional adoption with Bitcoin and Ethereum leading the market. DeFi protocols are gaining traction, and regulatory clarity is improving investor confidence.",
        "language": "English"
    },
    "tamil": {
        "query": "கிரிப்டோகரன்சி முதலீடுகளில் சமீபத்திய போக்குகள் என்ன?",
        "answer": "கிரிப்டோகரன்சி முதலீடுகள் நிறுவன ஏற்றுக்கொள்ளலை அதிகரித்து காட்டுகின்றன. பிட்காயின் மற்றும் எதீரியம் சந்தையை வழிநடத்துகின்றன. DeFi நெறிமுறைகள் வேகம் பெறுகின்றன.",
        "language": "Tamil"
    },
    "telugu": {
        "query": "క్రిప్టోకరెన్సీ పెట్టుబడులలో తాజా ట్రెండ్స్ ఏమిటి?",
        "answer": "క్రిప్టోకరెన్సీ పెట్టుబడులు సంస్థాగత స్వీకరణను పెంచుతున్నాయి. బిట్‌కాయిన్ మరియు ఇథీరియం మార్కెట్‌ను నడిపిస్తున్నాయి.",
        "language": "Telugu"
    },
    "kannada": {
        "query": "ಕ್ರಿಪ್ಟೋಕರೆನ್ಸಿ ಹೂಡಿಕೆಗಳಲ್ಲಿ ಇತ್ತೀಚಿನ ಪ್ರವೃತ್ತಿಗಳು ಯಾವುವು?",
        "answer": "ಕ್ರಿಪ್ಟೋಕರೆನ್ಸಿ ಹೂಡಿಕೆಗಳು ಸಾಂಸ್ಥಿಕ ಅಳವಡಿಕೆಯನ್ನು ಹೆಚ್ಚಿಸುತ್ತಿವೆ. ಬಿಟ್‌ಕಾಯಿನ್ ಮತ್ತು ಇಥೀರಿಯಂ ಮಾರುಕಟ್ಟೆಯನ್ನು ಮುನ್ನಡೆಸುತ್ತಿವೆ.",
        "language": "Kannada"
    }
}

CORRUPTION_TEST_CASES = [
    {
        "name": "Normal English Text",
        "text": "The financial markets are showing strong performance with diversified portfolios outperforming single-sector investments.",
        "expected_corrupted": False
    },
    {
        "name": "Normal Tamil Text", 
        "text": "நிதிச் சந்தைகள் வலுவான செயல்திறனைக் காட்டுகின்றன. பல்வகைப்படுத்தப்பட்ட போர்ட்ஃபோலியோக்கள் ஒற்றைத் துறை முதலீடுகளை விட சிறப்பாக செயல்படுகின்றன.",
        "expected_corrupted": False
    },
    {
        "name": "Repetitive Text (Corrupted)",
        "text": "The the the market market market is is is showing showing showing good good good performance performance performance.",
        "expected_corrupted": True
    },
    {
        "name": "Low Diversity Text (Corrupted)",
        "text": "aaaaaaaaaa bbbbbbbbbb cccccccccc dddddddddd eeeeeeeeee",
        "expected_corrupted": True
    },
    {
        "name": "Mixed Script Text",
        "text": "The market முதலீடு is showing good performance మంచి results.",
        "expected_corrupted": False
    }
]

def test_related_questions():
    """Test the enhanced related questions generation"""
    print("🧪 Testing Enhanced Related Questions Generation")
    print("=" * 60)
    
    for lang_name, test_case in TEST_CASES.items():
        print(f"\n📝 Testing {lang_name.upper()} Language:")
        print(f"Query: {test_case['query'][:50]}...")
        print(f"Answer: {test_case['answer'][:50]}...")
        
        # Prepare test context
        context_data = {
            'retrieved_documents': [
                {
                    'rank': 1,
                    'score': '92%',
                    'date': '2024-01-15',
                    'category': 'Cryptocurrency',
                    'text': 'Bitcoin reaches new all-time high as institutional adoption increases.'
                },
                {
                    'rank': 2,
                    'score': '87%',
                    'date': '2024-01-14',
                    'category': 'DeFi',
                    'text': 'Decentralized finance protocols show 300% growth in total value locked.'
                }
            ],
            'index_used': 'crypto_financial_data',
            'search_engine': 'FAISS',
            'has_uploaded_content': True,
            'upload_sources': ['Crypto Market Report 2024', 'DeFi Analysis'],
            'query_language': test_case['language'],
            'response_language': test_case['language'],
            'data_language': 'English'
        }
        
        payload = {
            'query': test_case['query'],
            'answer': test_case['answer'],
            'language': test_case['language'],
            'context': context_data
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/test_related_questions", json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ Success! Generated {result['results']['questions_count']} questions")
                    
                    # Display related questions
                    for i, question in enumerate(result['results']['related_questions'], 1):
                        print(f"   {i}. {question}")
                    
                    # Display character analysis
                    char_analysis = result['character_analysis']
                    print(f"\n📊 Character Analysis:")
                    print(f"   Script Type: {char_analysis['script_type']}")
                    print(f"   Diversity Ratio: {char_analysis['diversity_ratio']:.3f}")
                    print(f"   Normalized Diversity: {char_analysis['normalized_diversity']:.3f}")
                    print(f"   Complexity Score: {char_analysis['complexity_score']:.3f}")
                    print(f"   Corruption Status: {'❌ Corrupted' if char_analysis['is_corrupted'] else '✅ Clean'}")
                    
                else:
                    print(f"❌ API Error: {result.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request Error: {str(e)}")
        
        time.sleep(1)  # Rate limiting

def test_character_diversity():
    """Test the enhanced character diversity analysis"""
    print("\n\n🔍 Testing Enhanced Character Diversity Analysis")
    print("=" * 60)
    
    for test_case in CORRUPTION_TEST_CASES:
        print(f"\n📝 Testing: {test_case['name']}")
        print(f"Text: {test_case['text'][:50]}...")
        print(f"Expected Corrupted: {test_case['expected_corrupted']}")
        
        payload = {'text': test_case['text']}
        
        try:
            response = requests.post(f"{BASE_URL}/api/test_character_diversity", json=payload, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    analysis = result['analysis']
                    corruption = result['corruption_detection']
                    
                    print(f"📊 Analysis Results:")
                    print(f"   Script Type: {analysis['script_type']}")
                    print(f"   Basic Diversity: {analysis['basic_diversity']:.3f}")
                    print(f"   Normalized Diversity: {analysis['normalized_diversity']:.3f}")
                    print(f"   Complexity Score: {analysis['complexity_score']:.3f}")
                    print(f"   Entropy: {analysis['character_distribution']['entropy']:.3f}")
                    print(f"   Repetition Score: {analysis['character_distribution']['repetition_score']:.3f}")
                    
                    print(f"\n🔍 Corruption Detection:")
                    print(f"   Is Corrupted: {corruption['is_corrupted']}")
                    print(f"   Corruption Score: {corruption['corruption_score']:.3f}")
                    print(f"   Confidence: {corruption['confidence']:.3f}")
                    
                    # Check if prediction matches expectation
                    if corruption['is_corrupted'] == test_case['expected_corrupted']:
                        print(f"   ✅ Prediction matches expectation!")
                    else:
                        print(f"   ❌ Prediction mismatch! Expected: {test_case['expected_corrupted']}, Got: {corruption['is_corrupted']}")
                    
                    if corruption['indicators']:
                        print(f"   Corruption Indicators: {[ind['type'] for ind in corruption['indicators']]}")
                    
                else:
                    print(f"❌ API Error: {result.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request Error: {str(e)}")
        
        time.sleep(0.5)  # Rate limiting

def test_financial_query_integration():
    """Test the integration with the actual financial_query endpoint"""
    print("\n\n💰 Testing Financial Query Integration")
    print("=" * 60)
    
    test_queries = [
        {
            "query": "What are the best investment strategies for 2024?",
            "language": "English"
        },
        {
            "query": "2024 ஆம் ஆண்டுக்கான சிறந்த முதலீட்டு உத்திகள் என்ன?",
            "language": "Tamil"
        }
    ]
    
    for test_query in test_queries:
        print(f"\n📝 Testing Financial Query in {test_query['language']}:")
        print(f"Query: {test_query['query']}")
        
        payload = {
            'query': test_query['query'],
            'language': test_query['language'],
            'enable_translation': True
        }
        
        try:
            response = requests.post(f"{BASE_URL}/financial_query", json=payload, timeout=45)
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Financial Query Success!")
                print(f"   AI Response Length: {len(result.get('ai_response', ''))}")
                print(f"   Related Questions Count: {len(result.get('related_questions', []))}")
                print(f"   Retrieved Documents: {len(result.get('retrieved_documents', []))}")
                print(f"   Index Used: {result.get('index_used', 'N/A')}")
                print(f"   Search Engine: {result.get('search_engine', 'N/A')}")
                
                # Display first few related questions
                related_questions = result.get('related_questions', [])
                if related_questions:
                    print(f"\n🤔 Sample Related Questions:")
                    for i, question in enumerate(related_questions[:3], 1):
                        print(f"   {i}. {question}")
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                if response.text:
                    print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request Error: {str(e)}")
        
        time.sleep(2)  # Rate limiting for financial queries

def main():
    """Run all tests"""
    print("🚀 Enhanced Features Test Suite")
    print("Testing Character Diversity Analysis and Related Questions Generation")
    print("=" * 80)
    print(f"Base URL: {BASE_URL}")
    print(f"Test Started: {datetime.now().isoformat()}")
    
    try:
        # Test 1: Related Questions Generation
        test_related_questions()
        
        # Test 2: Character Diversity Analysis
        test_character_diversity()
        
        # Test 3: Financial Query Integration
        test_financial_query_integration()
        
        print("\n\n🎉 All Tests Completed!")
        print(f"Test Finished: {datetime.now().isoformat()}")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Tests interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test suite error: {str(e)}")

if __name__ == "__main__":
    main()