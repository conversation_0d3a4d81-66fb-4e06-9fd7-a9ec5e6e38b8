import React from 'react';

interface SourceReference {
  sentence: string;
  source_title: string;
  source_type: string;
  file_id?: string;
  page?: string;
  summary: string;
  url: string;
}

interface ReferenceLabels {
  source: string;
  references: string;
  page: string;
  document: string;
  summary: string;
}

interface PerplexityStyleResponseProps {
  response: string;
  sentence_analysis?: SourceReference[];
  data_language?: string;
  query_language?: string;
  reference_labels?: ReferenceLabels;
}

const PerplexityStyleResponse: React.FC<PerplexityStyleResponseProps> = ({ 
  response, 
  sentence_analysis = [], 
  data_language = 'English',
  query_language = 'English',
  reference_labels
}) => {
  
  // Language-specific labels
  const getLanguageLabels = (language: string) => {
    const labels = {
      'English': {
        source: 'Source',
        references: 'References',
        page: 'Page',
        document: 'Document',
        summary: 'Summary'
      },
      'Tamil': {
        source: 'மூலம்',
        references: 'குறிப்புகள்',
        page: 'பக்கம்',
        document: 'ஆவணம்',
        summary: 'சுருக்கம்'
      },
      'Telugu': {
        source: 'మూలం',
        references: 'సూచనలు',
        page: 'పేజీ',
        document: 'పత్రం',
        summary: 'సారాంశం'
      },
      'Kannada': {
        source: 'ಮೂಲ',
        references: 'ಉಲ್ಲೇಖಗಳು',
        page: 'ಪುಟ',
        document: 'ದಾಖಲೆ',
        summary: 'ಸಾರಾಂಶ'
      },
      'Oriya': {
        source: 'ଉତ୍ସ',
        references: 'ସନ୍ଦର୍ଭ',
        page: 'ପୃଷ୍ଠା',
        document: 'ଦଲିଲ',
        summary: 'ସାରାଂଶ'
      },
      'Odia': {
        source: 'ଉତ୍ସ',
        references: 'ସନ୍ଦର୍ଭ',
        page: 'ପୃଷ୍ଠା',
        document: 'ଦଲିଲ',
        summary: 'ସାରାଂଶ'
      }
    };
    
    return labels[language] || labels['English'];
  };

  // Use backend-provided labels if available, otherwise fall back to local labels
  const labels = reference_labels || getLanguageLabels(data_language);
  const formatResponse = (text: string) => {
    if (!text) return null;

    // Split by double hash (##) for line breaks
    const sections = text.split('###').filter(section => section.trim());

    return sections.map((section, index) => {
      // Handle bullet points (lines starting with - or *)
      if (section.trim().startsWith('-') || section.trim().startsWith('*')) {
        const bulletPoints = section.split('\n').filter(line => line.trim());
        return (
          <ul key={index} className="list-disc pl-5 space-y-1">
            {bulletPoints.map((point, i) => (
              <li key={`${index}-${i}`}>{point.trim().substring(1).trim()}</li>
            ))}
          </ul>
        );
      }

      // Regular paragraph
      return (
        <p key={index} className="mb-2">
          {section.trim()}
        </p>
      );
    });
  };

  // Extract page number from direct page field, source title, or summary
  const extractPageNumber = (ref: SourceReference) => {
    // First, try to use the direct page field from FAISS data
    if (ref.page && ref.page !== "Unknown" && ref.page !== "N/A" && ref.page.trim() !== "") {
      return ref.page;
    }
    
    // Fall back to regex extraction from summary or source title
    const pageMatch = ref.summary.match(/page[:\s]*(\d+)/i) || ref.source_title.match(/page[:\s]*(\d+)/i);
    return pageMatch ? pageMatch[1] : null;
  };

  // Check if file_id is valid and should be displayed
  const isValidFileId = (fileId: string | undefined) => {
    return fileId && fileId !== "Unknown" && fileId !== "N/A" && fileId.trim() !== "";
  };

  // Render source references
  const renderReferences = () => {
    if (!sentence_analysis || sentence_analysis.length === 0) {
      return null;
    }

    // Group references by source
    const groupedRefs = sentence_analysis.reduce((acc, ref) => {
      // Use a more descriptive key for unknown sources
      const key = ref.source_title === "Unknown" ? "Unknown Source" : ref.source_title;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(ref);
      return acc;
    }, {} as Record<string, SourceReference[]>);

    return (
      <div className="mt-6 border-t pt-4">
        <h3 className="text-lg font-semibold mb-3 text-gray-800">
          {labels.references}
        </h3>
        <div className="space-y-3">
          {Object.entries(groupedRefs).map(([sourceTitle, refs], index) => {
            const pageNum = extractPageNumber(refs[0]);
            const fileId = refs[0].file_id;
            
            return (
              <div key={index} className="bg-gray-50 p-3 rounded-lg border-l-4 border-blue-500">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 mb-1">
                      {labels.document}: {sourceTitle}
                    </div>
                    {isValidFileId(fileId) && (
                      <div className="text-xs text-gray-500 mb-1">
                        File ID: {fileId}
                      </div>
                    )}
                    {pageNum && (
                      <div className="text-sm text-gray-600 mb-2">
                        {labels.page}: {pageNum}
                      </div>
                    )}
                    <div className="text-sm text-gray-700">
                      {labels.summary}: {
                        refs[0].summary === "Source: Unknown" || refs[0].summary === "Unknown" 
                          ? "No summary available" 
                          : refs[0].summary.length > 150 
                            ? `${refs[0].summary.substring(0, 150)}...`
                            : refs[0].summary
                      }
                    </div>
                  </div>
                  <div className="ml-3 flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {refs.length} {refs.length === 1 ? 'ref' : 'refs'}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="whitespace-pre-wrap">
      {formatResponse(response)}
      {renderReferences()}
    </div>
  );
};

export default PerplexityStyleResponse;