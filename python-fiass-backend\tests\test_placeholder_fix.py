#!/usr/bin/env python3
"""
Test to verify that the placeholder issue is fixed
"""

import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.translation_service import translation_service

def test_no_placeholder_leakage():
    """Test that no old-style placeholders leak into responses"""
    
    print("🧪 TESTING PLACEHOLDER LEAKAGE FIX")
    print("=" * 50)
    
    # Test cases that might have caused the issue
    test_cases = [
        {
            "text": "வழங்கப்பட்ட சூழலின் அடிப்படையில், யு. எஸ். விமான போக்குவரத்து அழுத்த வழக்குகளைப் நீங்கள் கேட்கிறீர்கள், விலே ஒரு. இருப்பினும், சூழலில் விமான போக்குவரத்து அழுத்த அல்லது விலேயின் மூலத்தைப் பற்றிய குறிப்பிட்ட.",
            "source_lang": "ta",
            "target_lang": "en",
            "description": "Tamil text with potential capital words"
        },
        {
            "text": "The iPhone uses IOS operating system for API calls",
            "source_lang": "en", 
            "target_lang": "ta",
            "description": "English with multiple continuous capitals"
        },
        {
            "text": "Microsoft Azure REST API JSON XML HTTP",
            "source_lang": "en",
            "target_lang": "te", 
            "description": "Multiple continuous capitals only"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['description']}")
        print(f"Input: '{test_case['text'][:100]}{'...' if len(test_case['text']) > 100 else ''}'")
        print(f"Translation: {test_case['source_lang']} → {test_case['target_lang']}")
        
        try:
            result = translation_service.translate_text(
                test_case['text'], 
                test_case['target_lang'], 
                test_case['source_lang']
            )
            
            translated_text = result['translated_text']
            print(f"Output: '{translated_text[:100]}{'...' if len(translated_text) > 100 else ''}'")
            
            # Check for problematic patterns
            problematic_patterns = [
                "__capital_word_",
                "__CAPITAL_WORD_",
                "0xotp1oy",
                "1xcn0x5e",
                "wowuhkuzj7"
            ]
            
            issues_found = []
            for pattern in problematic_patterns:
                if pattern in translated_text:
                    issues_found.append(pattern)
            
            if issues_found:
                print(f"❌ ISSUES FOUND: {issues_found}")
                all_passed = False
            else:
                print("✅ No problematic patterns detected")
                
            # Check if continuous capitals are properly handled
            import re
            continuous_capitals = re.findall(r'\b[A-Z]{2,}\b', test_case['text'])
            if continuous_capitals:
                preserved_count = sum(1 for word in continuous_capitals if word in translated_text)
                print(f"📊 Continuous capitals: {len(continuous_capitals)} found, {preserved_count} preserved")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            all_passed = False
        
        print("-" * 40)
        print()
    
    print("📊 FINAL RESULTS")
    print("=" * 30)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ No placeholder leakage detected")
        print("✅ Capital word preservation working correctly")
        print("✅ Backend translation service functioning properly")
    else:
        print("❌ SOME ISSUES DETECTED!")
        print("⚠️  Check the output above for details")
    
    return all_passed

def test_backend_placeholder_system():
    """Test that the backend placeholder system works correctly"""
    
    print("🔧 TESTING BACKEND PLACEHOLDER SYSTEM")
    print("=" * 50)
    
    # Test the internal placeholder system
    test_text = "The iPhone uses IOS and REST API for JSON data"
    
    print(f"Original text: '{test_text}'")
    
    # Test the internal methods
    modified_text, capital_words = translation_service._preserve_continuous_capital_words(test_text)
    print(f"Modified text: '{modified_text}'")
    print(f"Capital words: {capital_words}")
    
    # Test restoration
    restored_text = translation_service._restore_continuous_capital_words(modified_text, capital_words)
    print(f"Restored text: '{restored_text}'")
    
    if restored_text == test_text:
        print("✅ Backend placeholder system working correctly")
        return True
    else:
        print("❌ Backend placeholder system has issues")
        return False

if __name__ == "__main__":
    print("🚀 STARTING PLACEHOLDER FIX VERIFICATION")
    print("=" * 60)
    print()
    
    backend_test = test_backend_placeholder_system()
    print()
    
    leakage_test = test_no_placeholder_leakage()
    print()
    
    if backend_test and leakage_test:
        print("🎉 ALL TESTS PASSED - PLACEHOLDER ISSUE FIXED!")
    else:
        print("⚠️  SOME ISSUES REMAIN - CHECK OUTPUT ABOVE")
    
    print()
    print("💡 SUMMARY:")
    print("- Frontend capital word logic has been removed")
    print("- Backend handles all capital word preservation")
    print("- No more __CAPITAL_WORD_XXXXXXXX__ patterns should appear")
    print("- Continuous capitals are preserved using backend logic only")