"""
Enhanced Indian Language Translation Service
Using specialized pretrained models for Tamil, Telugu, Kannada, and Oriya
"""

import os
import torch
from transformers import (
    AutoTokenizer, AutoModelForSeq2SeqLM, 
    MBartForConditionalGeneration, MBart50TokenizerFast,
    pipeline
)
from typing import Dict, List, Optional, Tuple
import logging
from functools import lru_cache
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedIndianTranslationService:
    """
    Enhanced translation service using specialized Indian language models
    """
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {self.device}")
        
        # Model configurations for different approaches
        self.model_configs = {
            "indicbart": {
                "model_name": "ai4bharat/IndicBARTSS",
                "tokenizer_name": "ai4bharat/IndicBARTSS",
                "languages": ["ta", "te", "kn", "or", "hi", "en"],
                "description": "AI4Bharat's IndicBART for Indian languages"
            },
            "mbart50": {
                "model_name": "facebook/mbart-large-50-many-to-many-mmt",
                "tokenizer_name": "facebook/mbart-large-50-many-to-many-mmt",
                "languages": ["ta_IN", "te_IN", "kn_IN", "or_IN", "hi_IN", "en_XX"],
                "description": "Facebook's mBART-50 with Indian language support"
            },
            "indictrans2": {
                "model_name": "ai4bharat/indictrans2-en-indic-1B",
                "tokenizer_name": "ai4bharat/indictrans2-en-indic-1B",
                "languages": ["tam_Taml", "tel_Telu", "kan_Knda", "ory_Orya", "hin_Deva", "eng_Latn"],
                "description": "AI4Bharat's IndicTrans2 - Latest and most accurate"
            }
        }
        
        # Language code mappings
        self.lang_mappings = {
            "indicbart": {
                "ta": "ta_IN", "te": "te_IN", "kn": "kn_IN", "or": "or_IN", "hi": "hi_IN", "en": "en_XX"
            },
            "mbart50": {
                "ta": "ta_IN", "te": "te_IN", "kn": "kn_IN", "or": "or_IN", "hi": "hi_IN", "en": "en_XX"
            },
            "indictrans2": {
                "ta": "tam_Taml", "te": "tel_Telu", "kn": "kan_Knda", "or": "ory_Orya", "hi": "hin_Deva", "en": "eng_Latn"
            }
        }
        
        # Initialize models lazily
        self.models = {}
        self.tokenizers = {}
        self.pipelines = {}
        
        # Performance tracking
        self.translation_stats = {
            "total_translations": 0,
            "successful_translations": 0,
            "failed_translations": 0,
            "avg_translation_time": 0.0,
            "model_usage": {}
        }
    
    @lru_cache(maxsize=32)
    def _load_model(self, model_type: str) -> Tuple[object, object]:
        """
        Load and cache translation models
        """
        try:
            config = self.model_configs[model_type]
            logger.info(f"Loading {config['description']}...")
            
            if model_type == "indictrans2":
                # IndicTrans2 specific loading
                tokenizer = AutoTokenizer.from_pretrained(
                    config["tokenizer_name"], 
                    trust_remote_code=True
                )
                model = AutoModelForSeq2SeqLM.from_pretrained(
                    config["model_name"], 
                    trust_remote_code=True
                ).to(self.device)
                
            elif model_type == "mbart50":
                # mBART-50 specific loading
                tokenizer = MBart50TokenizerFast.from_pretrained(config["tokenizer_name"])
                model = MBartForConditionalGeneration.from_pretrained(config["model_name"]).to(self.device)
                
            else:  # indicbart
                # IndicBART loading
                tokenizer = AutoTokenizer.from_pretrained(config["tokenizer_name"])
                model = AutoModelForSeq2SeqLM.from_pretrained(config["model_name"]).to(self.device)
            
            logger.info(f"✅ Successfully loaded {config['description']}")
            return model, tokenizer
            
        except Exception as e:
            logger.error(f"❌ Failed to load {model_type}: {str(e)}")
            return None, None
    
    def _get_best_model_for_languages(self, source_lang: str, target_lang: str) -> str:
        """
        Select the best model based on language pair and availability
        """
        # Priority order: IndicTrans2 > mBART-50 > IndicBART
        priority_models = ["indictrans2", "mbart50", "indicbart"]
        
        for model_type in priority_models:
            config = self.model_configs[model_type]
            lang_map = self.lang_mappings[model_type]
            
            if source_lang in lang_map and target_lang in lang_map:
                return model_type
        
        # Fallback to mBART-50 as it has the widest language support
        return "mbart50"
    
    def translate_with_indian_models(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str,
        model_preference: str = "auto"
    ) -> Dict[str, any]:
        """
        Translate text using specialized Indian language models
        
        Args:
            text: Text to translate
            source_lang: Source language code (ta, te, kn, or, hi, en)
            target_lang: Target language code
            model_preference: Preferred model ("auto", "indictrans2", "mbart50", "indicbart")
        
        Returns:
            Dictionary with translation results and metadata
        """
        start_time = time.time()
        self.translation_stats["total_translations"] += 1
        
        try:
            # Select model
            if model_preference == "auto":
                selected_model = self._get_best_model_for_languages(source_lang, target_lang)
            else:
                selected_model = model_preference
            
            logger.info(f"🔄 Translating using {selected_model}: {source_lang} -> {target_lang}")
            
            # Load model if not already loaded
            if selected_model not in self.models:
                model, tokenizer = self._load_model(selected_model)
                if model is None or tokenizer is None:
                    raise Exception(f"Failed to load {selected_model}")
                
                self.models[selected_model] = model
                self.tokenizers[selected_model] = tokenizer
            
            model = self.models[selected_model]
            tokenizer = self.tokenizers[selected_model]
            lang_map = self.lang_mappings[selected_model]
            
            # Map language codes
            src_lang_code = lang_map.get(source_lang, source_lang)
            tgt_lang_code = lang_map.get(target_lang, target_lang)
            
            # Perform translation based on model type
            if selected_model == "indictrans2":
                translated_text = self._translate_indictrans2(
                    text, src_lang_code, tgt_lang_code, model, tokenizer
                )
            elif selected_model == "mbart50":
                translated_text = self._translate_mbart50(
                    text, src_lang_code, tgt_lang_code, model, tokenizer
                )
            else:  # indicbart
                translated_text = self._translate_indicbart(
                    text, src_lang_code, tgt_lang_code, model, tokenizer
                )
            
            # Calculate translation time
            translation_time = time.time() - start_time
            
            # Update statistics
            self.translation_stats["successful_translations"] += 1
            self.translation_stats["model_usage"][selected_model] = \
                self.translation_stats["model_usage"].get(selected_model, 0) + 1
            
            # Update average translation time
            total_successful = self.translation_stats["successful_translations"]
            current_avg = self.translation_stats["avg_translation_time"]
            self.translation_stats["avg_translation_time"] = \
                (current_avg * (total_successful - 1) + translation_time) / total_successful
            
            return {
                "success": True,
                "translated_text": translated_text,
                "source_language": source_lang,
                "target_language": target_lang,
                "model_used": selected_model,
                "translation_time": round(translation_time, 3),
                "confidence_score": self._calculate_confidence_score(text, translated_text),
                "metadata": {
                    "model_description": self.model_configs[selected_model]["description"],
                    "source_lang_code": src_lang_code,
                    "target_lang_code": tgt_lang_code
                }
            }
            
        except Exception as e:
            self.translation_stats["failed_translations"] += 1
            logger.error(f"❌ Translation failed: {str(e)}")
            
            return {
                "success": False,
                "error": str(e),
                "source_language": source_lang,
                "target_language": target_lang,
                "fallback_available": True
            }
    
    def _translate_indictrans2(self, text: str, src_lang: str, tgt_lang: str, model, tokenizer) -> str:
        """Translate using IndicTrans2 model"""
        # Prepare input with language tokens
        input_text = f"{src_lang}: {text} </s> {tgt_lang}:"
        
        # Tokenize
        inputs = tokenizer(input_text, return_tensors="pt", padding=True, truncation=True, max_length=512)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Generate translation
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=512,
                num_beams=4,
                early_stopping=True,
                do_sample=False
            )
        
        # Decode output
        translated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Clean up the output (remove language prefixes)
        if ":" in translated_text:
            translated_text = translated_text.split(":", 1)[-1].strip()
        
        return translated_text
    
    def _translate_mbart50(self, text: str, src_lang: str, tgt_lang: str, model, tokenizer) -> str:
        """Translate using mBART-50 model"""
        # Set source language
        tokenizer.src_lang = src_lang
        
        # Tokenize
        inputs = tokenizer(text, return_tensors="pt", padding=True, truncation=True, max_length=512)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Generate translation
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                forced_bos_token_id=tokenizer.lang_code_to_id[tgt_lang],
                max_length=512,
                num_beams=4,
                early_stopping=True
            )
        
        # Decode output
        translated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        return translated_text
    
    def _translate_indicbart(self, text: str, src_lang: str, tgt_lang: str, model, tokenizer) -> str:
        """Translate using IndicBART model"""
        # Prepare input with language tokens
        input_text = f"<{src_lang}> {text} </{src_lang}> <{tgt_lang}>"
        
        # Tokenize
        inputs = tokenizer(input_text, return_tensors="pt", padding=True, truncation=True, max_length=512)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Generate translation
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=512,
                num_beams=4,
                early_stopping=True
            )
        
        # Decode output
        translated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Clean up language tags
        import re
        translated_text = re.sub(r'<[^>]+>', '', translated_text).strip()
        
        return translated_text
    
    def _calculate_confidence_score(self, source_text: str, translated_text: str) -> float:
        """
        Calculate a confidence score for the translation
        """
        try:
            # Simple heuristic based on length ratio and character diversity
            length_ratio = len(translated_text) / max(len(source_text), 1)
            
            # Penalize extreme length differences
            if length_ratio < 0.3 or length_ratio > 3.0:
                return 0.3
            
            # Check for character diversity (avoid repetitive translations)
            unique_chars = len(set(translated_text.lower()))
            total_chars = len(translated_text)
            diversity_ratio = unique_chars / max(total_chars, 1)
            
            # Combine factors
            confidence = min(1.0, (length_ratio * 0.5 + diversity_ratio * 0.5))
            return round(confidence, 3)
            
        except:
            return 0.5  # Default confidence
    
    def get_supported_languages(self) -> Dict[str, List[str]]:
        """Get list of supported languages for each model"""
        return {
            model_type: list(lang_map.keys()) 
            for model_type, lang_map in self.lang_mappings.items()
        }
    
    def get_translation_statistics(self) -> Dict[str, any]:
        """Get translation performance statistics"""
        return self.translation_stats.copy()
    
    def batch_translate(
        self, 
        texts: List[str], 
        source_lang: str, 
        target_lang: str,
        model_preference: str = "auto"
    ) -> List[Dict[str, any]]:
        """
        Translate multiple texts in batch for better efficiency
        """
        results = []
        
        for text in texts:
            result = self.translate_with_indian_models(
                text, source_lang, target_lang, model_preference
            )
            results.append(result)
        
        return results
    
    def clear_cache(self):
        """Clear model cache to free memory"""
        self.models.clear()
        self.tokenizers.clear()
        self.pipelines.clear()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("🧹 Model cache cleared")

# Global instance
enhanced_indian_translator = EnhancedIndianTranslationService()

# Convenience functions
def translate_to_indian_language(text: str, source_lang: str, target_lang: str, model: str = "auto") -> Dict[str, any]:
    """
    Convenience function for translation
    """
    return enhanced_indian_translator.translate_with_indian_models(text, source_lang, target_lang, model)

def get_best_translation_model(source_lang: str, target_lang: str) -> str:
    """
    Get the recommended model for a language pair
    """
    return enhanced_indian_translator._get_best_model_for_languages(source_lang, target_lang)

# Example usage and testing
if __name__ == "__main__":
    # Test the service
    translator = EnhancedIndianTranslationService()
    
    # Test translations
    test_cases = [
        ("Hello, how are you?", "en", "ta"),
        ("What is artificial intelligence?", "en", "te"),
        ("Good morning", "en", "kn"),
        ("Thank you very much", "en", "or")
    ]
    
    for text, src, tgt in test_cases:
        print(f"\n🔄 Testing: {text} ({src} -> {tgt})")
        result = translator.translate_with_indian_models(text, src, tgt)
        
        if result["success"]:
            print(f"✅ Translation: {result['translated_text']}")
            print(f"📊 Model: {result['model_used']}, Time: {result['translation_time']}s")
        else:
            print(f"❌ Failed: {result['error']}")
    
    # Print statistics
    print(f"\n📈 Translation Statistics:")
    stats = translator.get_translation_statistics()
    for key, value in stats.items():
        print(f"   {key}: {value}")