#!/usr/bin/env python3
"""
Debug script to test translation service with Tamil to Telugu translation.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tamil_to_telugu_translation():
    """Test Tamil to Telugu translation specifically."""
    print("🔧 TESTING TAMIL TO TELUGU TRANSLATION")
    print("=" * 60)
    
    try:
        from services.translation_service import TranslationService
        
        # Initialize translation service
        translation_service = TranslationService()
        
        # Test with a simple Tamil text
        tamil_text = "வணக்கம். இது ஒரு சோதனை செய்தி."
        print(f"📝 Original Tamil text: {tamil_text}")
        print()
        
        # Test translation to Telugu
        result = translation_service.translate_text(tamil_text, 'te', 'ta')
        
        print("🔍 Translation Result:")
        print(f"   Translated Text: {result['translated_text']}")
        print(f"   Source Language: {result['source_language']}")
        print(f"   Target Language: {result['target_language']}")
        print(f"   Provider: {result['translation_provider']}")
        print(f"   Cached: {result['cached']}")
        print()
        
        # Check for issues
        translated_text = result['translated_text']
        
        # Check for repeated characters
        if len(translated_text) > 10:
            unique_chars = len(set(translated_text.strip()))
            total_chars = len(translated_text.strip())
            char_diversity = unique_chars / total_chars
            
            print(f"🔍 Character Analysis:")
            print(f"   Total characters: {total_chars}")
            print(f"   Unique characters: {unique_chars}")
            print(f"   Character diversity: {char_diversity:.2f}")
            
            if char_diversity < 0.1:
                print("❌ WARNING: Low character diversity - possible repeated character issue")
            else:
                print("✅ Character diversity looks normal")
        
        # Test with another Tamil text
        print("\n" + "="*40)
        tamil_text2 = "நன்றி. உங்களுக்கு எப்படி உதவ முடியும்?"
        print(f"📝 Second Tamil text: {tamil_text2}")
        
        result2 = translation_service.translate_text(tamil_text2, 'te', 'ta')
        print(f"🔍 Second Translation: {result2['translated_text']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Translation service not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Translation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_deep_translator():
    """Test Deep Translator directly."""
    print("\n🔧 TESTING DEEP TRANSLATOR DIRECTLY")
    print("=" * 50)
    
    try:
        from deep_translator import GoogleTranslator
        
        tamil_text = "வணக்கம். இது ஒரு சோதனை செய்தி."
        print(f"📝 Tamil text: {tamil_text}")
        
        # Test direct translation
        translator = GoogleTranslator(source='ta', target='te')
        result = translator.translate(tamil_text)
        
        print(f"🔍 Direct Deep Translator result: {result}")
        
        # Check for issues
        if result and len(result) > 10:
            unique_chars = len(set(result.strip()))
            total_chars = len(result.strip())
            char_diversity = unique_chars / total_chars
            
            print(f"🔍 Character Analysis:")
            print(f"   Total characters: {total_chars}")
            print(f"   Unique characters: {unique_chars}")
            print(f"   Character diversity: {char_diversity:.2f}")
            
            if char_diversity < 0.1:
                print("❌ WARNING: Low character diversity detected in direct translation")
                print("   This suggests an issue with the Deep Translator library or Google Translate API")
            else:
                print("✅ Direct translation looks normal")
        
        return True
        
    except ImportError as e:
        print(f"❌ Deep Translator not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Direct translation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run translation debugging tests."""
    print("🚀 TRANSLATION SERVICE DEBUG")
    print("=" * 60)
    
    # Test translation service
    service_success = test_tamil_to_telugu_translation()
    
    # Test direct Deep Translator
    direct_success = test_direct_deep_translator()
    
    print("\n" + "=" * 60)
    if service_success and direct_success:
        print("🎯 CONCLUSION: Translation service tests completed")
        print("   Check the output above for any character repetition issues")
    else:
        print("⚠️ CONCLUSION: Some translation tests failed")
        print("   This might explain the repeated character issue you're seeing")
    
    return service_success and direct_success

if __name__ == "__main__":
    main()