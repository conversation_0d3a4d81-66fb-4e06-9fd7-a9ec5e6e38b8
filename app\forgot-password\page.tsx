"use client";
import React, { FormEvent, useState, useEffect } from "react";
import Image from "next/image";
import logoLight from "@/public/images/logo5.png";
import { baseUrl, uid } from "@/components/api/api";

import logoDark from "@/public/images/logo6.png";
import displayImg from "@/public/images/forgot-2.png";
import FormInput from "@/components/ui/FormInput";
import GradientBackground from "@/components/ui/GradientBackground";
import Footer from "@/components/Footer";
import { useRouter } from "next/navigation";
import { useTheme } from "next-themes";



function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [mobile, setMobile] = useState("");
  const [otp, setOtp] = useState("");
  const [showOtpField, setShowOtpField] = useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [sendingOtp, setSendingOtp] = useState(false);
  const [verifyingOtp, setVerifyingOtp] = useState(false);
  const [currentLogo, setCurrentLogo] = useState(logoLight);

  const { resolvedTheme } = useTheme();
  const router = useRouter();

  // Update logo based on theme
  useEffect(() => {
    setCurrentLogo(resolvedTheme === 'dark' ? logoDark : logoLight);
  }, [resolvedTheme]);

  const handleSendOtp = async () => {
    if ((!email || !email.includes("@")) && !mobile) {
      setIsError(true);
      setErrorMessage("Please enter a valid email or mobile number.");
      setTimeout(() => setIsError(false), 5000);
      return;
    }

    setSendingOtp(true);

    try {
      const res = await fetch(`${baseUrl}/Wforgetpwd/web`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          xxxid: uid,
        },
        body: JSON.stringify(mobile ? { mobileno: mobile } : { email }),
      });

      const result = await res.json();

      if (res.ok && (result.statusCode === 200 || result.status === "success")) {
        setIsSuccess(true);
        setSuccessMessage("OTP sent successfully.");
        setShowOtpField(true);
      } else {
        throw new Error(result.message || "Failed to send OTP");
      }
    } catch (error: any) {
      setIsError(true);
      setErrorMessage(error.message || "Failed to send OTP");
    } finally {
      setSendingOtp(false);
      setTimeout(() => {
        setIsError(false);
        setIsSuccess(false);
      }, 5000);
    }
  };

  const handleVerifyOtp = async (e: FormEvent) => {
    e.preventDefault();

    if (!otp) {
      setIsError(true);
      setErrorMessage("Please enter the OTP.");
      setTimeout(() => setIsError(false), 5000);
      return;
    }

    setVerifyingOtp(true);

    try {
      const res = await fetch(`${baseUrl}/WverifyOTP/web?Otp=${otp}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          xxxid: uid,
        },
        body: JSON.stringify(mobile ? { mobileno: mobile } : { email }),
      });

      const result = await res.json();

      if (res.ok && (result.statusCode === 200 || result.status === "success")) {
        setIsSuccess(true);
        const message = mobile
          ? "OTP verified! You will receive your password through your registered WhatsApp number."
          : "OTP verified! You will receive your password through your registered mail Id.";
        setSuccessMessage(message);

        setTimeout(() => {
          setIsSuccess(false);
          router.push("/sign-in");
        }, 5000);
      } else {
        throw new Error("Invalid OTP");
      }
    } catch (error: any) {
      setIsError(true);
      setErrorMessage("Invalid OTP");
      setTimeout(() => setIsError(false), 5000);
    } finally {
      setVerifyingOtp(false);
    }
  };

  return (
    <div className="flex flex-col lg:flex-row min-h-screen relative">
      {/* <GradientBackground /> */}

      {/* Left Section */}
      <div className="flex-1 flex flex-col justify-between items-center py-6 px-4 sm:px-8">
        <div className="flex items-center gap-1.5">
          <Image src={currentLogo} alt="Logo" />
        </div>

        <div className="w-full max-w-2xl bg-white/80 dark:bg-black/5 rounded-xl p-6 sm:p-8">
          <p className="text-2xl font-semibold text-center">Forgot Password?</p>
          <p className="text-sm pt-2 text-center text-n600 dark:text-n40">
            Enter your email or mobile number to receive an OTP
          </p>

          <form
            onSubmit={handleVerifyOtp}
            className="pt-8 flex flex-col items-center gap-4"
          >
            <div className="w-full max-w-md">
              <FormInput
                title="Email Address"
                placeholder="Enter your email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <div className="text-center text-sm text-gray-500 dark:text-gray-400">
              <span className="inline-block py-1">— or —</span>
            </div>

            <div className="w-full max-w-md">
              <FormInput
                title="Mobile Number"
                placeholder="Enter your mobile number"
                type="tel"
                value={mobile}
                onChange={(e) => setMobile(e.target.value)}
              />
            </div>

            {showOtpField && (
              <div className="w-full max-w-md">
                <FormInput
                  title="Enter OTP"
                  placeholder="6-digit OTP"
                  type="text"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                />
              </div>
            )}

            <div className="w-full max-w-md">
              {!showOtpField ? (
                <button
                  type="button"
                  onClick={handleSendOtp}
                  disabled={sendingOtp}
                  className={`w-full text-sm font-medium text-white bg-primaryColor py-3 px-6 rounded-full ${
                    sendingOtp ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                >
                  {sendingOtp ? "Sending OTP..." : "Send OTP"}
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={verifyingOtp}
                  className={`w-full text-sm font-medium text-white bg-primaryColor py-3 px-6 rounded-full ${
                    verifyingOtp ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                >
                  {verifyingOtp ? "Verifying..." : "Verify OTP"}
                </button>
              )}
              {isError && (
                <p className="text-errorColor text-sm pt-2">{errorMessage}</p>
              )}
              {isSuccess && (
                <p className="text-green-500 text-sm pt-2">{successMessage}</p>
              )}
            </div>
          </form>

          <div className="w-full text-center mt-4">
            <p className="text-sm text-n600 dark:text-n40">
              Remembered your credentials?{" "}
              <a
                href="/sign-in"
                className="text-primaryColor hover:underline font-medium"
              >
                Sign in
              </a>
            </p>
          </div>
        </div>

        <div className="flex justify-center items-center w-full pt-4">
          <Footer />
        </div>
      </div>

   {/* Right Section: Only visible on large screens */}
   <div className="hidden lg:block w-1/2 h-screen relative">
    <Image
    src={displayImg}
    alt="Forgot Password Illustration"
    fill
    className="object-cover"
    priority
    />
  </div>

    </div>
  );
}

export default ForgotPassword;
