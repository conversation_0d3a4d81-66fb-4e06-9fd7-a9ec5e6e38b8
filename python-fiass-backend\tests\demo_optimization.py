#!/usr/bin/env python3
"""
Demonstration script showing the language optimization in action
"""

import requests
import json
import time
from datetime import datetime

def demo_optimization():
    """Demonstrate the language optimization feature"""
    
    print("🚀 Language Optimization Demonstration")
    print("=" * 60)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    base_url = "http://localhost:5010/financial_query"
    
    # Demo scenarios
    scenarios = [
        {
            "title": "🚀 OPTIMIZED: Tamil Query with Tamil Index",
            "description": "This should be fast - no translation needed",
            "payload": {
                "query": "பணம் பற்றி சொல்லுங்கள்",
                "index_name": "tamil",
                "language": "Tamil"
            },
            "expected_optimization": True
        },
        {
            "title": "🌐 STANDARD: English Query with Tamil Index", 
            "description": "This uses cross-language processing",
            "payload": {
                "query": "Tell me about money",
                "index_name": "tamil",
                "language": "English"
            },
            "expected_optimization": False
        },
        {
            "title": "🔄 TRANSLATION: Tamil Query with Translation Request",
            "description": "This forces translation even with Tamil index",
            "payload": {
                "query": "பணம் பற்றி சொல்லுங்கள்",
                "index_name": "tamil",
                "language": "Tamil",
                "target_language": "English",
                "enable_translation": True
            },
            "expected_optimization": False
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{scenario['title']}")
        print(f"Description: {scenario['description']}")
        print(f"Query: {scenario['payload']['query']}")
        print(f"Index: {scenario['payload']['index_name']}")
        print("-" * 50)
        
        # Measure response time
        start_time = time.time()
        
        try:
            response = requests.post(base_url, json=scenario['payload'], timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract optimization information
                optimization_applied = data.get("optimized_direct_processing", False)
                early_optimization = data.get("early_optimization_detected", False)
                optimization_type = data.get("optimization_type", "none")
                query_translated = data.get("query_translated", False)
                data_language = data.get("data_language", "Unknown")
                
                # Cross-language processing info
                cross_lang_info = data.get("cross_language_processing", {})
                cross_lang_applied = cross_lang_info.get("applied", False)
                cross_lang_reason = cross_lang_info.get("reason", "Not specified")
                
                # AI response snippet
                ai_response = data.get("ai_response", "")
                response_snippet = ai_response[:100] + "..." if len(ai_response) > 100 else ai_response
                
                # Display results
                print(f"⏱️  Response Time: {response_time:.3f}s")
                print(f"🚀 Optimization Applied: {optimization_applied}")
                print(f"📈 Optimization Type: {optimization_type}")
                print(f"🔄 Query Translated: {query_translated}")
                print(f"📊 Data Language: {data_language}")
                print(f"🌐 Cross-language: {cross_lang_applied}")
                print(f"📝 Reason: {cross_lang_reason}")
                print(f"💬 Response: {response_snippet}")
                
                # Check if optimization worked as expected
                optimization_correct = optimization_applied == scenario["expected_optimization"]
                status = "✅ CORRECT" if optimization_correct else "❌ UNEXPECTED"
                print(f"🎯 Optimization Status: {status}")
                
                results.append({
                    "scenario": scenario["title"],
                    "response_time": response_time,
                    "optimization_applied": optimization_applied,
                    "optimization_type": optimization_type,
                    "expected": scenario["expected_optimization"],
                    "correct": optimization_correct
                })
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                results.append({
                    "scenario": scenario["title"],
                    "error": f"HTTP {response.status_code}",
                    "response_time": response_time
                })
                
        except requests.exceptions.RequestException as e:
            end_time = time.time()
            response_time = end_time - start_time
            print(f"❌ Request Error: {str(e)}")
            results.append({
                "scenario": scenario["title"],
                "error": str(e),
                "response_time": response_time
            })
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEMONSTRATION SUMMARY")
    print("=" * 60)
    
    successful_results = [r for r in results if "error" not in r]
    
    if successful_results:
        # Performance analysis
        optimized_times = [r["response_time"] for r in successful_results if r.get("optimization_applied", False)]
        standard_times = [r["response_time"] for r in successful_results if not r.get("optimization_applied", False)]
        
        print(f"Total Scenarios: {len(results)}")
        print(f"Successful: {len(successful_results)}")
        print(f"Failed: {len(results) - len(successful_results)}")
        
        if optimized_times:
            avg_optimized = sum(optimized_times) / len(optimized_times)
            print(f"\n🚀 OPTIMIZED SCENARIOS:")
            print(f"Average Response Time: {avg_optimized:.3f}s")
            print(f"Count: {len(optimized_times)}")
        
        if standard_times:
            avg_standard = sum(standard_times) / len(standard_times)
            print(f"\n🌐 STANDARD SCENARIOS:")
            print(f"Average Response Time: {avg_standard:.3f}s")
            print(f"Count: {len(standard_times)}")
        
        if optimized_times and standard_times:
            avg_optimized = sum(optimized_times) / len(optimized_times)
            avg_standard = sum(standard_times) / len(standard_times)
            improvement = ((avg_standard - avg_optimized) / avg_standard) * 100
            print(f"\n📈 PERFORMANCE IMPROVEMENT:")
            print(f"Optimization provides {improvement:.1f}% faster response time")
            print(f"Time saved: {avg_standard - avg_optimized:.3f}s per optimized query")
        
        # Detailed results
        print(f"\n📋 DETAILED RESULTS:")
        for result in successful_results:
            opt_status = "🚀 OPTIMIZED" if result.get("optimization_applied", False) else "🌐 STANDARD"
            print(f"{opt_status} {result['scenario']} - {result['response_time']:.3f}s")
    
    else:
        print("❌ No successful requests. Please check if the server is running on port 5010.")
    
    print(f"\n✅ Demonstration completed at {datetime.now().strftime('%H:%M:%S')}")

def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get("http://localhost:5010/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running on port 5010")
            return True
        else:
            print(f"⚠️ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("❌ Server is not running on port 5010")
        print("Please start the server with: python python-fiass-backend/full_code.py")
        return False

if __name__ == "__main__":
    print("🚀 Language Optimization Demo")
    print("This demo shows the performance benefits of the optimization feature")
    print("=" * 60)
    
    # Check server status first
    if check_server_status():
        demo_optimization()
    else:
        print("\n💡 To run this demo:")
        print("1. Start the server: cd python-fiass-backend && python full_code.py")
        print("2. Wait for the server to start (you'll see 'Running on http://127.0.0.1:5010')")
        print("3. Run this demo again: python demo_optimization.py")