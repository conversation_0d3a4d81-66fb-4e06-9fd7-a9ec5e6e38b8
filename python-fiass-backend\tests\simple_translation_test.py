#!/usr/bin/env python3
"""
Simple translation test without network dependencies
"""

import sys
import os
import requests
import time

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration
BASE_URL = os.getenv("BACKEND_URL", "http://localhost:5010")

def test_mymemory_direct():
    """Test MyMemory API directly"""
    print("🧪 Testing MyMemory API directly...")
    
    try:
        url = "https://api.mymemory.translated.net/get"
        params = {
            'q': 'Hello, how are you?',
            'langpair': 'en|te'
        }
        
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('responseStatus') == 200:
                translated_text = data.get('responseData', {}).get('translatedText', '')
                print(f"✅ MyMemory API working: 'Hello, how are you?' -> '{translated_text}'")
                return True
            else:
                print(f"❌ MyMemory API error: {data}")
        else:
            print(f"❌ MyMemory API HTTP error: {response.status_code}")
    except Exception as e:
        print(f"❌ MyMemory API exception: {e}")
    
    return False

def test_backend_api():
    """Test our backend translation API"""
    print("🧪 Testing backend translation API...")
    
    try:
        url = f"{BASE_URL}/api/translate"
        data = {
            'text': 'Hello, how are you?',
            'source_lang': 'en',
            'target_lang': 'te'
        }
        
        response = requests.post(url, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                translated_text = result.get('data', {}).get('translated_text', '')
                provider = result.get('data', {}).get('translation_provider', '')
                print(f"✅ Backend API working: 'Hello, how are you?' -> '{translated_text}' (provider: {provider})")
                return True
            else:
                print(f"❌ Backend API error: {result}")
        else:
            print(f"❌ Backend API HTTP error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Backend API exception: {e}")
    
    return False

def test_long_text_translation():
    """Test translation of longer text"""
    print("🧪 Testing long text translation...")
    
    long_text = """Based on general knowledge, here are some effective strategies for investing in mutual funds in India and their benefits:

**Strategies for Investing in Mutual Funds:**

1. Systematic Investment Plan (SIP):
   • Invest a fixed amount regularly (monthly/quarterly) to benefit from rupee cost averaging.
   • Reduces market timing risk.

2. Diversification:
   • Invest across different categories (equity, debt, hybrid) to minimize risk.

**Benefits of Mutual Funds:**

• Professional Management: Managed by experts."""
    
    try:
        url = f"{BASE_URL}/api/translate"
        data = {
            'text': long_text,
            'source_lang': 'en',
            'target_lang': 'te'
        }
        
        response = requests.post(url, json=data, timeout=60)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                translated_text = result.get('data', {}).get('translated_text', '')
                provider = result.get('data', {}).get('translation_provider', '')
                print(f"✅ Long text translation working (provider: {provider})")
                print(f"First 200 chars: '{translated_text[:200]}...'")
                
                # Check if it's mostly Telugu (should have Telugu characters)
                telugu_chars = sum(1 for c in translated_text if '\u0C00' <= c <= '\u0C7F')
                total_chars = len(translated_text.replace(' ', '').replace('\n', ''))
                telugu_percentage = (telugu_chars / total_chars * 100) if total_chars > 0 else 0
                print(f"Telugu character percentage: {telugu_percentage:.1f}%")
                
                if telugu_percentage > 30:  # At least 30% Telugu characters
                    print("✅ Translation appears to be working well!")
                    return True
                else:
                    print("⚠️ Translation may not be complete - low Telugu character percentage")
                    return False
            else:
                print(f"❌ Long text translation error: {result}")
        else:
            print(f"❌ Long text translation HTTP error: {response.status_code}")
    except Exception as e:
        print(f"❌ Long text translation exception: {e}")
    
    return False

def main():
    print("🚀 Starting Simple Translation Tests...")
    print("=" * 50)
    
    # Test 1: Direct MyMemory API
    mymemory_works = test_mymemory_direct()
    time.sleep(1)
    
    # Test 2: Backend API
    backend_works = test_backend_api()
    time.sleep(1)
    
    # Test 3: Long text translation
    if backend_works:
        long_text_works = test_long_text_translation()
    else:
        print("⏭️ Skipping long text test since backend API is not working")
        long_text_works = False
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"MyMemory API: {'✅ Working' if mymemory_works else '❌ Failed'}")
    print(f"Backend API: {'✅ Working' if backend_works else '❌ Failed'}")
    print(f"Long Text Translation: {'✅ Working' if long_text_works else '❌ Failed'}")
    
    if backend_works and long_text_works:
        print("\n🎉 All tests passed! Translation should be working in your app.")
    elif mymemory_works:
        print("\n⚠️ MyMemory API is working, but backend may need restart.")
        print("Try restarting your backend server: python full_code.py")
    else:
        print("\n❌ Translation services are having issues. Check network connectivity.")

if __name__ == "__main__":
    main()
