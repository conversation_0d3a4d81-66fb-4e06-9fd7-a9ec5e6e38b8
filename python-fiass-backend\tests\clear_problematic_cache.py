#!/usr/bin/env python3
"""
Clear any cached responses that might contain old placeholder patterns
"""

import sys
import os
import sqlite3

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def clear_problematic_cache():
    """Clear cached responses that contain old placeholder patterns"""
    
    print("🧹 CLEARING PROBLEMATIC CACHED RESPONSES")
    print("=" * 50)
    
    # Check if translation service has in-memory cache
    try:
        from services.translation_service import translation_service
        
        # Clear the in-memory translation cache
        with translation_service.cache_lock:
            old_cache_size = len(translation_service.cache)
            translation_service.cache.clear()
            print(f"✅ Cleared in-memory translation cache ({old_cache_size} entries)")
    except Exception as e:
        print(f"⚠️  Could not clear translation cache: {e}")
    
    # Check database files for cached responses
    db_files = [
        "csv_data.db",
        "../csv_data.db"
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"🔍 Checking database: {db_file}")
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Get all table names
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                for table in tables:
                    table_name = table[0]
                    print(f"  📋 Checking table: {table_name}")
                    
                    # Get column names
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    
                    # Look for text columns that might contain responses
                    text_columns = [col[1] for col in columns if 'text' in col[1].lower() or 'response' in col[1].lower() or 'content' in col[1].lower()]
                    
                    if text_columns:
                        print(f"    🔍 Text columns found: {text_columns}")
                        
                        # Check for problematic patterns
                        for column in text_columns:
                            try:
                                cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {column} LIKE '%__capital_word_%' OR {column} LIKE '%__CAPITAL_WORD_%'")
                                count = cursor.fetchone()[0]
                                
                                if count > 0:
                                    print(f"    ❌ Found {count} rows with old placeholder patterns in {column}")
                                    
                                    # Option to delete these rows (commented out for safety)
                                    # cursor.execute(f"DELETE FROM {table_name} WHERE {column} LIKE '%__capital_word_%' OR {column} LIKE '%__CAPITAL_WORD_%'")
                                    # conn.commit()
                                    # print(f"    ✅ Deleted {count} problematic rows from {column}")
                                else:
                                    print(f"    ✅ No problematic patterns found in {column}")
                            except Exception as e:
                                print(f"    ⚠️  Could not check column {column}: {e}")
                
                conn.close()
                
            except Exception as e:
                print(f"  ❌ Error checking database {db_file}: {e}")
        else:
            print(f"  ℹ️  Database {db_file} not found")
    
    print()
    print("💡 RECOMMENDATIONS:")
    print("1. Clear browser cache to remove any cached responses")
    print("2. Restart the Flask server to ensure fresh translation service")
    print("3. If issues persist, manually clear any database cached responses")
    print("4. Test with fresh queries to verify the fix")

def test_fresh_translation():
    """Test translation with a fresh query to verify fix"""
    
    print("\n🧪 TESTING FRESH TRANSLATION")
    print("=" * 40)
    
    try:
        from services.translation_service import translation_service
        
        test_text = "The iPhone uses IOS operating system"
        result = translation_service.translate_text(test_text, "ta", "en")
        
        print(f"Input: '{test_text}'")
        print(f"Output: '{result['translated_text']}'")
        
        # Check for problematic patterns
        if "__capital_word_" in result['translated_text'] or "__CAPITAL_WORD_" in result['translated_text']:
            print("❌ Still seeing old placeholder patterns!")
            return False
        else:
            print("✅ No old placeholder patterns detected")
            return True
            
    except Exception as e:
        print(f"❌ Error testing translation: {e}")
        return False

if __name__ == "__main__":
    clear_problematic_cache()
    
    if test_fresh_translation():
        print("\n🎉 SUCCESS: Fix appears to be working correctly!")
    else:
        print("\n⚠️  WARNING: Issues may still exist - check server logs")
    
    print("\n📋 NEXT STEPS:")
    print("1. Restart your Flask server")
    print("2. Clear browser cache")
    print("3. Test with a fresh query")
    print("4. Monitor for any remaining placeholder patterns")