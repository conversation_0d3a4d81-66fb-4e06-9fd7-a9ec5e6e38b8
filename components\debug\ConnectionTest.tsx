import React, { useState } from 'react';
import { testConnection, checkBackendHealth, processYouTubeURL } from '../../services/uploadService';

interface ConnectionTestProps {
  onClose?: () => void;
}

const ConnectionTest: React.FC<ConnectionTestProps> = ({ onClose }) => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (test: string, result: any) => {
    setTestResults(prev => [...prev, { test, result, timestamp: new Date().toISOString() }]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Basic connection
      console.log('🔌 Running connection test...');
      const connectionResult = await testConnection();
      addResult('Connection Test', connectionResult);

      // Test 2: Health check
      console.log('🏥 Running health check...');
      const healthResult = await checkBackendHealth();
      addResult('Health Check', healthResult);

      // Test 3: YouTube URL processing (with a short test video)
      console.log('🎥 Testing YouTube processing...');
      const youtubeResult = await processYouTubeURL('https://youtu.be/dQw4w9WgXcQ', {
        index_name: 'default',
        client_email: '<EMAIL>'
      });
      addResult('YouTube Processing', youtubeResult);

    } catch (error) {
      console.error('❌ Test suite error:', error);
      addResult('Test Suite Error', { success: false, error: error.message });
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Backend Connection Test
          </h2>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex gap-2">
            <button
              onClick={runTests}
              disabled={isRunning}
              className={`px-4 py-2 rounded-lg text-white font-medium ${
                isRunning
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600'
              }`}
            >
              {isRunning ? 'Running Tests...' : 'Run Tests'}
            </button>
            <button
              onClick={clearResults}
              className="px-4 py-2 rounded-lg bg-gray-500 hover:bg-gray-600 text-white font-medium"
            >
              Clear Results
            </button>
          </div>

          {testResults.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Test Results
              </h3>
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${
                    result.result.success
                      ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                      : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <span
                      className={`w-3 h-3 rounded-full ${
                        result.result.success ? 'bg-green-500' : 'bg-red-500'
                      }`}
                    />
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {result.test}
                    </h4>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  
                  {result.result.success ? (
                    <p className="text-green-700 dark:text-green-300">
                      ✅ {result.result.message || 'Success'}
                    </p>
                  ) : (
                    <p className="text-red-700 dark:text-red-300">
                      ❌ {result.result.error || 'Failed'}
                    </p>
                  )}

                  {/* Show detailed result data */}
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm text-gray-600 dark:text-gray-400">
                      Show Details
                    </summary>
                    <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto">
                      {JSON.stringify(result.result, null, 2)}
                    </pre>
                  </details>
                </div>
              ))}
            </div>
          )}

          {isRunning && (
            <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
              <div className="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full" />
              <span>Running tests...</span>
            </div>
          )}
        </div>

        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            Debug Information
          </h4>
          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>Backend URL:</strong> {process.env.NEXT_PUBLIC_BACKEND_URL || process.env.BACKEND_URL || 'http://localhost:5010'}</p>
            <p><strong>Environment:</strong> {process.env.NODE_ENV || 'development'}</p>
            <p><strong>User Agent:</strong> {navigator.userAgent}</p>
            <p><strong>Current Time:</strong> {new Date().toISOString()}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectionTest;
