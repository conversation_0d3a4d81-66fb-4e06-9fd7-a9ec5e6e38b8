#!/usr/bin/env python3
"""
Test script to demonstrate mixed query translation behavior
"""

import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.translation_service import TranslationService

def test_mixed_query_scenarios():
    """Test various real-world query scenarios"""
    
    print("🌍 Testing Mixed Query Translation Scenarios")
    print("=" * 70)
    
    # Initialize translation service
    translation_service = TranslationService()
    
    # Real-world test scenarios
    scenarios = [
        {
            "query": "What is API documentation?",
            "source": "en",
            "target": "ta",
            "description": "Technical query with API (should skip)",
            "expected_behavior": "skip"
        },
        {
            "query": "How to use Microsoft Azure?",
            "source": "en", 
            "target": "te",
            "description": "Query with proper noun (should translate - single capitals)",
            "expected_behavior": "translate"
        },
        {
            "query": "what is cloud computing",
            "source": "en",
            "target": "kn",
            "description": "General query, all lowercase (should translate)",
            "expected_behavior": "translate"
        },
        {
            "query": "explain machine learning concepts",
            "source": "en",
            "target": "hi",
            "description": "Educational query, no capitals (should translate)",
            "expected_behavior": "translate"
        },
        {
            "query": "How does REST API work?",
            "source": "en",
            "target": "or",
            "description": "Technical query with REST API (should skip)",
            "expected_behavior": "skip"
        },
        {
            "query": "iPhone vs Android comparison",
            "source": "en",
            "target": "ta",
            "description": "Product comparison with brand names (should translate - single capitals)",
            "expected_behavior": "translate"
        }
    ]
    
    print(f"Testing {len(scenarios)} real-world scenarios...\n")
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"Scenario {i}: {scenario['description']}")
        print(f"Query: '{scenario['query']}'")
        print(f"Language: {scenario['source']} -> {scenario['target']}")
        print(f"Expected: {scenario['expected_behavior']}")
        
        try:
            # Test the translation
            result = translation_service.translate_text(
                scenario['query'], 
                scenario['target'], 
                scenario['source']
            )
            
            translated_text = result['translated_text']
            provider = result.get('translation_provider', 'unknown')
            
            print(f"Result: '{translated_text}'")
            print(f"Provider: {provider}")
            
            # Determine actual behavior
            if provider == 'skipped_capital_letters':
                actual_behavior = "skip"
                print("🔒 Translation skipped due to capital letters")
            elif translated_text == scenario['query']:
                actual_behavior = "skip"
                print("🔒 Translation skipped (same text)")
            else:
                actual_behavior = "translate"
                print("🌐 Translation performed")
            
            # Check if behavior matches expectation
            if actual_behavior == scenario['expected_behavior']:
                print("✅ PASS - Behavior matches expectation")
            else:
                print("❌ FAIL - Behavior doesn't match expectation")
                print(f"   Expected: {scenario['expected_behavior']}, Got: {actual_behavior}")
                
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
        
        print("-" * 60)
        print()

def demonstrate_sentence_level_skipping():
    """Demonstrate how sentence-level skipping works in long text"""
    
    print("📝 Demonstrating Sentence-Level Capital Letter Skipping")
    print("=" * 70)
    
    translation_service = TranslationService()
    
    # Long text with mixed sentences
    long_text = """This is a general sentence about technology. 
    The API documentation explains REST principles. 
    Machine learning is becoming popular. 
    Microsoft Azure provides cloud services. 
    Many developers use these tools daily."""
    
    print("Original text:")
    print(long_text)
    print()
    
    print("Testing sentence-by-sentence translation (en -> ta):")
    print("-" * 50)
    
    # Split into sentences and test each
    sentences = [s.strip() for s in long_text.split('.') if s.strip()]
    
    for i, sentence in enumerate(sentences, 1):
        if not sentence.endswith('.'):
            sentence += '.'
            
        print(f"Sentence {i}: {sentence}")
        
        # Check if it has continuous capital letters
        has_continuous_capitals = translation_service._has_continuous_capital_letters(sentence)
        print(f"Has continuous capital letters: {has_continuous_capitals}")
        
        # Test translation
        result = translation_service.translate_text(sentence, "ta", "en")
        translated = result['translated_text']
        provider = result.get('translation_provider', 'unknown')
        
        print(f"Translation: {translated}")
        print(f"Provider: {provider}")
        
        if provider == 'skipped_capital_letters':
            print("🔒 SKIPPED - Contains continuous capital letters")
        else:
            print("🌐 TRANSLATED - No continuous capital letters")
        
        print()

if __name__ == "__main__":
    test_mixed_query_scenarios()
    print()
    demonstrate_sentence_level_skipping()
    
    print("🏁 Mixed query testing completed!")