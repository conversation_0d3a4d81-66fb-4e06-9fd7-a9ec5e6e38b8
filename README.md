# AIQuill Application

This project consists of a Next.js frontend and multiple Python backend services.

## Frontend

The frontend is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

### Getting Started with the Frontend

Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Backend

The backend consists of three separate Python Flask applications:

1. **Financial News API** (`app.py`): Fetch and categorize financial news articles
2. **CSV Processing** (`dyn.py`): Upload and process CSV files, store them in Pinecone vector database
3. **AI Query** (`suggest.py`): Generate AI responses to financial queries using vector search

### Getting Started with the Backend

#### Prerequisites

- Python 3.8+
- Required Python packages (install using `pip install -r requirements.txt`):
  - Flask
  - Flask-CORS
  - Pinecone-client
  - langchain-huggingface
  - pandas
  - numpy
  - openai
  - python-dotenv
  - requests

#### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
NEWS_API_KEY=your_news_api_key
PINECONE_API_KEY=your_pinecone_api_key
FINANCIAL_PINECONE_API_KEY=your_financial_pinecone_api_key
FINANCIAL_INDEX_NAME=financialnews
DEEPSEEK_API_KEY=your_deepseek_api_key
API_ENVIRONMENT=development
```

#### Running the Backend Services

Start each service in a separate terminal:

```bash
# Terminal 1: Financial News API
python app.py

# Terminal 2: CSV Processing
python dyn.py

# Terminal 3: AI Query
python suggest.py
```

The services will run on the following ports:
- Financial News API (`app.py`): Port 5002
- CSV Processing (`dyn.py`): Port 5000
- AI Query (`suggest.py`): Port (varies, check console output)

### API Endpoints

#### Financial News API (app.py)

- `GET /api/news`: Get financial news articles
  - Query parameters:
    - `category`: Filter news by category (all, business, markets, economy, technology, banking, policy)

#### CSV Processing (dyn.py)

- `POST /api/upload-csv`: Upload a CSV file to Pinecone
- `POST /api/upload-status`: Check the status of a CSV upload
- `POST /api/cancel-upload`: Cancel an ongoing CSV upload
- `GET /api/list-embedding-models`: List all available embedding models
- `POST /api/list-csv-files`: List all CSV files stored in the database
- `POST /api/get-csv-data`: Retrieve CSV data from the database

#### AI Query (suggest.py)

- `POST /financial_query`: Handle financial queries using AI
- `POST /index`: List all Pinecone indexes for a given API key (development mode only)

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

## Deploy

### Frontend

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

### Backend

The backend can be deployed on any platform that supports Python applications, such as:

- Heroku
- AWS Elastic Beanstalk
- Google Cloud Run
- Azure App Service

Run the development server:

### .env
```bash
API_KEY=pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua
PINECONE_ENV=aped-4627-b74a       # from your host URL: svc.aped-4627-b74a.pinecone.io
CHUNK_SIZE=500
BATCH_SIZE=10
PINECONE_NAMESPACE=default


# .env
TAMIL_PINECONE_API_KEY=pcsk_21PC84_LPSt5KydHCkFskjrFGXxkPj61ncHotYEoEe7bmFxDK6JKZq1bwFrPBcazexanfE
#PINECONE_API_KEY=pcsk_4qB8ev_SYexQ1991BnG9K5WYmYGAbrq1AKVRzZo7pcTKYYtiAfwHwvA4KWCmtQv3bCurJr
FINANCIAL_PINECONE_API_KEY=pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua
# .env
PINECONE_ENV=aped-4627-b74a       # from your host URL: svc.aped-4627-b74a.pinecone.io
TAMIL_INDEX_NAME=taamilnews
FINANCIAL_INDEX_NAME=financialnews
TAMIL_CSV_FILE_PATH=tamilmurasu_dataset.csv  # rename your file or adjust path here
FINANCIAL_CSV_FILE_PATH=india-news-headlines.csv
CHUNK_SIZE=500
BATCH_SIZE=10
PINECONE_NAMESPACE=default

DEEPSEEK_API_KEY=***********************************




NEWS_API_KEY=7c4119e05d994813800a9b21b0a5ea3d
```
