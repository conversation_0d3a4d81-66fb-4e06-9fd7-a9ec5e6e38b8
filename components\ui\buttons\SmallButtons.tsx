import React from "react";

type SmallButtonProps = {
  name: string;
  fn?: () => void;
  secondary?: boolean;
  onClick?: () => void;
  disabled?: boolean;
};

function SmallButtons({ name, fn, secondary, onClick, disabled }: SmallButtonProps) {
  return (
    <button
      onClick={onClick || fn}
      disabled={disabled}
      className={`py-2 px-4 rounded-full border ${
        secondary
          ? "border-primaryColor text-primaryColor"
          : "bg-primaryColor text-white border-primaryColor"
      } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
    >
      {name}
    </button>
  );
}

export default SmallButtons;
