"""
Google Translate API Service using @vitalets/google-translate-api
This service provides high-quality translation for financial responses
with proper handling of capital words, special characters, and sentence integrity.
"""

import json
import subprocess
import os
import sys
from typing import Dict, Any, Optional, List
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GoogleTranslateAPIService:
    """
    Google Translate API Service using @vitalets/google-translate-api
    Provides robust translation with better handling of:
    - Capital words and proper nouns
    - Special characters and formatting
    - Complete sentence integrity
    - Financial terminology
    """
    
    def __init__(self):
        self.service_available = self._check_service_availability()
        self.node_script_path = self._setup_node_script()
        
        # Language mappings
        self.language_codes = {
            'Kannada': 'kn',
            'Tamil': 'ta',
            'Telugu': 'te', 
            'Oriya': 'or',
            'Odia': 'or',
            'English': 'en',
            'Hindi': 'hi'
        }
        
        self.language_names = {
            'kn': 'Kannada',
            'ta': 'Tamil',
            'te': 'Telugu',
            'or': 'Oriya', 
            'en': 'English',
            'hi': 'Hindi'
        }
    
    def detect_language(self, text: str) -> Dict[str, Any]:
        """
        Detect language of input text
        
        Args:
            text: Text to detect language for
            
        Returns:
            Dict with language detection results
        """
        try:
            if not text or not text.strip():
                return {
                    'success': False,
                    'language': 'en',
                    'confidence': 0,
                    'error': 'Empty text provided'
                }
            
            payload = {
                'action': 'detect',
                'text': text
            }
            
            print(f"🔍 Detecting language for text: '{text[:50]}...'")
            
            response = self._make_request(payload)
            
            if response and response.get('success'):
                detected_lang = response.get('language', 'en')
                confidence = response.get('confidence', 0)
                
                print(f"✅ Language detected: {detected_lang} (confidence: {confidence})")
                
                return {
                    'success': True,
                    'language': detected_lang,
                    'language_name': self.language_names.get(detected_lang, detected_lang),
                    'confidence': confidence
                }
            else:
                error_msg = response.get('error', 'Unknown detection error') if response else 'No response from API'
                print(f"❌ Language detection failed: {error_msg}")
                return {
                    'success': False,
                    'language': 'en',
                    'confidence': 0,
                    'error': error_msg
                }
                
        except Exception as e:
            print(f"❌ Language detection error: {str(e)}")
            return {
                'success': False,
                'language': 'en',
                'confidence': 0,
                'error': str(e)
            }
    
    def translate_text(self, text: str, target_language: str, source_language: Optional[str] = None) -> Dict[str, Any]:
        """
        Translate text to target language
        
        Args:
            text: Text to translate
            target_language: Target language code or name
            source_language: Source language code or name (optional)
            
        Returns:
            Dict with translation results
        """
        try:
            if not text or not text.strip():
                return {
                    'success': False,
                    'translated_text': text,
                    'original_text': text,
                    'error': 'Empty text provided'
                }
            
            # Convert language names to codes if needed
            target_lang_code = self._get_language_code(target_language)
            source_lang_code = self._get_language_code(source_language) if source_language else None
            
            # Skip translation if source and target are the same
            if source_lang_code and source_lang_code == target_lang_code:
                return {
                    'success': True,
                    'translated_text': text,
                    'original_text': text,
                    'skipped': True,
                    'reason': 'Source and target languages are the same'
                }
            
            payload = {
                'action': 'translate',
                'text': text,
                'targetLanguage': target_lang_code
            }
            
            if source_lang_code:
                payload['sourceLang'] = source_lang_code
            
            print(f"🌐 Translating text from {source_lang_code or 'auto'} to {target_lang_code}")
            print(f"📝 Text to translate: '{text[:100]}...'")
            
            response = self._make_request(payload)
            
            if response and response.get('success'):
                translated_text = response.get('translatedText', text)
                
                print(f"✅ Translation successful: '{translated_text[:100]}...'")
                
                return {
                    'success': True,
                    'translated_text': translated_text,
                    'original_text': text,
                    'source_language': response.get('detectedSourceLanguage', source_lang_code),
                    'target_language': target_lang_code
                }
            else:
                error_msg = response.get('error', 'Unknown translation error') if response else 'No response from API'
                print(f"❌ Translation failed: {error_msg}")
                return {
                    'success': False,
                    'translated_text': text,  # Return original text as fallback
                    'original_text': text,
                    'error': error_msg
                }
                
        except Exception as e:
            print(f"❌ Translation error: {str(e)}")
            return {
                'success': False,
                'translated_text': text,  # Return original text as fallback
                'original_text': text,
                'error': str(e)
            }
    
    def translate_financial_response(self, response_data: Dict[str, Any], target_language: str) -> Dict[str, Any]:
        """
        Translate entire financial query response
        
        Args:
            response_data: Response data from financial_query endpoint
            target_language: Target language code or name
            
        Returns:
            Translated response data
        """
        try:
            if not response_data or not isinstance(response_data, dict):
                return {
                    'success': False,
                    'error': 'Invalid response data provided',
                    'data': response_data
                }
            
            target_lang_code = self._get_language_code(target_language)
            
            payload = {
                'action': 'translateResponse',
                'responseData': response_data,
                'targetLanguage': target_lang_code
            }
            
            print(f"🔄 Translating financial response to {target_lang_code}")
            
            response = self._make_request(payload)
            
            if response and response.get('success'):
                translated_data = response.get('data', response_data)
                
                print(f"✅ Financial response translation completed successfully")
                
                return {
                    'success': True,
                    'data': translated_data
                }
            else:
                error_msg = response.get('error', 'Unknown translation error') if response else 'No response from API'
                print(f"❌ Financial response translation failed: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'data': response_data  # Return original data as fallback
                }
                
        except Exception as e:
            print(f"❌ Financial response translation error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'data': response_data  # Return original data as fallback
            }
    
    def _make_request(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Make HTTP request to translation API with retries
        
        Args:
            payload: Request payload
            
        Returns:
            Response data or None if failed
        """
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    self.translate_endpoint,
                    json=payload,
                    timeout=self.timeout,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    print(f"⚠️ API request failed with status {response.status_code}: {response.text}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        return None
                        
            except requests.exceptions.Timeout:
                print(f"⚠️ Request timeout (attempt {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    return None
                    
            except requests.exceptions.ConnectionError:
                print(f"⚠️ Connection error (attempt {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    return None
                    
            except Exception as e:
                print(f"⚠️ Request error: {str(e)} (attempt {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    return None
        
        return None
    
    def _get_language_code(self, language: str) -> str:
        """
        Get language code from language name or return as-is if already a code
        
        Args:
            language: Language name or code
            
        Returns:
            Language code
        """
        if not language:
            return 'en'
        
        # If it's already a code (2-3 characters), return as-is
        if len(language) <= 3 and language.lower() in self.language_names:
            return language.lower()
        
        # Try to find code by name
        return self.language_codes.get(language.title(), language.lower())
    
    def get_language_name(self, language_code: str) -> str:
        """
        Get language name from code
        
        Args:
            language_code: Language code
            
        Returns:
            Language name
        """
        return self.language_names.get(language_code.lower(), language_code)
    
    def is_service_available(self) -> bool:
        """
        Check if the translation service is available
        
        Returns:
            True if service is available, False otherwise
        """
        try:
            test_payload = {
                'action': 'detect',
                'text': 'Hello world'
            }
            
            response = requests.post(
                self.translate_endpoint,
                json=test_payload,
                timeout=5,
                headers={'Content-Type': 'application/json'}
            )
            
            return response.status_code == 200
            
        except Exception:
            return False

# Create a singleton instance
google_translate_service = GoogleTranslateAPIService()