#!/usr/bin/env python3
"""
Test API integration with capital letter preservation
"""

import sys
import os
import requests
import json

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_translation_api():
    """Test the translation API endpoint"""
    
    print("🧪 TESTING TRANSLATION API INTEGRATION")
    print("=" * 50)
    
    # Test data
    test_cases = [
        {
            "text": "The iPhone uses IOS operating system",
            "target_language": "ta",
            "description": "Your exact example with IOS"
        },
        {
            "text": "Microsoft Azure uses REST API for cloud services",
            "target_language": "te",
            "description": "Multiple continuous capitals"
        },
        {
            "text": "Python developers love coding",
            "target_language": "hi",
            "description": "Only single capitals"
        }
    ]
    
    # API endpoint
    api_url = "http://localhost:5000/api/test-translation"
    
    print(f"Testing API endpoint: {api_url}")
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['description']}")
        print(f"Input: '{test_case['text']}'")
        print(f"Target: {test_case['target_language']}")
        
        try:
            # Make API request
            response = requests.post(api_url, json={
                "text": test_case['text'],
                "target_language": test_case['target_language']
            }, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    translation_result = data.get('translation_result', {})
                    translated_text = translation_result.get('translated_text', '')
                    
                    print(f"✅ API Success")
                    print(f"Output: '{translated_text}'")
                    print(f"Provider: {translation_result.get('translation_provider', 'unknown')}")
                    
                    # Check for the problematic pattern
                    if "__Capital_" in translated_text or "wowuhkuzj7" in translated_text:
                        print("❌ ISSUE DETECTED: Problematic pattern found in response!")
                        print(f"   Full response: {translated_text}")
                    else:
                        print("✅ No problematic patterns detected")
                        
                        # Check if continuous capitals are preserved
                        if test_case['text'] == "The iPhone uses IOS operating system":
                            if "IOS" in translated_text:
                                print("✅ IOS correctly preserved")
                            else:
                                print("❌ IOS not preserved")
                else:
                    print(f"❌ API Error: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection Error: Make sure the Flask server is running on localhost:5000")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        print("-" * 40)
        print()

def test_direct_translation_service():
    """Test the translation service directly"""
    
    print("🔧 TESTING TRANSLATION SERVICE DIRECTLY")
    print("=" * 50)
    
    try:
        from services.translation_service import translation_service
        
        test_text = "The iPhone uses IOS operating system"
        target_lang = "ta"
        
        print(f"Input: '{test_text}'")
        print(f"Target: {target_lang}")
        
        result = translation_service.translate_text(test_text, target_lang, "en")
        translated_text = result['translated_text']
        
        print(f"Output: '{translated_text}'")
        print(f"Provider: {result.get('translation_provider', 'unknown')}")
        
        # Check for problematic patterns
        if "__Capital_" in translated_text or "wowuhkuzj7" in translated_text:
            print("❌ ISSUE DETECTED: Problematic pattern found!")
        else:
            print("✅ No problematic patterns detected")
            
        # Check if IOS is preserved
        if "IOS" in translated_text:
            print("✅ IOS correctly preserved")
        else:
            print("❌ IOS not preserved")
            
    except Exception as e:
        print(f"❌ Error testing translation service: {str(e)}")

if __name__ == "__main__":
    test_direct_translation_service()
    print()
    test_translation_api()
    
    print("🏁 Testing completed!")
    print()
    print("📋 SUMMARY:")
    print("- If you see '__Capital_wowuhkuzj7__' in responses, it's likely coming from:")
    print("  1. A different part of the system (not the translation service)")
    print("  2. An older implementation or cached response")
    print("  3. A different API endpoint or service")
    print("- The current translation service correctly preserves continuous capitals")
    print("- Word-level preservation is working as expected")