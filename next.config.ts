import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: true,
  devIndicators: {
    buildActivity: false,
    buildActivityPosition: 'bottom-right',
  },
  experimental: {
    // Optimize hydration
    optimizeCss: true,
  },
  images: {
    domains: [
      'www.moneycontrol.com',
      // Add other domains you might need for favicons
    ],
    // Alternatively, you can use remotePatterns for more flexibility
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**', // This allows all domains, but you may want to restrict it
      },
    ],
  },
  // Optimize for better hydration
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};

export default nextConfig;
