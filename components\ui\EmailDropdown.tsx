import React, { useEffect, useState } from "react";
import SelectDropdown from "./SelectDropdown";

type EmailOption = {
  value: string;
  label: string;
};

type EmailDropdownProps = {
  title?: string;
  placeholder?: string;
  onChange?: (selectedEmail: string) => void;
};

const EmailDropdown: React.FC<EmailDropdownProps> = ({
  title = "Email",
  placeholder = "Select an email",
  onChange,
}) => {
  const [emailOptions, setEmailOptions] = useState<EmailOption[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEmails = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch("https://dev-commonmannit.mannit.co/mannit/eSearch", {
          method: "POST", // Changed from GET to POST based on your curl example
          headers: {
            "Content-Type": "application/json",
            "xxxid": "QUKTYWK",
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch emails: ${response.status}`);
        }

        const data = await response.json();
        console.log("API Response:", data);

        if (data.statusCode === 200 && Array.isArray(data.source)) {
          // Parse the JSON strings in the source array to extract emails
          const emails = data.source.map((item: string) => {
            try {
              const parsedItem = JSON.parse(item);
              return parsedItem.email || "";
            } catch (e) {
              console.error("Error parsing JSON item:", e);
              return "";
            }
          }).filter(Boolean); // Remove empty strings

          // Create options for the dropdown
          const options = emails.map((email: string) => ({
            value: email,
            label: email,
          }));

          setEmailOptions(options);
        } else {
          throw new Error("Invalid response format or no emails found");
        }
      } catch (error) {
        console.error("Error fetching emails:", error);
        setError(error instanceof Error ? error.message : "Unknown error");
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmails();
  }, []);

  const handleChange = (selectedOption: any) => {
    if (onChange && selectedOption) {
      onChange(selectedOption.value);
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 border border-primaryColor/30 rounded-xl">
        <p className="text-xs text-n400 pl-6">
          <span className="bg-white dark:bg-n0 px-1 relative z-10">{title}</span>
        </p>
        <div className="flex items-center justify-center py-2">
          <div className="animate-pulse text-primaryColor">Loading emails...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 border border-red-300 rounded-xl">
        <p className="text-xs text-n400 pl-6">
          <span className="bg-white dark:bg-n0 px-1 relative z-10">{title}</span>
        </p>
        <div className="text-red-500 text-sm py-2 px-4">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div>
      <SelectDropdown
        options={emailOptions}
        title={title}
        placeholder={emailOptions.length === 0 ? "No emails found" : placeholder}
        onChange={handleChange}
      />
    </div>
  );
};

export default EmailDropdown;
