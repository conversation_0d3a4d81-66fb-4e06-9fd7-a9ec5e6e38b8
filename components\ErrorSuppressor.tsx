'use client';

import { useEffect } from 'react';

export default function ErrorSuppressor() {
  useEffect(() => {
    // Store original console methods
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalLog = console.log;

    // Override console.error to filter Next.js UI errors
    console.error = (...args: any[]) => {
      const message = args.join(' ');
      
      // Filter out specific Next.js errors that show in UI
      const nextjsUIErrors = [
        'Warning: Extra attributes from the server',
        'Warning: Prop `',
        'Warning: React does not recognize',
        'Warning: validateDOMNesting',
        'Warning: Each child in a list should have a unique "key" prop',
        'Hydration failed because the initial UI does not match',
        'There was an error while hydrating',
        'Text content does not match server-rendered HTML',
        'Warning: Expected server HTML to contain',
        'Warning: An invalid form control',
        'nextjs-static-indicator',
        'static-indicator',
        '__NEXT_DISABLE_STATIC_INDICATOR'
      ];

      // Check if the error message contains any of the filtered terms
      const shouldSuppress = nextjsUIErrors.some(errorPattern => 
        message.toLowerCase().includes(errorPattern.toLowerCase())
      );

      // Only call original console.error if not suppressed
      if (!shouldSuppress) {
        originalError.apply(console, args);
      }
    };

    // Override console.warn to filter Next.js UI warnings
    console.warn = (...args: any[]) => {
      const message = args.join(' ');
      
      const nextjsUIWarnings = [
        'Warning: Extra attributes from the server',
        'Warning: Prop `',
        'Warning: React does not recognize',
        'Warning: validateDOMNesting',
        'Warning: Each child in a list should have a unique "key" prop',
        'nextjs-static-indicator',
        'static-indicator'
      ];

      const shouldSuppress = nextjsUIWarnings.some(warningPattern => 
        message.toLowerCase().includes(warningPattern.toLowerCase())
      );

      if (!shouldSuppress) {
        originalWarn.apply(console, args);
      }
    };

    // Hide Next.js error overlay in development
    const hideErrorOverlay = () => {
      // Hide Next.js error overlay
      const errorOverlay = document.querySelector('nextjs-portal');
      if (errorOverlay) {
        (errorOverlay as HTMLElement).style.display = 'none';
      }

      // Hide React error boundary overlays
      const reactErrorOverlays = document.querySelectorAll('[data-nextjs-dialog-overlay]');
      reactErrorOverlays.forEach(overlay => {
        (overlay as HTMLElement).style.display = 'none';
      });

      // Hide development error messages
      const devErrors = document.querySelectorAll('[data-nextjs-toast-errors]');
      devErrors.forEach(error => {
        (error as HTMLElement).style.display = 'none';
      });
    };

    // Run immediately and on DOM changes
    hideErrorOverlay();
    
    const observer = new MutationObserver(() => {
      hideErrorOverlay();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Cleanup function
    return () => {
      console.error = originalError;
      console.warn = originalWarn;
      console.log = originalLog;
      observer.disconnect();
    };
  }, []);

  return null;
}