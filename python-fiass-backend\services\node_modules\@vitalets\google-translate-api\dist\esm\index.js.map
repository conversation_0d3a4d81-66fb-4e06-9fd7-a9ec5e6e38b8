{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,KAAmB,MAAM,YAAY,CAAC;AAC7C,OAAO,eAAe,MAAM,aAAa,CAAC;AAE1C,OAAO,EAAE,0BAA0B,EAAE,MAAM,cAAc,CAAC;AAE1D,MAAM,QAAQ,GAA6D;IACzE,IAAI,EAAE,MAAM;IACZ,EAAE,EAAE,IAAI;IACR,IAAI,EAAE,sBAAsB;CAC7B,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,SAAS,CAAC,SAAiB,EAAE,OAA0B;IAC3E,OAAO,IAAI,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;AACxD,CAAC;AAED,MAAM,OAAO,UAAU;IAGrB,YAAsB,SAAiB,EAAE,OAA0B;QAA7C,cAAS,GAAT,SAAS,CAAQ;QACrC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,SAAS;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC9C,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,CAAC,EAAE;YAAE,MAAM,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,IAAI,EAAiB,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACpC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;IACvB,CAAC;IAES,QAAQ;QAChB,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9B,OAAO;YACL,WAAW,IAAI,qBAAqB;YACpC,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,OAAO,EAAG,sDAAsD;SACjE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;IAES,SAAS;QACjB,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAClC,MAAM,MAAM,GAAG;YACb,EAAE,EAAE,IAAI;YACR,EAAE,EAAE,EAAE;YACN,CAAC,EAAE,IAAI,CAAC,SAAS;SAClB,CAAC;QACF,OAAO,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;IAChD,CAAC;IAES,iBAAiB;QACzB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QACtC,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;QAC5C,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;QACpB,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE;YAC3C,cAAc,EAAE,iDAAiD;SAClE,CAAC,CAAC;QACH,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAES,YAAY,CAAC,EAAE,SAAS,EAAe;QAC/C,OAAO,SAAS;aACb,MAAM,CAAC,CAAC,CAAC,EAAiB,EAAE,CAAC,OAAO,IAAI,CAAC,CAAC;aAC1C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;aACjB,IAAI,CAAC,EAAE,CAAC,CAAC;IACd,CAAC;IAES,KAAK,CAAC,UAAU,CAAC,GAAa;QACtC,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE;YACtB,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;YAC9B,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,GAAG,GAAG,CAAC,UAAU,QAAQ,EAAE,WAAW,IAAI,UAAU,GAAG,EAAE,CAAC;YAC1E,OAAO,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SAC7C;aAAM;YACL,OAAO,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;SACpD;IACH,CAAC;CACF"}