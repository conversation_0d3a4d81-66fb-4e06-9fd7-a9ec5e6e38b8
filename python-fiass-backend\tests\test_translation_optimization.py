#!/usr/bin/env python3
"""
Translation Service Optimization Test
Tests the performance improvements and fixes for Oriya language support.
"""

import time
import sys
import os

# Add the parent directory to the path to import services
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.translation_service import translation_service

def test_oriya_detection():
    """Test Oriya language detection"""
    print("🧪 Testing Oriya language detection...")
    
    oriya_text = "ମ୍ୟୁଚୁଆଲ ଫଣ୍ଡ କଣ?"
    detected_lang = translation_service.detect_language(oriya_text)
    
    print(f"   Text: {oriya_text}")
    print(f"   Detected: {detected_lang}")
    print(f"   Expected: or")
    print(f"   ✅ PASS" if detected_lang == 'or' else f"   ❌ FAIL")
    print()

def test_batch_translation_performance():
    """Test batch translation performance vs individual translations"""
    print("🚀 Testing batch translation performance...")
    
    test_texts = [
        "What is mutual fund?",
        "How to invest in SIP?", 
        "What are the benefits?",
        "Tell me more about this",
        "How does this work?",
        "What are the risks?",
        "What is the minimum investment?",
        "How to redeem funds?",
        "What is NAV?",
        "What are the tax implications?"
    ]
    
    # Test individual translations
    print("   Testing individual translations...")
    start_time = time.time()
    individual_results = []
    for text in test_texts:
        result = translation_service.translate_text(text, 'or', 'en')
        individual_results.append(result['translated_text'])
    individual_time = time.time() - start_time
    
    # Test batch translation
    print("   Testing batch translation...")
    start_time = time.time()
    batch_results = translation_service.translate_batch_optimized(test_texts, 'or', 'en')
    batch_time = time.time() - start_time
    
    print(f"   Individual translation time: {individual_time:.2f}s")
    print(f"   Batch translation time: {batch_time:.2f}s")
    print(f"   Performance improvement: {((individual_time - batch_time) / individual_time * 100):.1f}%")
    print(f"   ✅ PASS" if batch_time < individual_time else f"   ❌ FAIL")
    print()

def test_cache_performance():
    """Test translation cache performance"""
    print("💾 Testing cache performance...")
    
    # Get initial stats
    initial_stats = translation_service.get_performance_stats()
    
    # Translate the same text multiple times
    test_text = "What is mutual fund?"
    
    # First translation (should miss cache)
    start_time = time.time()
    result1 = translation_service.translate_text(test_text, 'ta', 'en')
    first_time = time.time() - start_time
    
    # Second translation (should hit cache)
    start_time = time.time()
    result2 = translation_service.translate_text(test_text, 'ta', 'en')
    second_time = time.time() - start_time
    
    # Get final stats
    final_stats = translation_service.get_performance_stats()
    
    print(f"   First translation time: {first_time*1000:.1f}ms (cache miss)")
    print(f"   Second translation time: {second_time*1000:.1f}ms (cache hit)")
    print(f"   Cache hit rate: {final_stats['cache_hit_rate_percent']:.1f}%")
    print(f"   Speed improvement: {((first_time - second_time) / first_time * 100):.1f}%")
    print(f"   ✅ PASS" if second_time < first_time and result2['cached'] else f"   ❌ FAIL")
    print()

def test_oriya_translations():
    """Test specific Oriya translations"""
    print("🔤 Testing Oriya translations...")
    
    test_cases = [
        ("What is mutual fund?", "ମ୍ୟୁଚୁଆଲ ଫଣ୍ଡ କଣ?"),
        ("How to invest?", "କିପରି ବିନିଯୋଗ କରିବେ?"),
        ("What are the benefits?", "ଲାଭଗୁଡ଼ିକ କଣ?"),
    ]
    
    for english_text, expected_oriya in test_cases:
        result = translation_service.translate_text(english_text, 'or', 'en')
        translated_text = result['translated_text']
        
        print(f"   English: {english_text}")
        print(f"   Translated: {translated_text}")
        print(f"   Expected: {expected_oriya}")
        
        # Check if translation contains Oriya characters
        has_oriya_chars = any('\u0B00' <= char <= '\u0B7F' for char in translated_text)
        print(f"   Contains Oriya chars: {has_oriya_chars}")
        print(f"   ✅ PASS" if has_oriya_chars else f"   ❌ FAIL")
        print()

def test_concurrent_translation():
    """Test concurrent translation performance"""
    print("⚡ Testing concurrent translation...")
    
    test_texts = [
        "What is investment?",
        "How does SIP work?",
        "What are mutual funds?",
        "Tell me about equity funds",
        "What is risk management?"
    ]
    
    start_time = time.time()
    results = translation_service.translate_multiple_texts_concurrent(
        test_texts, 'te', 'en', max_workers=3
    )
    concurrent_time = time.time() - start_time
    
    print(f"   Translated {len(test_texts)} texts concurrently")
    print(f"   Time taken: {concurrent_time:.2f}s")
    print(f"   Average per text: {concurrent_time/len(test_texts):.2f}s")
    
    # Check if all translations completed
    success_count = sum(1 for r in results if r['translated_text'])
    print(f"   Success rate: {success_count}/{len(test_texts)} ({success_count/len(test_texts)*100:.1f}%)")
    print(f"   ✅ PASS" if success_count == len(test_texts) else f"   ❌ FAIL")
    print()

def main():
    """Run all translation optimization tests"""
    print("🧪 Translation Service Optimization Tests")
    print("=" * 50)
    
    try:
        test_oriya_detection()
        test_cache_performance()
        test_oriya_translations()
        test_batch_translation_performance()
        test_concurrent_translation()
        
        # Print final performance stats
        print("📊 Final Performance Statistics:")
        stats = translation_service.get_performance_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()