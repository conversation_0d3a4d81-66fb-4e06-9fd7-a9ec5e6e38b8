import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { PiArrowLeft, PiChartLine, PiNewspaper, PiTranslate, PiSpinner } from 'react-icons/pi';
import Header from '@/components/Header';

interface FinancialData {
  query: string;
  response: string;
  sources?: Array<{
    title: string;
    url: string;
    snippet: string;
  }>;
  timestamp: string;
}

interface Labels {
  title: string;
  subtitle: string;
  queryPlaceholder: string;
  searchButton: string;
  searching: string;
  back: string;
  results: string;
  sources: string;
  noResults: string;
  error: string;
  translating: string;
  originalQuery: string;
  translatedQuery: string;
  originalResponse: string;
  translatedResponse: string;
}

const getLabels = (): Labels => {
  return {
    title: 'ଓଡ଼ିଆ ଆର୍ଥିକ ସମ୍ବାଦ',
    subtitle: 'ଆର୍ଥିକ ସମ୍ବାଦ ଏବଂ ବିଶ୍ଳେଷଣ ପାଇଁ ଖୋଜନ୍ତୁ',
    queryPlaceholder: 'ଆର୍ଥିକ ପ୍ରଶ୍ନ ପଚାରନ୍ତୁ... (ଯେପରି: ଆଜିର ଷ୍ଟକ ମାର୍କେଟ କଣ?)',
    searchButton: 'ଖୋଜନ୍ତୁ',
    searching: 'ଖୋଜୁଛି...',
    back: 'ଫେରିଯାଅ',
    results: 'ଫଳାଫଳ',
    sources: 'ଉତ୍ସ',
    noResults: 'କୌଣସି ଫଳାଫଳ ମିଳିଲା ନାହିଁ',
    error: 'ତ୍ରୁଟି ଘଟିଛି',
    translating: 'ଅନୁବାଦ କରୁଛି...',
    originalQuery: 'ମୂଳ ପ୍ରଶ୍ନ',
    translatedQuery: 'ଅନୁବାଦିତ ପ୍ରଶ୍ନ',
    originalResponse: 'ମୂଳ ଉତ୍ତର',
    translatedResponse: 'ଅନୁବାଦିତ ଉତ୍ତର'
  };
};

const OriyaFinancialPage: React.FC = () => {
  const router = useRouter();
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);
  const [results, setResults] = useState<FinancialData | null>(null);
  const [translatedResults, setTranslatedResults] = useState<FinancialData | null>(null);
  const [error, setError] = useState<string | null>(null);

  const labels = getLabels();

  // Function to translate text to Oriya
  const translateToOriya = async (text: string): Promise<string> => {
    try {
      // Try MyMemory API first
      const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=en|or`;
      const response = await fetch(url);
      
      if (response.ok) {
        const data = await response.json();
        if (data.responseStatus === 200 && data.responseData && data.responseData.translatedText) {
          return data.responseData.translatedText;
        }
      }
      
      // Fallback to basic translation
      return await basicTranslateToOriya(text);
    } catch (error) {
      console.error('Translation error:', error);
      return await basicTranslateToOriya(text);
    }
  };

  // Basic translation fallback
  const basicTranslateToOriya = async (text: string): Promise<string> => {
    const basicTranslations: { [key: string]: string } = {
      // Financial terms
      'stock': 'ଷ୍ଟକ', 'market': 'ବଜାର', 'price': 'ମୂଲ୍ୟ', 'share': 'ଅଂଶ',
      'investment': 'ନିବେଶ', 'profit': 'ଲାଭ', 'loss': 'କ୍ଷତି', 'economy': 'ଅର୍ଥନୀତି',
      'bank': 'ବ୍ୟାଙ୍କ', 'loan': 'ଋଣ', 'interest': 'ସୁଧ', 'rate': 'ହାର',
      'inflation': 'ମୁଦ୍ରାସ୍ଫୀତି', 'growth': 'ବୃଦ୍ଧି', 'GDP': 'ଜିଡିପି',
      'rupee': 'ଟଙ୍କା', 'dollar': 'ଡଲାର', 'currency': 'ମୁଦ୍ରା',
      
      // Common words
      'today': 'ଆଜି', 'yesterday': 'ଗତକାଲି', 'tomorrow': 'ଆସନ୍ତାକାଲି',
      'high': 'ଉଚ୍ଚ', 'low': 'କମ', 'up': 'ଉପରକୁ', 'down': 'ତଳକୁ',
      'increase': 'ବୃଦ୍ଧି', 'decrease': 'ହ୍ରାସ', 'change': 'ପରିବର୍ତ୍ତନ',
      'news': 'ସମ୍ବାଦ', 'report': 'ରିପୋର୍ଟ', 'analysis': 'ବିଶ୍ଳେଷଣ',
      'government': 'ସରକାର', 'policy': 'ନୀତି', 'budget': 'ବଜେଟ',
      'company': 'କମ୍ପାନୀ', 'business': 'ବ୍ୟବସାୟ', 'industry': 'ଶିଳ୍ପ'
    };

    let translatedText = text;
    for (const [english, oriya] of Object.entries(basicTranslations)) {
      const regex = new RegExp(`\\b${english}\\b`, 'gi');
      translatedText = translatedText.replace(regex, oriya);
    }

    return `[ମୂଳ ଅନୁବାଦ] ${translatedText}`;
  };

  // Function to search financial data
  const searchFinancialData = async (searchQuery: string) => {
    setIsSearching(true);
    setError(null);
    setResults(null);
    setTranslatedResults(null);

    try {
      // First translate the query to English for better search results
      const englishQuery = await translateFromOriyaToEnglish(searchQuery);
      
      // Mock financial API call - replace with actual financial API
      const response = await fetch('/api/financial-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: englishQuery,
          language: 'en'
        })
      });

      if (!response.ok) {
        throw new Error('Search failed');
      }

      const data = await response.json();
      
      const financialData: FinancialData = {
        query: englishQuery,
        response: data.response || 'No financial data available at the moment.',
        sources: data.sources || [],
        timestamp: new Date().toISOString()
      };

      setResults(financialData);

      // Translate results to Oriya
      setIsTranslating(true);
      const translatedResponse = await translateToOriya(financialData.response);
      
      const translatedData: FinancialData = {
        query: searchQuery,
        response: translatedResponse,
        sources: financialData.sources,
        timestamp: financialData.timestamp
      };

      setTranslatedResults(translatedData);

    } catch (error) {
      console.error('Search error:', error);
      setError('ଖୋଜିବାରେ ତ୍ରୁଟି ଘଟିଛି। ଦୟାକରି ପୁଣି ଚେଷ୍ଟା କରନ୍ତୁ।');
      
      // Provide demo data for testing
      const demoData: FinancialData = {
        query: searchQuery,
        response: 'ଆର୍ଥିକ ବଜାର ଆଜି ମିଶ୍ରିତ ପ୍ରଦର୍ଶନ ଦେଖାଇଛି। ଷ୍ଟକ ମାର୍କେଟରେ କିଛି ଉନ୍ନତି ଦେଖାଯାଇଛି, କିନ୍ତୁ ମୁଦ୍ରାସ୍ଫୀତି ଚିନ୍ତାର ବିଷୟ ହୋଇ ରହିଛି। ବିଶେଷଜ୍ଞମାନେ ପରାମର୍ଶ ଦେଉଛନ୍ତି ଯେ ନିବେଶକମାନେ ସତର୍କ ରହିବା ଉଚିତ।',
        sources: [
          {
            title: 'ଆର୍ଥିକ ସମ୍ବାଦ ଆଜି',
            url: '#',
            snippet: 'ଆଜିର ବଜାର ଅପଡେଟ ଏବଂ ବିଶ୍ଳେଷଣ'
          }
        ],
        timestamp: new Date().toISOString()
      };
      
      setTranslatedResults(demoData);
    } finally {
      setIsSearching(false);
      setIsTranslating(false);
    }
  };

  // Function to translate from Oriya to English (for search)
  const translateFromOriyaToEnglish = async (text: string): Promise<string> => {
    try {
      const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=or|en`;
      const response = await fetch(url);
      
      if (response.ok) {
        const data = await response.json();
        if (data.responseStatus === 200 && data.responseData && data.responseData.translatedText) {
          return data.responseData.translatedText;
        }
      }
    } catch (error) {
      console.error('Translation error:', error);
    }
    
    // If translation fails, return original text
    return text;
  };

  // Handle search
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;
    
    await searchFinancialData(query.trim());
  };

  // Handle back navigation
  const handleBack = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBack}
              className="flex items-center gap-2 text-orange-600 hover:text-orange-700 transition-colors"
            >
              <PiArrowLeft size={20} />
              <span className="font-medium">{labels.back}</span>
            </button>
            
            <div className="flex items-center gap-2 text-orange-600">
              <PiChartLine size={24} />
              <span className="font-semibold text-lg">{labels.title}</span>
            </div>
          </div>

          <p className="text-gray-600 text-center mb-6" style={{ fontFamily: 'Noto Sans Oriya, serif' }}>
            {labels.subtitle}
          </p>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="relative">
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder={labels.queryPlaceholder}
                className="w-full p-4 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                style={{ fontFamily: 'Noto Sans Oriya, serif' }}
                disabled={isSearching}
              />
              <PiNewspaper className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            </div>
            
            <button
              type="submit"
              disabled={isSearching || !query.trim()}
              className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
            >
              {isSearching ? (
                <>
                  <PiSpinner className="animate-spin" size={20} />
                  {labels.searching}
                </>
              ) : (
                <>
                  <PiChartLine size={20} />
                  {labels.searchButton}
                </>
              )}
            </button>
          </form>
        </div>

        {/* Results Section */}
        {(results || translatedResults || error) && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <PiNewspaper size={24} />
              {labels.results}
            </h2>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <p className="text-red-700" style={{ fontFamily: 'Noto Sans Oriya, serif' }}>{error}</p>
              </div>
            )}

            {isTranslating && (
              <div className="text-center py-4 mb-4">
                <div className="flex items-center justify-center gap-2 text-orange-600">
                  <PiTranslate className="animate-pulse" size={20} />
                  <span style={{ fontFamily: 'Noto Sans Oriya, serif' }}>{labels.translating}</span>
                </div>
              </div>
            )}

            {translatedResults && (
              <div className="space-y-6">
                {/* Query Display */}
                <div className="bg-orange-50 rounded-lg p-4">
                  <h3 className="font-semibold text-orange-800 mb-2" style={{ fontFamily: 'Noto Sans Oriya, serif' }}>
                    {labels.translatedQuery}:
                  </h3>
                  <p className="text-orange-700" style={{ fontFamily: 'Noto Sans Oriya, serif' }}>
                    {translatedResults.query}
                  </p>
                </div>

                {/* Response Display */}
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3" style={{ fontFamily: 'Noto Sans Oriya, serif' }}>
                    {labels.translatedResponse}:
                  </h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700 leading-relaxed whitespace-pre-wrap" style={{ fontFamily: 'Noto Sans Oriya, serif' }}>
                      {translatedResults.response}
                    </p>
                  </div>
                </div>

                {/* Sources */}
                {translatedResults.sources && translatedResults.sources.length > 0 && (
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-3" style={{ fontFamily: 'Noto Sans Oriya, serif' }}>
                      {labels.sources}:
                    </h3>
                    <div className="space-y-2">
                      {translatedResults.sources.map((source, index) => (
                        <div key={index} className="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-500">
                          <h4 className="font-medium text-blue-800 mb-1">{source.title}</h4>
                          <p className="text-blue-700 text-sm mb-2">{source.snippet}</p>
                          <a
                            href={source.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-700 underline text-sm"
                          >
                            {source.url}
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Timestamp */}
                <div className="text-sm text-gray-500 text-center">
                  {new Date(translatedResults.timestamp).toLocaleString('or-IN')}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default OriyaFinancialPage;