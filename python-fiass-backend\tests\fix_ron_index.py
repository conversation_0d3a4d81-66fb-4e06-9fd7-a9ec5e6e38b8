#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the missing database record for the ron index.
This will add the ron index to the database tables so it shows up for the correct user.
"""

import os
import sys
import json

# Add the python-fiass-backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

import database

def fix_ron_index():
    """Add the missing ron index to the database <NAME_EMAIL>."""
    try:
        # Check if ron index files exist
        faiss_data_dir = os.path.join('python-fiass-backend', 'faiss_data', 'ron')
        faiss_file = os.path.join(faiss_data_dir, 'ron.faiss')
        json_file = os.path.join(faiss_data_dir, 'ron.json')
        
        if not os.path.exists(faiss_file) or not os.path.exists(json_file):
            print(f"❌ Ron index files not found:")
            print(f"   FAISS file: {faiss_file} - {'EXISTS' if os.path.exists(faiss_file) else 'MISSING'}")
            print(f"   JSON file: {json_file} - {'EXISTS' if os.path.exists(json_file) else 'MISSING'}")
            return False
        
        print(f"✅ Ron index files found:")
        print(f"   FAISS file: {faiss_file}")
        print(f"   JSON file: {json_file}")
        
        # Read metadata to get information about the index
        with open(json_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        print(f"📊 Ron index contains {len(metadata)} entries")
        
        # Use the specified client email
        client_email = "<EMAIL>"
        print(f"📧 Associating ron index with user: {client_email}")
        
        # Get database connection
        conn = database.get_connection()
        cursor = conn.cursor()
        
        # Check if ron index already exists in pine_collection
        cursor.execute("SELECT * FROM pine_collection WHERE index_name = ?", ("ron",))
        existing_record = cursor.fetchone()
        
        if existing_record:
            print(f"ℹ️ Ron index already exists in pine_collection: {existing_record}")
            # Update the email if it's different
            cursor.execute('''
            UPDATE pine_collection 
            SET email = ? 
            WHERE index_name = ?
            ''', (client_email, "ron"))
            print(f"✅ Updated ron index email to: {client_email}")
        else:
            # Add ron index to pine_collection
            cursor.execute('''
            INSERT INTO pine_collection (index_name, email)
            VALUES (?, ?)
            ''', ("ron", client_email))
            
            print(f"✅ Added ron index to pine_collection with email: {client_email}")
        
        # Check if we need to add to excel_files table as well
        cursor.execute("SELECT * FROM excel_files WHERE index_name = ?", ("ron",))
        existing_excel_record = cursor.fetchone()
        
        if not existing_excel_record:
            # Determine the original filename from metadata
            original_filename = "unknown.pdf"  # Default
            if metadata and len(metadata) > 0:
                first_item = metadata[0]
                if 'file_name' in first_item:
                    original_filename = first_item['file_name']
            
            # Add to excel_files table
            cursor.execute('''
            INSERT INTO excel_files 
            (file_name, index_name, client_id, columns_json, row_count, 
             embedding_model, embedding_dimension, detected_language, chunks_created)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                original_filename,  # Original filename from metadata
                "ron",  # index_name
                client_email,  # client_id
                "[]",  # columns_json (empty for PDF data)
                len(metadata),  # row_count
                "all-MiniLM-L6-v2",  # embedding_model (default)
                384,  # embedding_dimension (default for MiniLM)
                "English",  # detected_language (default)
                len(metadata)  # chunks_created
            ))
            
            print(f"✅ Added ron index to excel_files table with filename: {original_filename}")
        else:
            print(f"ℹ️ Ron index already exists in excel_files: {existing_excel_record}")
            # Update the client_id if it's different
            cursor.execute('''
            UPDATE excel_files 
            SET client_id = ? 
            WHERE index_name = ?
            ''', (client_email, "ron"))
            print(f"✅ Updated ron index client_id to: {client_email}")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("\n🎉 Successfully fixed ron index database records!")
        print(f"The ron index should now appear for user: {client_email}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing ron index: {e}")
        return False

def verify_fix():
    """Verify that the fix worked by listing all indexes."""
    try:
        conn = database.get_connection()
        cursor = conn.cursor()
        
        print("\n📋 Current pine_collection entries:")
        cursor.execute("SELECT * FROM pine_collection ORDER BY upload_date DESC")
        pine_records = cursor.fetchall()
        for record in pine_records:
            print(f"   {record}")
        
        print("\n📋 Current excel_files entries:")
        cursor.execute("SELECT id, file_name, index_name, client_id FROM excel_files ORDER BY upload_date DESC")
        excel_records = cursor.fetchall()
        for record in excel_records:
            print(f"   {record}")
        
        # Test the specific user query
        print(f"\n🔍 Testing indexes for user: <EMAIL>")
        cursor.execute("""
        SELECT DISTINCT index_name 
        FROM pine_collection 
        WHERE email = ? 
        ORDER BY index_name
        """, ("<EMAIL>",))
        user_indexes = cursor.fetchall()
        print(f"   User indexes: {[row[0] for row in user_indexes]}")
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying fix: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing missing ron index database records...")
    
    if fix_ron_index():
        verify_fix()
        print("\n✅ Fix completed successfully!")
        print("Please test the API endpoint to see the ron index <NAME_EMAIL>")
    else:
        print("\n❌ Fix failed. Please check the error messages above.")