/**
 * Simple test component to verify backend connectivity from the frontend
 */

import React, { useState } from 'react';
import axios from 'axios';

const BackendConnectionTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testBackendConnection = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || process.env.BACKEND_URL || 'http://localhost:5010';

    try {
      // Test 1: Health check
      addResult('🔍 Testing health endpoint...');
      const healthResponse = await axios.get(`${BACKEND_URL}/api/health`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        }
      });
      addResult('✅ Health check successful');

      // Test 2: YouTube processing endpoint
      addResult('🔍 Testing YouTube processing endpoint...');
      const youtubeResponse = await axios.post(`${BACKEND_URL}/api/process_youtube`, {
        url: 'https://youtu.be/EVDsImdq4Yg?si=fw35W9-vgxQTZARv',
        index_name: 'default',
        client_email: '<EMAIL>'
      }, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        }
      });
      addResult('✅ YouTube processing successful');

    } catch (error: any) {
      addResult(`❌ Error: ${error.message}`);
      addResult(`Code: ${error.code || 'N/A'}`);
      addResult(`Status: ${error.response?.status || 'N/A'}`);
      
      if (error.code === 'ERR_NETWORK') {
        addResult('💡 Network error - check if backend is running on port 5010');
      }
      
      if (error.response?.data) {
        addResult(`Response data: ${JSON.stringify(error.response.data)}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Backend Connection Test</h2>
      
      <button
        onClick={testBackendConnection}
        disabled={isLoading}
        className={`px-4 py-2 rounded-lg text-white font-medium ${
          isLoading 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-blue-500 hover:bg-blue-600'
        }`}
      >
        {isLoading ? 'Testing...' : 'Test Backend Connection'}
      </button>

      {testResults.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Test Results:</h3>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm font-mono mb-1">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-6 text-sm text-gray-600 dark:text-gray-400">
        <h4 className="font-semibold mb-2">Troubleshooting:</h4>
        <ul className="list-disc list-inside space-y-1">
          <li>Make sure the backend server is running: <code>cd python-fiass-backend && python full_code.py</code></li>
          <li>Check that the backend is running on port 5010</li>
          <li>Verify CORS configuration allows localhost:3000</li>
          <li>Check browser console for additional error details</li>
        </ul>
      </div>
    </div>
  );
};

export default BackendConnectionTest;
