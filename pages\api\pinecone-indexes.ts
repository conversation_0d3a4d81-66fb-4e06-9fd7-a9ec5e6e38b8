import { NextApiRequest, NextApiResponse } from 'next';

// Extend NextApiRequest to include session and user
interface AuthenticatedRequest extends NextApiRequest {
  session?: {
    user?: {
      email: string;
    };
  };
  user?: {
    email: string;
  };
}

interface PineRecord {
  _id: string;
  api_key: string;
  index_name: string;
  client: string;
}

interface ApiResponse {
  success: boolean;
  indexes?: string[];
  api_keys?: string[];
  message?: string;
}

export default async function handler(
  req: AuthenticatedRequest,
  res: NextApiResponse<ApiResponse>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get email from authenticated session or localStorage
    let userEmail = req.session?.user?.email || req.user?.email;
    if (!userEmail) {
      // Try to get from localStorage if available in request headers
      const localStorageEmail = req.headers['x-user-email'];
      if (localStorageEmail && typeof localStorageEmail === 'string') {
        userEmail = localStorageEmail;
      } else {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }
    }

    // Fetch from external API with error handling
    let response: Response;
    let records: PineRecord[];
    
    try {
      response = await fetch(
        'https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE',
        {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        throw new Error(`API request failed with status ${response.status}: ${errorText}`);
      }

      records = await response.json();
      
      if (!Array.isArray(records)) {
        throw new Error('Invalid response format - expected array');
      }
    } catch (error) {
      console.error('Failed to fetch PINE collection:', error);
      return res.status(502).json({
        success: false,
        message: `Failed to fetch PINE collection: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    // Filter records by matching client email (case insensitive)
    const filteredRecords = records.filter(record => {
      if (!record.client) {
        console.warn('Record missing client email:', record._id);
        return false;
      }
      return record.client.toLowerCase() === userEmail.toLowerCase();
    });

    console.log('PINE collection results:', {
      userEmail,
      totalRecords: records.length,
      filteredRecords: filteredRecords.length
    });

    // Extract unique api_key/index_name pairs
    const uniquePairs: Record<string, {api_key: string, index_name: string}> = {};
    filteredRecords.forEach(record => {
      if (!record.api_key || !record.index_name) {
        console.warn('Skipping record with missing api_key or index_name', record);
        return;
      }
      const key = `${record.api_key}|${record.index_name}`;
      if (!uniquePairs[key]) {
        uniquePairs[key] = {
          api_key: record.api_key,
          index_name: record.index_name
        };
      }
    });

    const result: ApiResponse = {
      success: true,
      indexes: Object.values(uniquePairs).map(pair => pair.index_name),
      api_keys: Object.values(uniquePairs).map(pair => pair.api_key)
    };

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error fetching Pinecone indexes:', error);
    return res.status(500).json({ 
      success: false,
      message: 'Internal server error'
    });
  }
}