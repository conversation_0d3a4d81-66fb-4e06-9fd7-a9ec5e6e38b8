import React, { useState } from 'react';
import { TranslationService } from './services/TranslationService';

const TranslationTest: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [sourceLang, setSourceLang] = useState('ta');
  const [targetLang, setTargetLang] = useState('en');
  const [isLoading, setIsLoading] = useState(false);

  const handleTranslate = async () => {
    if (!inputText.trim()) return;

    setIsLoading(true);
    try {
      const result = await TranslationService.translateText(inputText, sourceLang, targetLang);
      setTranslatedText(result);
    } catch (error) {
      console.error('Translation error:', error);
      setTranslatedText('Translation failed');
    } finally {
      setIsLoading(false);
    }
  };

  const testTamilToEnglish = async () => {
    const tamilText = 'வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?';
    setInputText(tamilText);
    setSourceLang('ta');
    setTargetLang('en');
    
    setIsLoading(true);
    try {
      const result = await TranslationService.translateText(tamilText, 'ta', 'en');
      setTranslatedText(result);
    } catch (error) {
      console.error('Translation error:', error);
      setTranslatedText('Translation failed');
    } finally {
      setIsLoading(false);
    }
  };

  const testEnglishToTamil = async () => {
    const englishText = 'Hello, how are you?';
    setInputText(englishText);
    setSourceLang('en');
    setTargetLang('ta');
    
    setIsLoading(true);
    try {
      const result = await TranslationService.translateText(englishText, 'en', 'ta');
      setTranslatedText(result);
    } catch (error) {
      console.error('Translation error:', error);
      setTranslatedText('Translation failed');
    } finally {
      setIsLoading(false);
    }
  };

  const testTeluguToEnglish = async () => {
    const teluguText = 'హలో, మీరు ఎలా ఉన్నారు?';
    setInputText(teluguText);
    setSourceLang('te');
    setTargetLang('en');
    
    setIsLoading(true);
    try {
      const result = await TranslationService.translateText(teluguText, 'te', 'en');
      setTranslatedText(result);
    } catch (error) {
      console.error('Translation error:', error);
      setTranslatedText('Translation failed');
    } finally {
      setIsLoading(false);
    }
  };

  const testKannadaToEnglish = async () => {
    const kannadaText = 'ಹಲೋ, ನೀವು ಹೇಗಿದ್ದೀರಿ?';
    setInputText(kannadaText);
    setSourceLang('kn');
    setTargetLang('en');
    
    setIsLoading(true);
    try {
      const result = await TranslationService.translateText(kannadaText, 'kn', 'en');
      setTranslatedText(result);
    } catch (error) {
      console.error('Translation error:', error);
      setTranslatedText('Translation failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Translation Service Test</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Input Text:
          </label>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={3}
            placeholder="Enter text to translate..."
          />
        </div>

        <div className="flex space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From:
            </label>
            <select
              value={sourceLang}
              onChange={(e) => setSourceLang(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              <option value="ta">Tamil</option>
              <option value="te">Telugu</option>
              <option value="kn">Kannada</option>
              <option value="en">English</option>
              <option value="hi">Hindi</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              To:
            </label>
            <select
              value={targetLang}
              onChange={(e) => setTargetLang(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              <option value="en">English</option>
              <option value="ta">Tamil</option>
              <option value="te">Telugu</option>
              <option value="kn">Kannada</option>
              <option value="hi">Hindi</option>
            </select>
          </div>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={handleTranslate}
            disabled={isLoading || !inputText.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Translating...' : 'Translate'}
          </button>

          <button
            onClick={testTamilToEnglish}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
          >
            Test Tamil → English
          </button>

          <button
            onClick={testEnglishToTamil}
            disabled={isLoading}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400"
          >
            Test English → Tamil
          </button>

          <button
            onClick={testTeluguToEnglish}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:bg-gray-400"
          >
            Test Telugu → English
          </button>

          <button
            onClick={testKannadaToEnglish}
            disabled={isLoading}
            className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:bg-gray-400"
          >
            Test Kannada → English
          </button>
        </div>

        {translatedText && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Translation Result:
            </label>
            <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
              <p className="text-gray-800">{translatedText}</p>
            </div>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 rounded-md">
          <h3 className="font-semibold text-blue-800 mb-2">Cache Statistics:</h3>
          <button
            onClick={() => {
              const stats = TranslationService.getCacheStats();
              console.log('Cache stats:', stats);
              alert(`Cache size: ${stats.size} entries`);
            }}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Show Cache Stats
          </button>
          
          <button
            onClick={() => {
              TranslationService.clearCache();
              alert('Cache cleared!');
            }}
            className="ml-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            Clear Cache
          </button>
        </div>
      </div>
    </div>
  );
};

export default TranslationTest;