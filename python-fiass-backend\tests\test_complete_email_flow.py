#!/usr/bin/env python3
"""
Complete Email Flow Test Script

This script tests the entire client email filtering flow:
1. Database initialization with file_name column
2. CSV upload with client email and file name storage
3. Index listing filtered by client email
4. Verification that users only see their own data

Run this script to verify the complete implementation works correctly.
"""

import sys
import os
import requests
import json
import tempfile
import csv
import sqlite3
from datetime import datetime

# Add the python-fiass-backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

# Import our modules
try:
    from database import init_db, get_connection
    from pine_filter_service import PineFilterService
except ImportError as e:
    print(f"❌ Error importing modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

# Backend URL
BACKEND_URL = "http://localhost:5010"

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_step(step_num, description):
    """Print a formatted step."""
    print(f"\n📋 Step {step_num}: {description}")
    print("-" * 40)

def create_test_csv(filename, data):
    """Create a test CSV file with the given data."""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(data)
    print(f"✅ Created test CSV file: {filename}")
    return filename

def test_database_initialization():
    """Test that the database is properly initialized with file_name column."""
    print_step(1, "Database Initialization Test")
    
    try:
        # Initialize database
        init_db()
        print("✅ Database initialized successfully")
        
        # Check if pine_collection table has file_name column
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(pine_collection)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'file_name' in column_names:
            print("✅ pine_collection table has file_name column")
        else:
            print("❌ pine_collection table missing file_name column")
            return False
            
        # Check table structure
        print(f"📊 pine_collection columns: {column_names}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def test_csv_upload(file_path, client_email, index_name, expected_file_name):
    """Test CSV upload with client email and file name storage."""
    print(f"\n🔄 Testing CSV upload:")
    print(f"   📧 Client: {client_email}")
    print(f"   📁 Index: {index_name}")
    print(f"   📄 File: {expected_file_name}")
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (expected_file_name, f, 'text/csv')}
            data = {
                'client': client_email,
                'index_name': index_name,
                'embed_model': 'all-MiniLM-L6-v2'
            }
            
            response = requests.post(f"{BACKEND_URL}/api/upload-csv", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Upload successful")
                print(f"   📊 Vectors: {result.get('data', {}).get('vector_count', 'N/A')}")
                
                # Verify database storage
                conn = get_connection()
                cursor = conn.cursor()
                
                cursor.execute(
                    "SELECT email, index_name, file_name FROM pine_collection WHERE email = ? AND index_name = ?",
                    (client_email, index_name)
                )
                row = cursor.fetchone()
                
                if row:
                    stored_email, stored_index, stored_file = row
                    print(f"✅ Database storage verified:")
                    print(f"   📧 Stored email: {stored_email}")
                    print(f"   📁 Stored index: {stored_index}")
                    print(f"   📄 Stored file: {stored_file}")
                    
                    if stored_file == expected_file_name:
                        print("✅ File name correctly stored")
                    else:
                        print(f"⚠️  File name mismatch: expected {expected_file_name}, got {stored_file}")
                else:
                    print("❌ No database entry found")
                    return False
                
                conn.close()
                return True
            else:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error during upload: {e}")
        return False

def test_index_filtering(client_email, expected_indexes):
    """Test that index listing is properly filtered by client email."""
    print(f"\n🔍 Testing index filtering for: {client_email}")
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/list-faiss-indexes",
            json={'email': client_email},
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            indexes = result.get('indexes', [])
            print(f"✅ API Response successful")
            print(f"   📊 Found {len(indexes)} indexes: {indexes}")
            
            # Check if expected indexes are present
            missing_indexes = [idx for idx in expected_indexes if idx not in indexes]
            unexpected_indexes = [idx for idx in indexes if idx not in expected_indexes and idx != 'default']
            
            if not missing_indexes and not unexpected_indexes:
                print("✅ Index filtering working correctly")
                return True
            else:
                if missing_indexes:
                    print(f"❌ Missing expected indexes: {missing_indexes}")
                if unexpected_indexes:
                    print(f"❌ Unexpected indexes found: {unexpected_indexes}")
                return False
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during index filtering test: {e}")
        return False

def test_cross_user_isolation():
    """Test that users cannot see each other's indexes."""
    print_step(4, "Cross-User Isolation Test")
    
    test_users = [
        ('<EMAIL>', ['test_index_user1']),
        ('<EMAIL>', ['test_index_user2']),
        ('<EMAIL>', ['test_index_user3'])
    ]
    
    isolation_success = True
    
    for user_email, user_indexes in test_users:
        print(f"\n👤 Testing isolation for: {user_email}")
        
        # Get indexes for this user
        try:
            response = requests.post(
                f"{BACKEND_URL}/api/list-faiss-indexes",
                json={'email': user_email},
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                returned_indexes = result.get('indexes', [])
                
                # Remove 'default' index as it's available to all users
                user_specific_indexes = [idx for idx in returned_indexes if idx != 'default']
                
                # Check that user only sees their own indexes
                other_user_indexes = []
                for other_email, other_indexes in test_users:
                    if other_email != user_email:
                        other_user_indexes.extend(other_indexes)
                
                leaked_indexes = [idx for idx in user_specific_indexes if idx in other_user_indexes]
                
                if not leaked_indexes:
                    print(f"✅ {user_email} - No data leakage detected")
                else:
                    print(f"❌ {user_email} - Data leakage detected: {leaked_indexes}")
                    isolation_success = False
            else:
                print(f"❌ {user_email} - API request failed")
                isolation_success = False
                
        except Exception as e:
            print(f"❌ {user_email} - Error: {e}")
            isolation_success = False
    
    return isolation_success

def display_database_summary():
    """Display a summary of the database contents."""
    print_step(5, "Database Summary")
    
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Get all pine_collection entries
        cursor.execute("SELECT email, index_name, file_name, upload_date FROM pine_collection ORDER BY email, upload_date")
        rows = cursor.fetchall()
        
        print(f"📊 Pine Collection Contents ({len(rows)} entries):")
        print(f"{'Email':<25} {'Index Name':<20} {'File Name':<25} {'Upload Date':<20}")
        print("-" * 90)
        
        for row in rows:
            email, index_name, file_name, upload_date = row
            print(f"{email:<25} {index_name:<20} {file_name or 'N/A':<25} {upload_date:<20}")
        
        # Get CSV files table summary
        cursor.execute("SELECT client_email, index_name, file_name, row_count FROM csv_files ORDER BY client_email")
        csv_rows = cursor.fetchall()
        
        print(f"\n📊 CSV Files Table ({len(csv_rows)} entries):")
        print(f"{'Client Email':<25} {'Index Name':<20} {'File Name':<25} {'Row Count':<10}")
        print("-" * 80)
        
        for row in csv_rows:
            client_email, index_name, file_name, row_count = row
            print(f"{client_email or 'N/A':<25} {index_name:<20} {file_name or 'N/A':<25} {row_count or 0:<10}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error displaying database summary: {e}")

def main():
    """Main test function."""
    print_header("Complete Client Email Filtering Test")
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test configuration
    test_data = [
        ['Product Name', 'Description', 'Category', 'Price'],
        ['Laptop Pro', 'High-performance laptop for professionals', 'Electronics', '$1299'],
        ['Wireless Mouse', 'Ergonomic wireless mouse with precision tracking', 'Electronics', '$49'],
        ['Office Chair', 'Comfortable ergonomic office chair', 'Furniture', '$299'],
        ['Coffee Maker', 'Automatic drip coffee maker with timer', 'Appliances', '$89']
    ]
    
    test_users = [
        ('<EMAIL>', 'test_index_user1', 'user1_products.csv'),
        ('<EMAIL>', 'test_index_user2', 'user2_products.csv'),
        ('<EMAIL>', 'test_index_user3', 'user3_products.csv')
    ]
    
    temp_files = []
    test_results = {
        'database_init': False,
        'uploads': {},
        'filtering': {},
        'isolation': False
    }
    
    try:
        # Step 1: Test database initialization
        test_results['database_init'] = test_database_initialization()
        
        if not test_results['database_init']:
            print("❌ Database initialization failed. Stopping tests.")
            return
        
        # Step 2: Test CSV uploads with client email and file name storage
        print_step(2, "CSV Upload Tests")
        
        for client_email, index_name, file_name in test_users:
            # Create test CSV file
            temp_file = f"temp_{file_name}"
            create_test_csv(temp_file, test_data)
            temp_files.append(temp_file)
            
            # Test upload
            success = test_csv_upload(temp_file, client_email, index_name, file_name)
            test_results['uploads'][client_email] = success
        
        # Step 3: Test index filtering
        print_step(3, "Index Filtering Tests")
        
        for client_email, index_name, file_name in test_users:
            expected_indexes = [index_name]  # Each user should see only their own index
            success = test_index_filtering(client_email, expected_indexes)
            test_results['filtering'][client_email] = success
        
        # Step 4: Test cross-user isolation
        test_results['isolation'] = test_cross_user_isolation()
        
        # Step 5: Display database summary
        display_database_summary()
        
        # Final results
        print_header("Test Results Summary")
        
        print(f"📊 Database Initialization: {'✅ PASS' if test_results['database_init'] else '❌ FAIL'}")
        
        upload_success = all(test_results['uploads'].values())
        print(f"📊 CSV Uploads: {'✅ PASS' if upload_success else '❌ FAIL'}")
        for email, success in test_results['uploads'].items():
            print(f"   {email}: {'✅' if success else '❌'}")
        
        filtering_success = all(test_results['filtering'].values())
        print(f"📊 Index Filtering: {'✅ PASS' if filtering_success else '❌ FAIL'}")
        for email, success in test_results['filtering'].items():
            print(f"   {email}: {'✅' if success else '❌'}")
        
        print(f"📊 Cross-User Isolation: {'✅ PASS' if test_results['isolation'] else '❌ FAIL'}")
        
        overall_success = (test_results['database_init'] and upload_success and 
                          filtering_success and test_results['isolation'])
        
        print(f"\n🎯 Overall Test Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
        
        if overall_success:
            print("\n🎉 Client email filtering implementation is working correctly!")
            print("   ✅ Client emails are properly stored")
            print("   ✅ File names are correctly tracked")
            print("   ✅ Index names are associated with users")
            print("   ✅ Users only see their own indexes")
            print("   ✅ Cross-user data isolation is maintained")
        else:
            print("\n⚠️  Some tests failed. Please check the implementation.")
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        
    finally:
        # Clean up temporary files
        print_step("Cleanup", "Removing temporary files")
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    print(f"🧹 Cleaned up: {temp_file}")
            except Exception as e:
                print(f"⚠️  Could not clean up {temp_file}: {e}")
    
    print(f"\n🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()