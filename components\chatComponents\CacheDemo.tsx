import React, { useState } from 'react';
import { CacheService } from './services/CacheService';
import CacheStatsDisplay from './CacheStatsDisplay';

/**
 * Demo component to showcase cache functionality
 * This component demonstrates how the cache improves performance
 */
const CacheDemo: React.FC = () => {
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [responseTime, setResponseTime] = useState<number>(0);
  const [cacheHit, setCacheHit] = useState<boolean>(false);

  // Sample queries for testing
  const sampleQueries = [
    "What is the current stock price of Apple?",
    "How is the cryptocurrency market performing today?",
    "What are the latest financial news headlines?",
    "Explain the concept of compound interest",
    "What factors affect stock market volatility?"
  ];

  const simulateApiCall = async (query: string): Promise<any> => {
    // Simulate API delay (500-1500ms)
    const delay = Math.random() * 1000 + 500;
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return {
      ai_response: `This is a simulated response for: "${query}". The response includes detailed financial information and analysis. This would normally come from the actual financial query API endpoint.`,
      related_questions: [
        "What are the market trends?",
        "How do I invest in stocks?",
        "What is portfolio diversification?"
      ],
      sentence_analysis: [
        {
          sentence: "Sample analysis sentence",
          url: "https://example.com/financial-data",
          summary: "Financial data summary"
        }
      ]
    };
  };

  const handleQuery = async (queryText: string) => {
    if (!queryText.trim()) return;

    setIsLoading(true);
    setResponse('');
    setCacheHit(false);
    
    const startTime = Date.now();
    
    try {
      // Check cache first (same logic as in ChatBox)
      const cacheContext = 'demo|<EMAIL>|';
      const cachedResponse = CacheService.getCachedResponse(queryText, cacheContext);
      
      let data;
      if (cachedResponse) {
        // Cache hit - instant response
        setCacheHit(true);
        data = {
          ai_response: cachedResponse.ai_response,
          related_questions: cachedResponse.related_questions,
          sentence_analysis: cachedResponse.sentence_analysis
        };
        // Small delay to show the difference
        await new Promise(resolve => setTimeout(resolve, 50));
      } else {
        // Cache miss - simulate API call
        setCacheHit(false);
        data = await simulateApiCall(queryText);
        
        // Cache the response
        CacheService.setCachedResponse(queryText, data, cacheContext);
      }
      
      const endTime = Date.now();
      setResponseTime(endTime - startTime);
      setResponse(data.ai_response);
      
    } catch (error) {
      console.error('Error in demo query:', error);
      setResponse('Error occurred while processing the query.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSampleQuery = (sampleQuery: string) => {
    setQuery(sampleQuery);
    handleQuery(sampleQuery);
  };

  const getResponseTimeColor = () => {
    if (responseTime < 100) return 'text-green-600';
    if (responseTime < 500) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2">
          🚀 Financial Query Cache Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Experience the performance improvement with intelligent caching
        </p>
      </div>

      {/* Cache Statistics */}
      <CacheStatsDisplay showDetails={true} className="mb-6" />

      {/* Query Input */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4">Try a Financial Query</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Enter your question:
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleQuery(query)}
                placeholder="Ask a financial question..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                disabled={isLoading}
              />
              <button
                onClick={() => handleQuery(query)}
                disabled={isLoading || !query.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '⏳' : '🚀'} Ask
              </button>
            </div>
          </div>

          {/* Sample Queries */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Or try these sample queries:
            </label>
            <div className="flex flex-wrap gap-2">
              {sampleQueries.map((sampleQuery, index) => (
                <button
                  key={index}
                  onClick={() => handleSampleQuery(sampleQuery)}
                  disabled={isLoading}
                  className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md transition-colors disabled:opacity-50"
                >
                  {sampleQuery}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Response Display */}
      {(response || isLoading) && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Response</h2>
            {!isLoading && (
              <div className="flex items-center space-x-4 text-sm">
                <span className={`font-medium ${getResponseTimeColor()}`}>
                  ⚡ {responseTime}ms
                </span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  cacheHit 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                }`}>
                  {cacheHit ? '🎯 Cache Hit' : '🌐 API Call'}
                </span>
              </div>
            )}
          </div>
          
          {isLoading ? (
            <div className="flex items-center space-x-2 text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>Processing your query...</span>
            </div>
          ) : (
            <div className="prose dark:prose-invert max-w-none">
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {response}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
          💡 How to Test Cache Performance
        </h3>
        <ol className="list-decimal list-inside space-y-2 text-blue-700 dark:text-blue-300">
          <li>Ask any question above (first time will be slower - API call)</li>
          <li>Ask the exact same question again (should be instant - cache hit)</li>
          <li>Try different questions to see the performance difference</li>
          <li>Watch the cache statistics update in real-time</li>
          <li>Notice how identical questions return instantly</li>
        </ol>
        
        <div className="mt-4 p-3 bg-blue-100 dark:bg-blue-800/30 rounded-md">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong>Pro Tip:</strong> The cache considers the exact question text, so "What is Apple stock price?" 
            and "what is apple stock price?" are treated as the same question (case-insensitive matching).
          </p>
        </div>
      </div>
    </div>
  );
};

export default CacheDemo;
