import React from 'react';
import PerplexityStyleResponse from './PerplexityStyleResponse';

// Example usage of the enhanced PerplexityStyleResponse component
const ExampleUsage: React.FC = () => {
  // Example response data structure (similar to your JSON file)
  const exampleResponseData = {
    ai_response: "The provided context describes the **Aleutian Clouds**, a striking meteorological phenomenon observed over the waters off the western Aleutian Islands. Here's a detailed summary based on the source (PDF: *703154main_earth_art-ebook.pdf*, page 14):\n\n1. **Location and Conditions**: The clouds hover over the Bering Sea near the Aleutian Islands, a region known for frequent fog, heavy rains, and high winds.\n\n2. **Cloud Type**: The image, captured by Landsat 7 in 2000, shows **low marine stratocumulus clouds**, which often produce drizzle.\n\n3. **Color Variations**: The visual differences in cloud color are likely due to variations in **temperature** and the **size of water droplets** within the clouds.",
    
    sentence_analysis: [
      {
        sentence: "The provided context describes the **Aleutian Clouds**, a striking meteorological phenomenon observed over the waters off the western Aleutian Islands.",
        source_title: "703154main_earth_art-ebook.pdf",
        source_type: "new-4-chunk-0-bfd54c52",
        file_id: "1.74574E+12",
        page: "14",
        summary: "Source: E\nA\nR\nT\nH\n A\nS\n A\nR\nT\n4\nAleutian Clouds\nBering Sea \nClouds hover over the waters off the western Ale...",
        url: "N/A"
      },
      {
        sentence: "**Location and Conditions**: The clouds hover over the Bering Sea near the Aleutian Islands, a region known for frequent fog, heavy rains, and high winds.",
        source_title: "703154main_earth_art-ebook.pdf",
        source_type: "new-4-chunk-0-bfd54c52",
        file_id: "1.74574E+12",
        page: "14",
        summary: "Source: E\nA\nR\nT\nH\n A\nS\n A\nR\nT\n4\nAleutian Clouds\nBering Sea \nClouds hover over the waters off the western Ale... page: 14.0",
        url: "N/A"
      }
    ],
    
    data_language: "English",
    query_language: "English",
    
    // Backend-provided multilingual labels
    reference_labels: {
      source: "Source",
      references: "References", 
      page: "Page",
      document: "Document",
      summary: "Summary"
    }
  };

  // Example for Tamil
  const tamilExampleData = {
    ...exampleResponseData,
    data_language: "Tamil",
    reference_labels: {
      source: "மூலம்",
      references: "குறிப்புகள்",
      page: "பக்கம்",
      document: "ஆவணம்",
      summary: "சுருக்கம்"
    }
  };

  // Example for Telugu
  const teluguExampleData = {
    ...exampleResponseData,
    data_language: "Telugu",
    reference_labels: {
      source: "మూలం",
      references: "సూచనలు",
      page: "పేజీ",
      document: "పత్రం",
      summary: "సారాంశం"
    }
  };

  // Example for Kannada
  const kannadaExampleData = {
    ...exampleResponseData,
    data_language: "Kannada",
    reference_labels: {
      source: "ಮೂಲ",
      references: "ಉಲ್ಲೇಖಗಳು",
      page: "ಪುಟ",
      document: "ದಾಖಲೆ",
      summary: "ಸಾರಾಂಶ"
    }
  };

  return (
    <div className="space-y-8 p-6">
      <h1 className="text-2xl font-bold mb-6">Multilingual Reference Examples</h1>
      
      {/* English Example */}
      <div className="border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">English References</h2>
        <PerplexityStyleResponse
          response={exampleResponseData.ai_response}
          sentence_analysis={exampleResponseData.sentence_analysis}
          data_language={exampleResponseData.data_language}
          reference_labels={exampleResponseData.reference_labels}
        />
      </div>

      {/* Tamil Example */}
      <div className="border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Tamil References (தமிழ் குறிப்புகள்)</h2>
        <PerplexityStyleResponse
          response={tamilExampleData.ai_response}
          sentence_analysis={tamilExampleData.sentence_analysis}
          data_language={tamilExampleData.data_language}
          reference_labels={tamilExampleData.reference_labels}
        />
      </div>

      {/* Telugu Example */}
      <div className="border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Telugu References (తెలుగు సూచనలు)</h2>
        <PerplexityStyleResponse
          response={teluguExampleData.ai_response}
          sentence_analysis={teluguExampleData.sentence_analysis}
          data_language={teluguExampleData.data_language}
          reference_labels={teluguExampleData.reference_labels}
        />
      </div>

      {/* Kannada Example */}
      <div className="border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Kannada References (ಕನ್ನಡ ಉಲ್ಲೇಖಗಳು)</h2>
        <PerplexityStyleResponse
          response={kannadaExampleData.ai_response}
          sentence_analysis={kannadaExampleData.sentence_analysis}
          data_language={kannadaExampleData.data_language}
          reference_labels={kannadaExampleData.reference_labels}
        />
      </div>
    </div>
  );
};

export default ExampleUsage;