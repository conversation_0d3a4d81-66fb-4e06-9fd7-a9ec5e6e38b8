#!/usr/bin/env python3
"""
Fix User Access to FAISS Index

This script adds user access to the 'new' index in the pine_collection table.
"""

import os
import sqlite3
from datetime import datetime

# Database configuration
DB_PATH = os.getenv("DB_PATH", "csv_data.db")

def get_connection():
    """Get a connection to the SQLite database."""
    conn = sqlite3.connect(DB_PATH)
    conn.execute("PRAGMA foreign_keys = ON")
    return conn

def add_user_access_to_index(user_email, index_name, file_name=None):
    """
    Add user access to a specific FAISS index.
    
    Args:
        user_email: User's email address
        index_name: Name of the FAISS index
        file_name: Optional file name associated with the index
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Check if the entry already exists
        cursor.execute(
            "SELECT COUNT(*) FROM pine_collection WHERE index_name = ? AND email = ?",
            (index_name, user_email)
        )
        
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"✅ User {user_email} already has access to index '{index_name}'")
            return True
        
        # Insert new access record
        cursor.execute(
            """INSERT INTO pine_collection (index_name, email, file_name, upload_date)
               VALUES (?, ?, ?, ?)""",
            (index_name, user_email, file_name or f"{index_name}.pdf", datetime.now().isoformat())
        )
        
        conn.commit()
        conn.close()
        
        print(f"✅ Successfully granted access to user {user_email} for index '{index_name}'")
        return True
        
    except Exception as e:
        print(f"❌ Error adding user access: {str(e)}")
        return False

def list_user_access(user_email):
    """List all indexes a user has access to."""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT index_name, file_name, upload_date FROM pine_collection WHERE email = ? ORDER BY upload_date DESC",
            (user_email,)
        )
        
        rows = cursor.fetchall()
        conn.close()
        
        print(f"\n📋 Indexes accessible by {user_email}:")
        for row in rows:
            print(f"  - Index: {row[0]}, File: {row[1]}, Date: {row[2]}")
        
        return rows
        
    except Exception as e:
        print(f"❌ Error listing user access: {str(e)}")
        return []

def main():
    """Main function to fix user access."""
    user_email = "<EMAIL>"
    index_name = "new"
    file_name = "703154main_earth_art-ebook.pdf"
    
    print(f"🔧 Fixing access for user: {user_email}")
    print(f"🎯 Target index: {index_name}")
    
    # Show current access
    print("\n📋 Current access:")
    list_user_access(user_email)
    
    # Add access to the new index
    print(f"\n🔧 Adding access to '{index_name}' index...")
    success = add_user_access_to_index(user_email, index_name, file_name)
    
    if success:
        print("\n✅ Access granted successfully!")
        print("\n📋 Updated access:")
        list_user_access(user_email)
    else:
        print("\n❌ Failed to grant access")

if __name__ == "__main__":
    main()