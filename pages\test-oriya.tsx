import React, { useState } from 'react';
import Head from 'next/head';

const TestOriyaPage: React.FC = () => {
  const [testText, setTestText] = useState('');
  const [detectionResult, setDetectionResult] = useState('');

  // Function to detect if text is likely Oriya
  const isOriyaText = (text: string): boolean => {
    // Oriya Unicode range: \u0B00-\u0B7F
    const oriyaRegex = /[\u0B00-\u0B7F]/;
    return oriyaRegex.test(text);
  };

  const handleTestDetection = () => {
    const isOriya = isOriyaText(testText);
    setDetectionResult(isOriya ? 'Oriya text detected!' : 'Not Oriya text');
  };

  const sampleOriyaTexts = [
    'ଭାରତରେ ଆଜିର ଷ୍ଟକ ମାର୍କେଟ କଣ ଅବସ୍ଥା?',
    'ମୁଦ୍ରାସ୍ଫୀତି ହାର କେତେ?',
    'ଛୋଟ ନିବେଶକମାନଙ୍କ ପାଇଁ ସର୍ବୋତ୍ତମ ବିକଳ୍ପ କଣ?',
    'ଗୃହ ଋଣ ସୁଧ ହାର କେତେ?',
    'ଡିଜିଟାଲ ପେମେଣ୍ଟର ସୁରକ୍ଷା କଣ?'
  ];

  return (
    <>
      <Head>
        <title>Oriya Language Test</title>
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Oriya:wght@400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </Head>
      
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8 text-orange-800">
            Oriya Language Functionality Test
          </h1>

          {/* Language Detection Test */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Language Detection Test
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enter text to test:
                </label>
                <textarea
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  rows={3}
                  placeholder="Type or paste text here..."
                  style={{ fontFamily: 'Noto Sans Oriya, serif' }}
                />
              </div>
              
              <button
                onClick={handleTestDetection}
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
              >
                Test Detection
              </button>
              
              {detectionResult && (
                <div className={`p-3 rounded-lg ${detectionResult.includes('detected') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {detectionResult}
                </div>
              )}
            </div>
          </div>

          {/* Sample Oriya Texts */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Sample Oriya Financial Queries
            </h2>
            
            <div className="space-y-3">
              {sampleOriyaTexts.map((text, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-3">
                  <p 
                    className="text-gray-700 mb-2"
                    style={{ fontFamily: 'Noto Sans Oriya, serif' }}
                  >
                    {text}
                  </p>
                  <button
                    onClick={() => {
                      setTestText(text);
                      setDetectionResult('');
                    }}
                    className="text-orange-600 hover:text-orange-700 text-sm underline"
                  >
                    Use this text
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Font Rendering Test */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Font Rendering Test
            </h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-700 mb-2">With Noto Sans Oriya:</h3>
                <p 
                  className="text-lg p-3 bg-orange-50 rounded border"
                  style={{ fontFamily: 'Noto Sans Oriya, serif' }}
                >
                  ଭାରତରେ ଆଜିର ଷ୍ଟକ ମାର୍କେଟ କଣ ଅବସ୍ଥା? ମୁଦ୍ରାସ୍ଫୀତି ହାର କେତେ ଏବଂ ଏହା କିପରି ସାଧାରଣ ଲୋକଙ୍କୁ ପ୍ରଭାବିତ କରୁଛି?
                </p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-700 mb-2">Without specific font:</h3>
                <p className="text-lg p-3 bg-gray-50 rounded border">
                  ଭାରତରେ ଆଜିର ଷ୍ଟକ ମାର୍କେଟ କଣ ଅବସ୍ଥା? ମୁଦ୍ରାସ୍ଫୀତି ହାର କେତେ ଏବଂ ଏହା କିପରି ସାଧାରଣ ଲୋକଙ୍କୁ ପ୍ରଭାବିତ କରୁଛି?
                </p>
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="mt-8 text-center space-x-4">
            <a
              href="/oriya"
              className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors inline-block"
            >
              Go to Oriya Translation Page
            </a>
            <a
              href="/oriya-financial"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-block"
            >
              Go to Oriya Financial Page
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

export default TestOriyaPage;