"""
Tamil Processing Configuration
Optimized settings for better Tamil text processing.
"""

# Tamil processing configuration
TAMIL_CONFIG = {
    # Disable aggressive corruption detection for Tamil
    'disable_corruption_detection': True,
    
    # Preserve content ratio (minimum % of original content to keep)
    'content_preservation_ratio': 0.95,
    
    # Fast processing mode
    'fast_processing_mode': True,
    
    # Conservative cleaning thresholds
    'corruption_thresholds': {
        'tamil': {
            'normalized_diversity_min': 0.1,  # Very lenient
            'complexity_min': 0.02,
            'repetition_max': 0.8,
            'entropy_min': 0.5,
            'single_char_dominance_max': 0.7
        }
    },
    
    # Skip translation for direct Tamil processing
    'skip_translation_for_tamil': True,
    
    # Minimal cleaning patterns (only remove obvious corruption)
    'minimal_cleaning_patterns': [
        r'__[A-Z_]+__',
        r'\[CAPITAL_WORD\]',
        r'\d+_\[CAPITAL_WORD\]_CAPWORD',
        r'\*\.?\d*\*'
    ],
    
    # Performance optimizations
    'performance_optimizations': {
        'cache_tamil_responses': True,
        'batch_processing': True,
        'parallel_processing': False,  # Disable for stability
        'response_timeout': 15  # Reduced timeout
    }
}

def apply_tamil_optimizations():
    """Apply Tamil-specific optimizations to the system."""
    import os
    import sys
    
    # Add the backend path to Python path
    backend_path = os.path.join(os.path.dirname(__file__), 'python-fiass-backend')
    if backend_path not in sys.path:
        sys.path.append(backend_path)
    
    try:
        # Try to import and configure the backend
        print("🔧 Applying Tamil optimizations...")
        
        # Set environment variables for Tamil optimization
        os.environ['TAMIL_OPTIMIZATION_ENABLED'] = 'true'
        os.environ['DISABLE_AGGRESSIVE_CLEANING'] = 'true'
        os.environ['TAMIL_CONTENT_PRESERVATION'] = '0.95'
        
        print("✅ Tamil optimization environment variables set")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Could not apply all optimizations: {str(e)}")
        return False

def get_optimized_cleaning_function():
    """Get the optimized text cleaning function for Tamil."""
    import re
    import unicodedata
    
    def clean_tamil_text_optimized(text):
        """Optimized Tamil text cleaning."""
        if not text or not isinstance(text, str) or len(text.strip()) < 5:
            return text
        
        # Check if it's Tamil text
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        if not tamil_pattern.search(text):
            return text  # Not Tamil, return as-is
        
        cleaned_text = text
        
        # Only remove obvious corruption patterns
        for pattern in TAMIL_CONFIG['minimal_cleaning_patterns']:
            cleaned_text = re.sub(pattern, '', cleaned_text)
        
        # Fix only extreme repetition (4+ consecutive identical words)
        cleaned_text = re.sub(r'(\b[\u0B80-\u0BFF]+\b)(\s+\1){3,}', r'\1', cleaned_text)
        
        # Clean up excessive whitespace
        cleaned_text = re.sub(r'\s{3,}', ' ', cleaned_text)
        
        # Normalize Unicode
        cleaned_text = unicodedata.normalize('NFC', cleaned_text)
        
        # Only return cleaned version if we preserve enough content
        preservation_ratio = len(cleaned_text) / len(text) if len(text) > 0 else 1.0
        if preservation_ratio >= TAMIL_CONFIG['content_preservation_ratio']:
            return cleaned_text.strip()
        else:
            return text  # Return original if too much content was removed
    
    return clean_tamil_text_optimized

if __name__ == "__main__":
    # Test the optimization
    apply_tamil_optimizations()
    
    # Test the cleaning function
    clean_func = get_optimized_cleaning_function()
    
    test_text = """நெருக்கமான மதிப்பீடுகள் 1995 இல் கண்கவர் செயல்திறன் மூலம் வெற்றி நிரூபிக்கப்பட்டுள்ளது, இது பாரிஸ் ஏர் ஷோவில் கண்கவர் செயல்திறன் திறனைக் காட்டியது, பாரம்பரிய ஸ்டால் கட்டுப்படுத்தப்படுகிறது அப்பால் (*ஸ்டால.30.30ப.30.30ட.30.30எஃப், பக.30.208, 354*).112. 3. 4. நெறிப்படுத்தப்பட்ட விவரக்குறிப்புகள் நடைமுறைகள் அதிகாரத்துவ."""
    
    print("Original length:", len(test_text))
    cleaned = clean_func(test_text)
    print("Cleaned length:", len(cleaned))
    print("Preservation ratio:", len(cleaned) / len(test_text))
    print("Cleaned text:", cleaned[:100] + "...")