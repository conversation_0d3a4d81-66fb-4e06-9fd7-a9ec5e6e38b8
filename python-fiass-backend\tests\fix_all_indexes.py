#!/usr/bin/env python3
"""
Script to ensure all indexes are properly recorded in the database.
"""

import os
import sys

# Add the python-fiass-backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

import database

def fix_all_indexes():
    """Ensure all indexes are properly recorded in the database."""
    try:
        conn = database.get_connection()
        cursor = conn.cursor()
        
        # Define all the indexes that should exist
        indexes_to_add = [
            {
                'index_name': 'tamil',
                'email': '<EMAIL>',
                'file_name': 'dinamalar_dataset_first_500.xlsx',
                'columns_json': '["news_id", "news_date", "news_category", "news_title", "news_article"]',
                'row_count': 500,
                'detected_language': 'Tamil'
            },
            {
                'index_name': 'ron',
                'email': '<EMAIL>',
                'file_name': '703154main_earth_art-ebook.pdf',
                'columns_json': '[]',
                'row_count': 253,
                'detected_language': 'English'
            },
            {
                'index_name': 'robin',
                'email': '<EMAIL>',
                'file_name': 'pdf_index_new - Edited.csv',
                'columns_json': '[]',
                'row_count': 253,
                'detected_language': 'English'
            }
        ]
        
        for index_info in indexes_to_add:
            # Add to pine_collection
            cursor.execute('''
            INSERT OR REPLACE INTO pine_collection (index_name, email)
            VALUES (?, ?)
            ''', (index_info['index_name'], index_info['email']))
            
            # Add to excel_files
            cursor.execute('''
            INSERT OR REPLACE INTO excel_files 
            (file_name, index_name, client_id, columns_json, row_count, 
             embedding_model, embedding_dimension, detected_language, chunks_created)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                index_info['file_name'],
                index_info['index_name'],
                index_info['email'],
                index_info['columns_json'],
                index_info['row_count'],
                'all-MiniLM-L6-v2',
                384,
                index_info['detected_language'],
                index_info['row_count']
            ))
            
            print(f"✅ Added/Updated {index_info['index_name']} for {index_info['email']}")
        
        conn.commit()
        
        # Verify all records
        print('\n=== FINAL PINE COLLECTION ===')
        cursor.execute('SELECT * FROM pine_collection ORDER BY email, index_name')
        pine_records = cursor.fetchall()
        for record in pine_records:
            print(f'  {record}')
        
        print('\n=== FINAL EXCEL FILES ===')
        cursor.execute('SELECT id, file_name, index_name, client_id FROM excel_files ORDER BY client_id, index_name')
        excel_records = cursor.fetchall()
        for record in excel_records:
            print(f'  {record}')
        
        # Test user-specific queries
        print('\n=== USER-SPECIFIC INDEX TESTS ===')
        
        # <NAME_EMAIL>
        cursor.execute('SELECT index_name FROM pine_collection WHERE email = ? ORDER BY index_name', ('<EMAIL>',))
        user1_indexes = [row[0] for row in cursor.fetchall()]
        print(f'<EMAIL> indexes: {user1_indexes}')
        
        # <NAME_EMAIL>
        cursor.execute('SELECT index_name FROM pine_collection WHERE email = ? ORDER BY index_name', ('<EMAIL>',))
        user2_indexes = [row[0] for row in cursor.fetchall()]
        print(f'<EMAIL> indexes: {user2_indexes}')
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing indexes: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing all index database records...")
    
    if fix_all_indexes():
        print("\n✅ All indexes fixed successfully!")
        print("Now test the API to see if ron index <NAME_EMAIL>")
    else:
        print("\n❌ Fix failed. Please check the error messages above.")