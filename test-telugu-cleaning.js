// Test script to verify Telugu text cleaning
const testText = `A*.21* సూపర్సోనిక్ వ15ానం అనేది ధ్వని వేగం కంటే వేగంగా ఎగురుతున్న ఎగురుతున్న విమానం విమానం (మాక్ 1, సముద్ర మట్టంలో మట్టంలో 767 mph లేదా 1,235 కిమీ/గం).16ఈ విమానాలు ధ్వని ధ్వని అవరోధాన్ని విచ్ఛిన్నం చేస్తున్నప్పుడు షాక్ ఉత్పత్తి ఉత్పత్తి చేస్తాయి చేస్తాయి, దీని ఫలితంగా సోనిక్ బూమ్ బూమ్ -బిగ్గరగా, పేలుడు శబ్దం నేలమీ.16.16
ముఖ్య లక్షణాలు సూపర్సోనిక్ విమానం విమానం:
.16.16.16. 4_[CAPITAL_WORD]_CAPWORD నిషేధాలు ఓవర్‌ల్యాండ్ డ్యూ శబ్దం శబ్దం) మిగిలి ఉన్నాయి ఉన్నాయి, కానీ పురోగతి తగ్గింపు తగ్గింపు ఇంధన వాణిజ్య ప్రయాణాన్ని 2030 లను.`;

function cleanTextContent(content) {
    if (!content || typeof content !== 'string') return content;
    
    // Detect if this is regional language content
    const isRegionalLanguage = /[\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/.test(content);
    
    if (isRegionalLanguage) {
        // OPTIMIZED CLEANING: Preserve maximum content for regional languages
        const originalLength = content.length;
        
        let cleaned = content
            // Fix Unicode normalization first
            .normalize('NFC')
            // Remove only obvious corruption patterns
            .replace(/__[A-Z_]+__/g, '')
            .replace(/\[CAPITAL_WORD\]/g, '')
            .replace(/\d+_\[CAPITAL_WORD\]_CAPWORD/g, '')
            .replace(/\*\.?\d*\*/g, '')
            // Fix only extreme repetition (3+ consecutive identical words)
            .replace(/([\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF]+)(\s+\1){2,}/g, '$1')
            // Clean up excessive whitespace only
            .replace(/\s{3,}/g, ' ')
            .replace(/\n{3,}/g, '\n\n')
            // Fix only clearly broken patterns
            .replace(/\.(\d+)\.(\d+)\./g, '. ')
            .trim();
        
        // Only use cleaned version if we preserve at least 90% of content
        const preservationRatio = cleaned.length / originalLength;
        if (preservationRatio >= 0.9) {
            console.log(`✅ Optimized cleaning preserved ${(preservationRatio * 100).toFixed(1)}% of content`);
            return cleaned;
        } else {
            console.log(`⚠️ Cleaning would remove too much content (${(preservationRatio * 100).toFixed(1)}%), keeping original`);
            return content;
        }
    }
    
    return content;
}

// New optimized function specifically for Tamil
function cleanTamilTextOptimized(content) {
    if (!content || typeof content !== 'string') return content;
    
    // Check if it's Tamil text
    const isTamil = /[\u0B80-\u0BFF]/.test(content);
    if (!isTamil) return content;
    
    console.log(`🔧 Applying optimized Tamil cleaning to ${content.length} characters`);
    
    const originalLength = content.length;
    
    // Very conservative cleaning - only remove obvious corruption
    let cleaned = content
        .normalize('NFC')
        // Remove only placeholder patterns
        .replace(/__[A-Z_]+__/g, '')
        .replace(/\[CAPITAL_WORD\]/g, '')
        .replace(/\*\.?\d*\*/g, '')
        // Fix only extreme repetition (4+ consecutive words)
        .replace(/([\u0B80-\u0BFF]+)(\s+\1){3,}/g, '$1')
        // Minimal whitespace cleanup
        .replace(/\s{4,}/g, ' ')
        .trim();
    
    const preservationRatio = cleaned.length / originalLength;
    
    console.log(`📊 Tamil cleaning stats:`);
    console.log(`   Original: ${originalLength} chars`);
    console.log(`   Cleaned: ${cleaned.length} chars`);
    console.log(`   Preserved: ${(preservationRatio * 100).toFixed(1)}%`);
    
    // Use cleaned version only if we preserve 95%+ content
    return preservationRatio >= 0.95 ? cleaned : content;
}

// Test with Tamil text
const tamilTestText = `நெருக்கமான மதிப்பீடுகள் 1995 இல் கண்கவர் செயல்திறன் மூலம் வெற்றி நிரூபிக்கப்பட்டுள்ளது, இது பாரிஸ் ஏர் ஷோவில் கண்கவர் செயல்திறன் திறனைக் காட்டியது, பாரம்பரிய ஸ்டால் கட்டுப்படுத்தப்படுகிறது அப்பால் (*ஸ்டால.30.30ப.30.30ட.30.30எஃப், பக.30.208, 354*).112. 3. 4. நெறிப்படுத்தப்பட்ட விவரக்குறிப்புகள் நடைமுறைகள் அதிகாரத்துவ. இணை இருப்பிட குழுக்கள் தகவல்தொடர்பு சிக்கலைத் 220, 336*). ### முதன்மை குறிக்கோள்கள் குறிக்கோள்கள் 1. 2.208, 354*).1127ஒத்துழைப்பு செயல்திறனை. யு. ஆயினும்கூட, மரபு நவீன சூப்பர்மேன்யூவலபிள். (ஆதாரங்கள்:பறக்கும் பி. டி. எஃப்*, 220, 235, 336,`;

console.log("🧪 TESTING TELUGU TEXT CLEANING");
console.log("=" * 50);
console.log("Original Telugu text:");
console.log(testText);
console.log("\nCleaned Telugu text:");
console.log(cleanTextContent(testText));

console.log("\n" + "=".repeat(50) + "\n");

console.log("🧪 TESTING TAMIL TEXT CLEANING (OPTIMIZED)");
console.log("=" * 50);
console.log("Original Tamil text:");
console.log(tamilTestText);
console.log("\nOptimized Tamil cleaning:");
console.log(cleanTamilTextOptimized(tamilTestText));
console.log("\nStandard cleaning:");
console.log(cleanTextContent(tamilTestText));

// Performance comparison
console.log("\n" + "=".repeat(50) + "\n");
console.log("📊 PERFORMANCE COMPARISON");

const startTime1 = Date.now();
const result1 = cleanTamilTextOptimized(tamilTestText);
const time1 = Date.now() - startTime1;

const startTime2 = Date.now();
const result2 = cleanTextContent(tamilTestText);
const time2 = Date.now() - startTime2;

console.log(`Optimized cleaning: ${time1}ms, ${result1.length} chars`);
console.log(`Standard cleaning: ${time2}ms, ${result2.length} chars`);
console.log(`Speed improvement: ${time2 > 0 ? ((time2 - time1) / time2 * 100).toFixed(1) : 0}%`);
console.log(`Content preservation: ${(result1.length / tamilTestText.length * 100).toFixed(1)}%`);