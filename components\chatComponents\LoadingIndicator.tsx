// components/chatComponents/LoadingIndicator.tsx
import React from "react";
import Image from "next/image";
import logo from "@/public/images/favicon.png";
import { PiSparkle, PiChecks } from "react-icons/pi";

interface LoadingIndicatorProps {
  message?: string;
  showSteps?: boolean;
  showAvatar?: boolean;
  inline?: boolean;
  timestamp?: string;
  showPulse?: boolean;
}

// Professional SVG Loading Animation Component
const SVGLoadingAnimation: React.FC<{ className?: string; variant?: 'default' | 'brain' | 'professional' }> = ({
  className = "",
  variant = 'professional'
}) => {
  if (variant === 'professional') {
    return (
      <svg
        width="32"
        height="32"
        viewBox="0 0 32 32"
        className={`${className}`}
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="currentColor" stopOpacity="0.8" />
            <stop offset="50%" stopColor="currentColor" stopOpacity="1" />
            <stop offset="100%" stopColor="currentColor" stopOpacity="0.6" />
          </linearGradient>
          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="currentColor" stopOpacity="0.3" />
            <stop offset="50%" stopColor="currentColor" stopOpacity="0.8" />
            <stop offset="100%" stopColor="currentColor" stopOpacity="0.3" />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        <style>
          {`
            .pro-outer-ring {
              animation: proRotate 3s linear infinite;
              transform-origin: 16px 16px;
              filter: url(#glow);
            }

            .pro-inner-ring {
              animation: proRotateReverse 2s linear infinite;
              transform-origin: 16px 16px;
            }

            .pro-center-pulse {
              animation: proPulse 1.5s ease-in-out infinite;
              transform-origin: 16px 16px;
            }

            .pro-orbit-dot {
              animation: proOrbit 2.5s ease-in-out infinite;
            }

            .pro-orbit-dot-1 { animation-delay: 0s; }
            .pro-orbit-dot-2 { animation-delay: 0.4s; }
            .pro-orbit-dot-3 { animation-delay: 0.8s; }
            .pro-orbit-dot-4 { animation-delay: 1.2s; }

            @keyframes proRotate {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }

            @keyframes proRotateReverse {
              0% { transform: rotate(360deg); }
              100% { transform: rotate(0deg); }
            }

            @keyframes proPulse {
              0%, 100% {
                transform: scale(1);
                opacity: 0.8;
              }
              50% {
                transform: scale(1.2);
                opacity: 1;
              }
            }

            @keyframes proOrbit {
              0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
              }
              50% {
                opacity: 1;
                transform: scale(1.3);
              }
            }
          `}
        </style>

        {/* Outer rotating ring with gradient */}
        <circle
          className="pro-outer-ring"
          cx="16"
          cy="16"
          r="12"
          fill="none"
          stroke="url(#gradient1)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="20 10"
          opacity="0.7"
        />

        {/* Inner counter-rotating ring */}
        <circle
          className="pro-inner-ring"
          cx="16"
          cy="16"
          r="8"
          fill="none"
          stroke="url(#gradient2)"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeDasharray="15 5"
          opacity="0.5"
        />

        {/* Center pulsing core */}
        <circle
          className="pro-center-pulse"
          cx="16"
          cy="16"
          r="3"
          fill="url(#gradient1)"
          opacity="0.9"
        />

        {/* Orbiting dots */}
        <circle className="pro-orbit-dot pro-orbit-dot-1" cx="16" cy="4" r="1.5" fill="currentColor" />
        <circle className="pro-orbit-dot pro-orbit-dot-2" cx="28" cy="16" r="1.5" fill="currentColor" />
        <circle className="pro-orbit-dot pro-orbit-dot-3" cx="16" cy="28" r="1.5" fill="currentColor" />
        <circle className="pro-orbit-dot pro-orbit-dot-4" cx="4" cy="16" r="1.5" fill="currentColor" />
      </svg>
    );
  }

  if (variant === 'brain') {
    return (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        className={`${className}`}
        xmlns="http://www.w3.org/2000/svg"
      >
        <style>
          {`
            .brain-wave {
              animation: brainWave 2s ease-in-out infinite;
            }
            .brain-wave-1 { animation-delay: 0s; }
            .brain-wave-2 { animation-delay: 0.2s; }
            .brain-wave-3 { animation-delay: 0.4s; }
            .brain-wave-4 { animation-delay: 0.6s; }
            .brain-wave-5 { animation-delay: 0.8s; }

            .brain-core {
              animation: brainPulse 1.5s ease-in-out infinite;
            }

            @keyframes brainWave {
              0%, 100% { opacity: 0.2; transform: scale(0.8); }
              50% { opacity: 1; transform: scale(1.1); }
            }

            @keyframes brainPulse {
              0%, 100% { opacity: 0.6; }
              50% { opacity: 1; }
            }
          `}
        </style>

        {/* Brain-like neural network pattern */}
        <circle className="brain-core" cx="12" cy="12" r="2" fill="currentColor" />
        <circle className="brain-wave brain-wave-1" cx="6" cy="8" r="1" fill="currentColor" />
        <circle className="brain-wave brain-wave-2" cx="18" cy="8" r="1" fill="currentColor" />
        <circle className="brain-wave brain-wave-3" cx="6" cy="16" r="1" fill="currentColor" />
        <circle className="brain-wave brain-wave-4" cx="18" cy="16" r="1" fill="currentColor" />
        <circle className="brain-wave brain-wave-5" cx="12" cy="6" r="1" fill="currentColor" />

        {/* Connecting lines */}
        <line className="brain-wave brain-wave-1" x1="12" y1="12" x2="6" y2="8" stroke="currentColor" strokeWidth="0.5" opacity="0.4" />
        <line className="brain-wave brain-wave-2" x1="12" y1="12" x2="18" y2="8" stroke="currentColor" strokeWidth="0.5" opacity="0.4" />
        <line className="brain-wave brain-wave-3" x1="12" y1="12" x2="6" y2="16" stroke="currentColor" strokeWidth="0.5" opacity="0.4" />
        <line className="brain-wave brain-wave-4" x1="12" y1="12" x2="18" y2="16" stroke="currentColor" strokeWidth="0.5" opacity="0.4" />
        <line className="brain-wave brain-wave-5" x1="12" y1="12" x2="12" y2="6" stroke="currentColor" strokeWidth="0.5" opacity="0.4" />
      </svg>
    );
  }

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      className={`${className}`}
      xmlns="http://www.w3.org/2000/svg"
    >
      <style>
        {`
          .spinner-ring {
            animation: spin 2s linear infinite;
            transform-origin: 12px 12px;
          }
          .spinner-dots {
            animation: pulse 1.5s ease-in-out infinite;
          }
          .spinner-dot-1 { animation-delay: 0s; }
          .spinner-dot-2 { animation-delay: 0.3s; }
          .spinner-dot-3 { animation-delay: 0.6s; }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          @keyframes pulse {
            0%, 100% { opacity: 0.3; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.2); }
          }
        `}
      </style>

      {/* Outer rotating ring */}
      <circle
        className="spinner-ring"
        cx="12"
        cy="12"
        r="10"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeDasharray="31.416"
        strokeDashoffset="15.708"
        opacity="0.3"
      />

      {/* Inner pulsing dots */}
      <circle className="spinner-dots spinner-dot-1" cx="8" cy="12" r="1.5" fill="currentColor" />
      <circle className="spinner-dots spinner-dot-2" cx="12" cy="12" r="1.5" fill="currentColor" />
      <circle className="spinner-dots spinner-dot-3" cx="16" cy="12" r="1.5" fill="currentColor" />
    </svg>
  );
};

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  message = "Thinking",
  showSteps = false,
  showAvatar = true,
  inline = false,
  timestamp,
  showPulse = false
}) => {
  const formattedTime = timestamp
    ? new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    : 'processing...';

  // Inline loading indicator with SVG animation only
  if (inline) {
    return (
      <span className="inline-flex items-center justify-center">
        <SVGLoadingAnimation variant="professional" className="text-primaryColor w-8 h-8" />
      </span>
    );
  }

  // Full loading indicator with avatar and container
  return (
    <div className="flex justify-start items-start gap-1 sm:gap-3 w-full max-w-[90%]">
      {showAvatar && (
        <Image src={logo} alt="AIQuill logo" className="max-sm:size-5 object-cover" width={32} height={32} />
      )}
      <div className="flex flex-col justify-start items-start gap-3 flex-1">
          <SVGLoadingAnimation variant="professional" className="text-secondaryColor w-10 h-10" />
      </div>
    </div>
  );
};

export default LoadingIndicator;