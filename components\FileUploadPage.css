.file-upload-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
}

.file-upload-header {
  text-align: center;
  margin-bottom: 40px;
}

.file-upload-header h1 {
  font-size: 32px;
  color: #333;
  margin-bottom: 10px;
}

.file-upload-header p {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
}

.upload-restriction-badge {
  display: inline-block;
  background-color: #4a90e2;
  color: white;
  font-size: 14px;
  font-weight: 500;
  padding: 5px 15px;
  border-radius: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(74, 144, 226, 0.3);
}

.api-key-section {
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.api-key-container {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.api-key-label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
}

.required-field {
  color: #f44336;
  margin-left: 3px;
}

.api-key-input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
  margin-bottom: 10px;
}

.api-key-input:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.api-key-error {
  color: #F44336;
  font-size: 14px;
  margin-top: 5px;
  margin-bottom: 10px;
}

.api-key-info {
  font-size: 12px;
  color: #666;
  margin-top: 10px;
}

.api-key-help {
  margin-top: 5px;
}

.api-key-help a {
  color: #4a90e2;
  text-decoration: none;
  transition: color 0.2s;
}

.api-key-help a:hover {
  color: #2a70c2;
  text-decoration: underline;
}

.api-key-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.manage-indexes-btn {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.manage-indexes-btn:hover {
  background-color: #e0e0e0;
}

.indexes-container {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.indexes-container h3 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
}

.indexes-loading {
  display: flex;
  align-items: center;
  padding: 15px 0;
  color: #666;
}

.indexes-loading .spinner {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.indexes-error {
  color: #F44336;
  padding: 10px;
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: 4px;
  margin-bottom: 15px;
}

.no-indexes {
  color: #666;
  padding: 15px 0;
  font-style: italic;
}

.indexes-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 10px;
  background-color: rgba(33, 150, 243, 0.1);
  border-radius: 4px;
}

.indexes-list ul {
  list-style: none;
  padding: 0;
  margin: 0 0 15px 0;
}

.index-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: white;
  transition: background-color 0.2s;
}

.index-item:hover {
  background-color: #f9f9f9;
}

.index-info {
  display: flex;
  flex-direction: column;
}

.index-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.index-details {
  font-size: 12px;
  color: #666;
}

.delete-index-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 70px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-index-btn:hover {
  background-color: #d32f2f;
}

.delete-index-btn:disabled {
  background-color: #ffcdd2;
  cursor: not-allowed;
}

.spinner-small {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

.refresh-indexes-btn {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-indexes-btn:hover {
  background-color: #357abf;
}

.refresh-indexes-btn:disabled {
  background-color: #bbdefb;
  cursor: not-allowed;
}

.file-upload-section {
  margin-bottom: 60px;
}

.client-selection {
  margin: 20px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.client-selection label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
}

.client-dropdown {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.3s;
  margin-bottom: 20px;
  cursor: pointer;
}

.client-dropdown:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.submit-button {
  display: block;
  width: 100%;
  max-width: 600px;
  margin: 0 auto 30px auto;
  padding: 14px 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submit-button:hover {
  background-color: #357abf;
}

.submit-button:disabled {
  background-color: #bbdefb;
  cursor: not-allowed;
}

/* Upload Status Styling */
.upload-status-container {
  margin-top: 40px;
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.dark .upload-status-container {
  background-color: #1a1a1a;
  color: #e0e0e0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid #333;
}

.upload-status-container h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.dark .upload-status-container h3 {
  color: #e0e0e0;
  border-bottom: 1px solid #333;
}

.upload-status-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.upload-status-item {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.dark .upload-status-item {
  background-color: #2a2a2a;
  color: #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #333;
}

.upload-status-item.success {
  border-left: 4px solid #4CAF50;
}

.upload-status-item.error {
  border-left: 4px solid #F44336;
}

.upload-status-item.uploading {
  border-left: 4px solid #2196F3;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.file-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.dark .file-name {
  color: #e0e0e0;
}

.file-status {
  display: flex;
  align-items: center;
}

.uploading {
  color: #2196F3;
  display: flex;
  align-items: center;
}

.dark .uploading {
  color: #64b5f6; /* Lighter blue for dark mode */
}

.success {
  color: #4CAF50;
  font-weight: 500;
}

.dark .success {
  color: #81c784; /* Lighter green for dark mode */
}

.error {
  color: #F44336;
  font-weight: 500;
}

.dark .error {
  color: #e57373; /* Lighter red for dark mode */
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(33, 150, 243, 0.3);
  border-radius: 50%;
  border-top-color: #2196F3;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.dark .spinner {
  border: 2px solid rgba(100, 181, 246, 0.2);
  border-top-color: #64b5f6;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.progress-container {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  margin-bottom: 15px;
  overflow: hidden;
}

.dark .progress-container {
  background-color: #424242;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #2196F3, #03A9F4);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.dark .progress-bar {
  background: linear-gradient(90deg, #64b5f6, #29b6f6);
}

.status-message {
  margin: 10px 0;
  font-size: 14px;
  color: #666;
}

.dark .status-message {
  color: #b0b0b0;
}

/* Processing Stages */
.processing-stages {
  display: flex;
  margin: 25px 0;
  position: relative;
  padding: 10px 0;
}

.stage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.stage-indicator {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  z-index: 2;
  transition: all 0.3s ease;
}

.dark .stage-indicator {
  background-color: #333;
  color: #e0e0e0;
}

.stage-item.active .stage-indicator {
  background-color: #2196F3;
  color: white;
  box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.2);
}

.dark .stage-item.active .stage-indicator {
  background-color: #64b5f6;
  box-shadow: 0 0 0 4px rgba(100, 181, 246, 0.2);
}

.stage-item.completed .stage-indicator {
  background-color: #4CAF50;
  color: white;
}

.dark .stage-item.completed .stage-indicator {
  background-color: #81c784;
}

.stage-number, .stage-check {
  font-size: 14px;
  font-weight: 600;
}

.stage-info {
  text-align: center;
  max-width: 120px;
}

.stage-info h4 {
  font-size: 14px;
  margin-bottom: 5px;
  color: #333;
}

.dark .stage-info h4 {
  color: #e0e0e0;
}

.stage-info p {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.dark .stage-info p {
  color: #b0b0b0;
}

.stage-connector {
  position: absolute;
  top: 15px;
  left: 50%;
  right: -50%;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

.dark .stage-connector {
  background-color: #424242;
}

.stage-connector.active, .stage-connector.completed {
  background-color: #4CAF50;
}

.dark .stage-connector.active,
.dark .stage-connector.completed {
  background-color: #81c784;
}

/* Success Container */
.success-container {
  margin-top: 20px;
  animation: fadeIn 0.5s ease;
}

.success-message {
  display: flex;
  align-items: center;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.dark .success-message {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.success-icon {
  width: 40px;
  height: 40px;
  background-color: #4CAF50;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin-right: 15px;
  flex-shrink: 0;
}

.dark .success-icon {
  background-color: #388e3c;
}

.success-text h4 {
  font-size: 16px;
  margin-bottom: 5px;
  color: #333;
}

.dark .success-text h4 {
  color: #e0e0e0;
}

.success-text p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.dark .success-text p {
  color: #b0b0b0;
}

/* Success Details */
.success-details {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.dark .success-details {
  background-color: #2a2a2a;
  color: #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #333;
}

.success-details h4 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.dark .success-details h4 {
  color: #e0e0e0;
  border-bottom: 1px solid #333;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.dark .detail-label {
  color: #b0b0b0;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.dark .detail-value {
  color: #e0e0e0;
}

/* Error Container */
.error-container {
  margin-top: 20px;
  animation: fadeIn 0.5s ease;
}

.error-message {
  display: flex;
  align-items: center;
  background-color: rgba(244, 67, 54, 0.1);
  padding: 15px;
  border-radius: 8px;
}

.dark .error-message {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.error-icon {
  width: 40px;
  height: 40px;
  background-color: #F44336;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin-right: 15px;
  flex-shrink: 0;
}

.dark .error-icon {
  background-color: #d32f2f;
}

.error-text h4 {
  font-size: 16px;
  margin-bottom: 5px;
  color: #333;
}

.dark .error-text h4 {
  color: #e0e0e0;
}

.error-text p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.dark .error-text p {
  color: #b0b0b0;
}

.error-action-hint {
  margin-top: 15px;
  padding: 10px;
  background-color: rgba(255, 235, 238, 0.5);
  border-left: 3px solid #f44336;
  border-radius: 4px;
}

.dark .error-action-hint {
  background-color: rgba(244, 67, 54, 0.15);
  border-left: 3px solid #e57373;
}

.error-action-hint p {
  margin-bottom: 8px;
  font-size: 13px;
  color: #555;
}

.dark .error-action-hint p {
  color: #b0b0b0;
}

.error-action-hint p:last-child {
  margin-bottom: 0;
}

/* Info Section */
.info-section {
  margin-top: 60px;
  padding-top: 40px;
  border-top: 1px solid #eee;
}

.dark .info-section {
  border-top: 1px solid #333;
}

.info-section h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.dark .info-section h3 {
  color: #e0e0e0;
}

.info-section > p {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 30px;
  font-size: 16px;
  color: #666;
  line-height: 1.6;
}

.dark .info-section > p {
  color: #b0b0b0;
}

.info-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

.info-card {
  flex: 1;
  min-width: 250px;
  max-width: 350px;
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .info-card {
  background-color: #2a2a2a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 1px solid #333;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.dark .info-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.info-card h4 {
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
}

.dark .info-card h4 {
  color: #e0e0e0;
}

.info-card p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.dark .info-card p {
  color: #b0b0b0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .file-upload-header h1 {
    font-size: 28px;
  }

  .file-upload-header p {
    font-size: 14px;
  }

  .upload-restriction-badge {
    font-size: 12px;
    padding: 4px 12px;
  }

  .api-key-section {
    padding: 0 15px;
  }

  .api-key-container {
    padding: 15px;
  }

  .api-key-label {
    font-size: 14px;
  }

  .api-key-input {
    padding: 10px;
  }

  .client-selection {
    padding: 0 15px;
  }

  .client-dropdown {
    padding: 10px;
  }

  .submit-button {
    padding: 12px 16px;
    font-size: 14px;
  }

  .index-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .index-info {
    margin-bottom: 10px;
    width: 100%;
  }

  .delete-index-btn {
    align-self: flex-end;
  }

  .info-cards {
    flex-direction: column;
    align-items: center;
  }

  .info-card {
    width: 100%;
  }

  .processing-stages {
    flex-direction: column;
    align-items: flex-start;
  }

  .stage-item {
    flex-direction: row;
    margin-bottom: 15px;
    width: 100%;
  }

  .stage-indicator {
    margin-right: 15px;
    margin-bottom: 0;
  }

  .stage-info {
    text-align: left;
    max-width: none;
  }

  .stage-connector {
    display: none;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }
}
