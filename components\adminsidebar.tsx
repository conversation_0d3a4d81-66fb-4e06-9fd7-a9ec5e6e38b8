"use client";
import React from 'react';
import { useRouter } from 'next/navigation';
import { PiDatabase, PiPlus, PiList, PiGear } from 'react-icons/pi';

interface AdminSidebarProps {
  currentView?: string;
  onViewChange?: (view: string) => void;
  isOpen?: boolean;
  onToggle?: () => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({
  currentView = 'create',
  onViewChange,
  isOpen = true,
  onToggle
}) => {
  const router = useRouter();

  const handleNavigation = (view: string) => {
    if (view === 'show') {
      // Navigate to the show-index route
      router.push('/show-index');
    } else if (view === 'create') {
      // Navigate to the file-upload-standalone route
      router.push('/file-upload-standalone');
    } else if (onViewChange) {
      onViewChange(view);
    }
  };

  const menuItems = [
    {
      id: 'create',
      label: 'Create Index',
      icon: PiPlus,
      description: 'Upload CSV or Excel files to create new Fiass indexes'
    },
    {
      id: 'show',
      label: 'Show Index',
      icon: PiDatabase,
      description: 'View and manage existing PINE collection entries'
    }
  ];

  return (
    <div className={`admin-sidebar w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 fixed left-0 top-0 p-4 h-full z-40 transition-transform duration-300 ${
      isOpen ? 'translate-x-0' : '-translate-x-full'
    } lg:translate-x-0`}>
      {/* Header */}
      <div className="sidebar-header mb-8">
        <div className="flex items-center gap-2 mb-2">
          <PiGear className="text-primaryColor text-xl" />
          <h2 className="text-xl font-semibold dark:text-white">
            Admin Panel
          </h2>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Manage your FiassDB and data
        </p>
      </div>

      {/* Navigation Menu */}
      <nav className="space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentView === item.id;

          return (
            <button
              key={item.id}
              onClick={() => handleNavigation(item.id)}
              className={`block w-full text-left px-4 py-3 rounded-md transition-colors group ${
                isActive
                  ? 'bg-primaryColor text-white shadow-sm'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
              }`}
            >
              <div className="flex items-center gap-3">
                <Icon
                  className={`text-lg ${
                    isActive ? 'text-white' : 'text-primaryColor'
                  }`}
                />
                <div>
                  <div className="font-medium">{item.label}</div>
                  <div className={`text-xs mt-1 ${
                    isActive
                      ? 'text-white/80'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {item.description}
                  </div>
                </div>
              </div>
            </button>
          );
        })}
      </nav>

      {/* Footer */}
      {/* <div className="absolute bottom-4 left-4 right-4">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <PiList className="text-primaryColor" />
            <span className="text-sm font-medium dark:text-white">Quick Info</span>
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400">
            Use this panel to manage your Pinecone vector databases and PINE collection data.
          </p>
        </div>
      </div> */}
    </div>
  );
};

export default AdminSidebar;