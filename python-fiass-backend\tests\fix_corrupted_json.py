#!/usr/bin/env python3
"""
Script to fix corrupted JSON metadata files in FAISS indexes
"""

import os
import json
import re
from typing import List, Dict, Any

def clean_json_content(content: str) -> str:
    """
    Clean corrupted JSON content by removing invalid control characters
    and fixing common JSON corruption issues.
    """
    # Remove invalid control characters (except valid ones like \n, \t, \r)
    # Control characters are in range 0x00-0x1F except \t(0x09), \n(0x0A), \r(0x0D)
    cleaned = ""
    for char in content:
        char_code = ord(char)
        if char_code < 32:  # Control character
            if char_code in [9, 10, 13]:  # Keep \t, \n, \r
                cleaned += char
            else:
                # Replace other control characters with space
                cleaned += " "
        else:
            cleaned += char
    
    return cleaned

def fix_json_structure(content: str) -> str:
    """
    Fix common JSON structure issues
    """
    # Remove any trailing commas before closing brackets/braces
    content = re.sub(r',(\s*[}\]])', r'\1', content)
    
    # Fix any double quotes issues
    content = re.sub(r'""', r'"', content)
    
    # Remove any null bytes
    content = content.replace('\x00', '')
    
    return content

def validate_and_fix_json_file(file_path: str) -> bool:
    """
    Validate and fix a JSON file if it's corrupted
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        bool: True if file was fixed or was already valid, False if unfixable
    """
    print(f"🔍 Checking JSON file: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    # Create backup
    backup_path = file_path + ".backup"
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(original_content)
        print(f"📋 Created backup: {backup_path}")
    except Exception as e:
        print(f"⚠️ Could not create backup: {e}")
    
    try:
        # Try to load the JSON file
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        print(f"✅ JSON file is valid: {file_path}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON corruption detected: {e}")
        print(f"🔧 Attempting to fix...")
        
        try:
            # Read the corrupted content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                corrupted_content = f.read()
            
            # Clean the content
            cleaned_content = clean_json_content(corrupted_content)
            cleaned_content = fix_json_structure(cleaned_content)
            
            # Try to parse the cleaned content
            try:
                parsed_data = json.loads(cleaned_content)
                
                # If successful, write the cleaned version back
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(parsed_data, f, ensure_ascii=False, indent=2)
                
                print(f"✅ Successfully fixed JSON file: {file_path}")
                return True
                
            except json.JSONDecodeError:
                # If still corrupted, try to salvage what we can
                print(f"🔧 Attempting advanced repair...")
                return attempt_advanced_repair(file_path, corrupted_content)
                
        except Exception as e:
            print(f"❌ Failed to fix JSON file: {e}")
            return False
    
    except Exception as e:
        print(f"❌ Error reading JSON file: {e}")
        return False

def attempt_advanced_repair(file_path: str, corrupted_content: str) -> bool:
    """
    Attempt advanced repair by reconstructing the JSON structure
    """
    try:
        # Try to find valid JSON objects in the content
        print("🔧 Attempting to reconstruct JSON array...")
        
        # Look for patterns that suggest this is a list of metadata objects
        if '[' in corrupted_content and ']' in corrupted_content:
            # Try to extract individual JSON objects
            objects = []
            
            # Split by common delimiters and try to parse each part
            parts = re.split(r'[,\n](?=\s*{)', corrupted_content)
            
            for part in parts:
                part = part.strip()
                if part.startswith('{') and part.endswith('}'):
                    try:
                        # Clean this individual object
                        clean_part = clean_json_content(part)
                        clean_part = fix_json_structure(clean_part)
                        obj = json.loads(clean_part)
                        objects.append(obj)
                    except:
                        continue
            
            if objects:
                # Write the reconstructed array
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(objects, f, ensure_ascii=False, indent=2)
                
                print(f"✅ Reconstructed JSON with {len(objects)} objects")
                return True
        
        # If all else fails, create a minimal valid structure
        print("🔧 Creating minimal valid JSON structure...")
        minimal_data = []
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(minimal_data, f, ensure_ascii=False, indent=2)
        
        print(f"⚠️ Created empty JSON array (data may be lost)")
        return True
        
    except Exception as e:
        print(f"❌ Advanced repair failed: {e}")
        return False

def fix_all_faiss_indexes(faiss_data_dir: str):
    """
    Fix all corrupted JSON files in FAISS indexes
    """
    print(f"🔍 Scanning FAISS data directory: {faiss_data_dir}")
    
    if not os.path.exists(faiss_data_dir):
        print(f"❌ FAISS data directory not found: {faiss_data_dir}")
        return
    
    fixed_count = 0
    total_count = 0
    
    # Scan all subdirectories
    for item in os.listdir(faiss_data_dir):
        item_path = os.path.join(faiss_data_dir, item)
        if os.path.isdir(item_path):
            print(f"\n📁 Checking index: {item}")
            
            # Check for JSON files in this directory
            json_files = []
            for file in os.listdir(item_path):
                if file.endswith('.json'):
                    json_files.append(os.path.join(item_path, file))
            
            if not json_files:
                print(f"⚠️ No JSON files found in {item}")
                continue
            
            # Fix each JSON file
            for json_file in json_files:
                total_count += 1
                if validate_and_fix_json_file(json_file):
                    fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Total JSON files checked: {total_count}")
    print(f"   Files fixed or validated: {fixed_count}")
    print(f"   Success rate: {(fixed_count/total_count*100):.1f}%" if total_count > 0 else "   No files found")

if __name__ == "__main__":
    # Get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    faiss_data_dir = os.path.join(script_dir, "faiss_data")
    
    print("🔧 FAISS JSON Corruption Fixer")
    print("=" * 50)
    
    fix_all_faiss_indexes(faiss_data_dir)
    
    print("\n✅ JSON fixing process completed!")
    print("🚀 You can now restart the Flask server.")