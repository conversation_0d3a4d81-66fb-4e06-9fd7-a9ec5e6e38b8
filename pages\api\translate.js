/**
 * Translation API endpoint using @vitalets/google-translate-api
 * This endpoint handles translation requests from the Python backend
 */

import googleTranslateService from '../../services/googleTranslateService';

export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        return res.status(405).json({ 
            success: false, 
            error: 'Method not allowed. Use POST.' 
        });
    }

    try {
        const { action, text, targetLanguage, sourceLang, responseData } = req.body;

        if (!action) {
            return res.status(400).json({
                success: false,
                error: 'Action is required. Use "detect", "translate", or "translateResponse"'
            });
        }

        switch (action) {
            case 'detect':
                if (!text) {
                    return res.status(400).json({
                        success: false,
                        error: 'Text is required for language detection'
                    });
                }

                const detection = await googleTranslateService.detectLanguage(text);
                return res.status(200).json({
                    success: true,
                    ...detection
                });

            case 'translate':
                if (!text || !targetLanguage) {
                    return res.status(400).json({
                        success: false,
                        error: 'Text and targetLanguage are required for translation'
                    });
                }

                const translation = await googleTranslateService.translateText(
                    text, 
                    targetLanguage, 
                    sourceLang
                );
                return res.status(200).json(translation);

            case 'translateResponse':
                if (!responseData || !targetLanguage) {
                    return res.status(400).json({
                        success: false,
                        error: 'responseData and targetLanguage are required for response translation'
                    });
                }

                const translatedResponse = await googleTranslateService.translateFinancialResponse(
                    responseData, 
                    targetLanguage
                );
                return res.status(200).json({
                    success: true,
                    data: translatedResponse
                });

            default:
                return res.status(400).json({
                    success: false,
                    error: 'Invalid action. Use "detect", "translate", or "translateResponse"'
                });
        }

    } catch (error) {
        console.error('Translation API error:', error);
        return res.status(500).json({
            success: false,
            error: error.message || 'Internal server error'
        });
    }
}