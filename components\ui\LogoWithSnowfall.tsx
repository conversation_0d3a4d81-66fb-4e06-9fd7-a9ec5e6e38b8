import React, { useEffect, useRef } from 'react';
import Image from 'next/image';
import fav from '@/public/images/favicon.png';

interface SnowflakeProps {
  size: number;
  x: number;
  y: number;
  speedX: number;
  speedY: number;
  opacity: number;
}

const LogoWithSnowfall: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const snowflakesRef = useRef<SnowflakeProps[]>([]);
  const animationFrameRef = useRef<number>(0);

  // Initialize snowflakes
  useEffect(() => {
    // Only run on client side to prevent hydration errors
    if (typeof window === 'undefined') return;

    const initSnowflakes = () => {
      const snowflakes: SnowflakeProps[] = [];
      const count = 50; // Number of snowflakes

      for (let i = 0; i < count; i++) {
        snowflakes.push({
          size: Math.random() * 3 + 1, // Size between 1-4px
          x: Math.random() * window.innerWidth,
          y: Math.random() * window.innerHeight,
          speedX: Math.random() * 0.5 - 0.25, // Random horizontal drift
          speedY: Math.random() * 1 + 0.5, // Fall speed
          opacity: Math.random() * 0.8 + 0.2, // Opacity between 0.2-1
        });
      }

      snowflakesRef.current = snowflakes;
    };

    initSnowflakes();

    // Handle window resize
    const handleResize = () => {
      if (canvasRef.current) {
        canvasRef.current.width = window.innerWidth;
        canvasRef.current.height = window.innerHeight;
      }
      initSnowflakes();
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, []);

  // Animation loop
  useEffect(() => {
    const animate = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw and update snowflakes
      snowflakesRef.current.forEach((flake, index) => {
        // Draw snowflake
        ctx.beginPath();
        ctx.arc(flake.x, flake.y, flake.size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${flake.opacity})`;
        ctx.fill();

        // Update position
        flake.y += flake.speedY;
        flake.x += flake.speedX;

        // Reset if out of bounds
        if (flake.y > canvas.height) {
          flake.y = -10;
          flake.x = Math.random() * canvas.width;
        }

        if (flake.x < 0) flake.x = canvas.width;
        if (flake.x > canvas.width) flake.x = 0;

        // Update the snowflake in the array
        snowflakesRef.current[index] = flake;
      });

      // Continue animation loop
      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, []);

  return (
    <div className="relative w-full">
      {/* Snowfall Canvas */}
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 w-full h-full pointer-events-none z-10"
      />

      {/* Logo Container */}
      <div className="relative z-20 flex justify-start items-center gap-1.5 p-4 bg-[#1a2231] bg-opacity-90 rounded-md">
        <Image src={fav} alt="AIQuill Logo" width={32} height={32} />
        <span className="text-2xl font-semibold text-gray-200">
          QueryOne
        </span>
      </div>
    </div>
  );
};

export default LogoWithSnowfall;
