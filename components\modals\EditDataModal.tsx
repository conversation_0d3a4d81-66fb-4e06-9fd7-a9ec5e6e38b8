"use client";
import React, { useState, useEffect, useMemo } from "react";
import {
  PiX,
  PiMagnifyingGlass,
  PiTrash,
  PiCheckSquare,
  PiSquare,
  PiTable,
  PiWarning,
  PiSpinner,
  PiFileText,
  PiFileCsv,
  PiCaretLeft,
  PiCaretRight,
  PiFunnel,
  PiGlobe,
  PiTag
} from "react-icons/pi";
import {
  fetchIndexData,
  deleteIndexRows,
  exportToCSV,
  exportToJSON,
  filterData,
  getUniqueColumnValues,
  IndexDataRow
} from '../../services/dataManagementService';

interface IndexData {
  _id: string;
  email: string;
  index_name: string;
  embed_model?: string;
  api_key?: string;
  source: 'FAISS' | 'PINE';
  [key: string]: any;
}

interface EditDataModalProps {
  isOpen: boolean;
  onClose: () => void;
  indexData: IndexData | null;
}

const EditDataModal: React.FC<EditDataModalProps> = ({
  isOpen,
  onClose,
  indexData
}) => {
  const [data, setData] = useState<IndexDataRow[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Filter states
  const [urlFilter, setUrlFilter] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [showFilters, setShowFilters] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20); // Fixed page size, can be made configurable
  const [totalRows, setTotalRows] = useState(0);
  const [allData, setAllData] = useState<IndexDataRow[]>([]); // Store all data for search/export

  // Enhanced filtering function that includes URL and category filters
  const applyAllFilters = (dataToFilter: IndexDataRow[]) => {
    let filtered = dataToFilter;

    // Apply search term filter
    if (searchTerm.trim()) {
      filtered = filterData(filtered, searchTerm);
    }

    // Apply URL filter
    if (urlFilter.trim()) {
      filtered = filtered.filter(row => {
        const url = row.url || '';
        return String(url).toLowerCase().includes(urlFilter.toLowerCase());
      });
    }

    // Apply category filter
    if (categoryFilter.trim()) {
      filtered = filtered.filter(row => {
        const category = row.category || '';
        return String(category).toLowerCase().includes(categoryFilter.toLowerCase());
      });
    }

    return filtered;
  };

  // Filter data based on all filters (for current page)
  const filteredData = useMemo(() => {
    return applyAllFilters(data);
  }, [data, searchTerm, urlFilter, categoryFilter]);

  // For search functionality, we need to work with all data
  const allFilteredData = useMemo(() => {
    return applyAllFilters(allData);
  }, [allData, searchTerm, urlFilter, categoryFilter]);

  // Calculate pagination values (after filtered data is calculated)
  const totalPages = searchTerm
    ? Math.ceil(allFilteredData.length / pageSize)
    : Math.ceil(totalRows / pageSize);
  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  // Load data when modal opens or page changes
  useEffect(() => {
    if (isOpen && indexData) {
      loadData();
    }
  }, [isOpen, indexData, currentPage]);

  // Reset pagination when filters change
  useEffect(() => {
    if (searchTerm || urlFilter || categoryFilter) {
      setCurrentPage(1);
    }
  }, [searchTerm, urlFilter, categoryFilter]);

  // Clear state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setData([]);
      setAllData([]);
      setSearchTerm("");
      setUrlFilter("");
      setCategoryFilter("");
      setShowFilters(false);
      setSelectedRows(new Set());
      setError(null);
      setShowDeleteConfirm(false);
      setCurrentPage(1);
      setTotalRows(0);
    }
  }, [isOpen]);

  const loadData = async () => {
    if (!indexData) return;

    setLoading(true);
    setError(null);

    try {
      // Calculate offset for pagination
      const offset = (currentPage - 1) * pageSize;

      // If we have any filters, we need to load all data first for proper filtering
      if (searchTerm || urlFilter || categoryFilter) {
        // Load all data for filtering
        const allResult = await fetchIndexData(indexData.index_name, 10000, 0); // Large limit to get all data

        if (allResult.success) {
          setAllData(allResult.data);
          const filtered = applyAllFilters(allResult.data);

          // Get the current page of filtered data
          const paginatedData = filtered.slice(offset, offset + pageSize);
          setData(paginatedData);
          setTotalRows(filtered.length);
        } else {
          setError(allResult.error || 'Failed to load data');
        }
      } else {
        // Load paginated data directly
        const result = await fetchIndexData(indexData.index_name, pageSize, offset);

        if (result.success) {
          setData(result.data);
          setTotalRows(result.total);

          // Also load all data for export functionality (only on first page load)
          if (currentPage === 1) {
            const allResult = await fetchIndexData(indexData.index_name, 10000, 0);
            if (allResult.success) {
              setAllData(allResult.data);
            }
          }
        } else {
          setError(result.error || 'Failed to load data');
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Pagination navigation functions
  const handleNextPage = () => {
    if (hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (hasPrevPage) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const handleRowSelect = (rowId: string) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(rowId)) {
      newSelected.delete(rowId);
    } else {
      newSelected.add(rowId);
    }
    setSelectedRows(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRows.size === filteredData.length) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(filteredData.map(row => row.id)));
    }
  };

  const handleDeleteSelected = async () => {
    if (!indexData || selectedRows.size === 0) return;

    setIsDeleting(true);
    
    try {
      const result = await deleteIndexRows(indexData.index_name, Array.from(selectedRows));
      
      if (result.success) {
        // Remove deleted rows from local state
        setData(prevData => prevData.filter(row => !selectedRows.has(row.id)));
        setSelectedRows(new Set());
        setShowDeleteConfirm(false);
      } else {
        setError(result.error || 'Failed to delete rows');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleExportCSV = () => {
    const dataToExport = searchTerm ? allFilteredData : allData;
    const filename = `${indexData?.index_name || 'index'}_data_${new Date().toISOString().split('T')[0]}.csv`;
    exportToCSV(dataToExport, filename);
  };

  const handleExportJSON = () => {
    const dataToExport = searchTerm ? allFilteredData : allData;
    const filename = `${indexData?.index_name || 'index'}_data_${new Date().toISOString().split('T')[0]}.json`;
    exportToJSON(dataToExport, filename);
  };

  // Get unique values for filters
  const uniqueUrls = useMemo(() => {
    return getUniqueColumnValues(allData, 'url').slice(0, 20); // Limit to 20 for performance
  }, [allData]);

  const uniqueCategories = useMemo(() => {
    return getUniqueColumnValues(allData, 'category').slice(0, 20); // Limit to 20 for performance
  }, [allData]);

  // Get column headers from the first row of data (prefer allData if available)
  // This useMemo must be called before any early returns to maintain hook order
  const columns = useMemo(() => {
    if (data.length > 0) return Object.keys(data[0]);
    if (allData.length > 0) return Object.keys(allData[0]);
    return [];
  }, [data, allData]);

  if (!isOpen || !indexData) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-n800 rounded-lg w-full max-w-7xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-n600">
          <div className="flex items-center">
            <PiTable className="text-blue-500 text-2xl mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-n700 dark:text-n20">
                Edit Data: {indexData.index_name}
              </h3>
              <p className="text-sm text-n500 dark:text-n40">
                {indexData.email}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-n700 rounded-md transition-colors"
          >
            <PiX className="text-xl text-n500 dark:text-n40" />
          </button>
        </div>

        {/* Controls */}
        <div className="p-6 border-b border-gray-200 dark:border-n600">
          <div className="flex flex-col gap-4">
            {/* Search and Filter Toggle */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <PiMagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500" />
                <input
                  type="text"
                  placeholder="Search across all fields..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200"
                />
              </div>

              {/* Filter Toggle Button */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-2 rounded-lg border transition-all duration-200 flex items-center gap-2 ${
                  showFilters || urlFilter || categoryFilter
                    ? 'bg-primaryColor text-white border-primaryColor shadow-md'
                    : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-n700'
                }`}
              >
                <PiFunnel className="text-sm" />
                Filters
                {(urlFilter || categoryFilter) && (
                  <span className="bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full">
                    {[urlFilter, categoryFilter].filter(Boolean).length}
                  </span>
                )}
              </button>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="bg-gray-50 dark:bg-n700 rounded-lg p-4 border border-gray-200 dark:border-n600">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* URL Filter */}
                  <div className="relative">
                    <label className="block text-sm font-medium text-n700 dark:text-white mb-2">
                      <PiGlobe className="inline mr-2 text-primaryColor" />
                      Filter by URL
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Enter URL to filter..."
                        value={urlFilter}
                        onChange={(e) => setUrlFilter(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200"
                      />
                      {urlFilter && (
                        <button
                          onClick={() => setUrlFilter("")}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 hover:text-n600 dark:hover:text-n300 transition-colors"
                        >
                          <PiX />
                        </button>
                      )}
                    </div>
                    {uniqueUrls.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-n500 dark:text-n400 mb-1">Common URLs:</p>
                        <div className="flex flex-wrap gap-1">
                          {uniqueUrls.slice(0, 5).map((url, index) => (
                            <button
                              key={index}
                              onClick={() => setUrlFilter(url)}
                              className="text-xs px-2 py-1 bg-white dark:bg-n800 border border-gray-300 dark:border-n600 rounded text-n700 dark:text-n20 hover:bg-primaryColor hover:text-white hover:border-primaryColor transition-all duration-200"
                              title={url}
                            >
                              {url.length > 20 ? `${url.substring(0, 20)}...` : url}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Category Filter */}
                  <div className="relative">
                    <label className="block text-sm font-medium text-n700 dark:text-n20 mb-2">
                      <PiTag className="inline mr-2 text-primaryColor" />
                      Filter by Category
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Enter category to filter..."
                        value={categoryFilter}
                        onChange={(e) => setCategoryFilter(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200"
                      />
                      {categoryFilter && (
                        <button
                          onClick={() => setCategoryFilter("")}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 hover:text-n600 dark:hover:text-n300 transition-colors"
                        >
                          <PiX />
                        </button>
                      )}
                    </div>
                    {uniqueCategories.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-n500 dark:text-n400 mb-1">Available Categories:</p>
                        <div className="flex flex-wrap gap-1">
                          {uniqueCategories.map((category, index) => (
                            <button
                              key={index}
                              onClick={() => setCategoryFilter(category)}
                              className="text-xs px-2 py-1 bg-white dark:bg-n800 border border-gray-300 dark:border-n600 rounded text-n700 dark:text-n20 hover:bg-primaryColor hover:text-white hover:border-primaryColor transition-all duration-200"
                            >
                              {category}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Clear All Filters */}
                {(urlFilter || categoryFilter) && (
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-n600">
                    <button
                      onClick={() => {
                        setUrlFilter("");
                        setCategoryFilter("");
                      }}
                      className="text-sm text-primaryColor hover:bg-gray-50 dark:hover:bg-gray-800/10 px-2 py-1 rounded-md transition-colors flex items-center gap-1"
                    >
                      <PiX className="text-xs" />
                      Clear all filters
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 flex-wrap mt-4">
            {selectedRows.size > 0 && (
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="px-4 py-2 bg-errorColor text-white rounded-lg hover:bg-errorColor/90 transition-all duration-200 flex items-center shadow-sm hover:shadow-md"
              >
                <PiTrash className="mr-2" />
                Delete ({selectedRows.size})
              </button>
            )}

            <button
              onClick={handleExportCSV}
              disabled={totalRows === 0}
              className="px-4 py-2 bg-successColor text-white rounded-lg hover:bg-successColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-sm hover:shadow-md"
            >
              <PiFileCsv className="mr-2" />
              Export CSV
            </button>

            <button
              onClick={handleExportJSON}
              disabled={totalRows === 0}
              className="px-4 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-sm hover:shadow-md"
            >
              <PiFileText className="mr-2" />
              Export JSON
            </button>
          </div>

          {/* Results info */}
          <div className="mt-4 flex flex-col sm:flex-row gap-2 sm:gap-0 justify-between items-start sm:items-center">
            <div className="text-sm text-n500 dark:text-n40">
              {(searchTerm || urlFilter || categoryFilter) ? (
                <div className="space-y-1">
                  <div>Showing {filteredData.length} of {allFilteredData.length} filtered results (Page {currentPage})</div>
                  <div className="flex flex-wrap gap-2 text-xs">
                    {searchTerm && (
                      <span className="bg-primaryColor/10 text-primaryColor px-2 py-1 rounded-full">
                        Search: "{searchTerm}"
                      </span>
                    )}
                    {urlFilter && (
                      <span className="bg-infoColor/10 text-infoColor px-2 py-1 rounded-full">
                        URL: "{urlFilter}"
                      </span>
                    )}
                    {categoryFilter && (
                      <span className="bg-warningColor/10 text-warningColor px-2 py-1 rounded-full">
                        Category: "{categoryFilter}"
                      </span>
                    )}
                  </div>
                </div>
              ) : (
                <>
                  Showing {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, totalRows)} of {totalRows} rows
                </>
              )}
            </div>

            {/* Pagination Controls */}
            {(((searchTerm || urlFilter || categoryFilter) && Math.ceil(allFilteredData.length / pageSize) > 1) || (!(searchTerm || urlFilter || categoryFilter) && totalPages > 1)) && (
              <div className="flex items-center gap-2">
                <button
                  onClick={handlePrevPage}
                  disabled={!hasPrevPage || loading}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors flex items-center gap-1 ${
                    !hasPrevPage || loading
                      ? 'bg-gray-100 dark:bg-n700 text-n400 dark:text-n500 border-gray-200 dark:border-n600 cursor-not-allowed'
                      : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-gray-800/10'
                  }`}
                >
                  <PiCaretLeft />
                  Previous
                </button>

                <span className="text-sm text-n500 dark:text-n40 px-2">
                  Page {currentPage} of {(searchTerm || urlFilter || categoryFilter) ? Math.ceil(allFilteredData.length / pageSize) : totalPages}
                </span>

                <button
                  onClick={handleNextPage}
                  disabled={!hasNextPage || loading}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors flex items-center gap-1 ${
                    !hasNextPage || loading
                      ? 'bg-gray-100 dark:bg-n700 text-n400 dark:text-n500 border-gray-200 dark:border-n600 cursor-not-allowed'
                      : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-gray-800/10'
                  }`}
                >
                  Next
                  <PiCaretRight />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <PiSpinner className="animate-spin text-4xl text-primaryColor mx-auto mb-4" />
                <p className="text-n600 dark:text-n30 font-medium">Loading data...</p>
                <div className="mt-2 w-32 h-1 bg-gray-200 dark:bg-n600 rounded-full mx-auto overflow-hidden">
                  <div className="h-full bg-primaryColor rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center max-w-md">
                <PiWarning className="text-4xl text-errorColor mx-auto mb-4" />
                <p className="text-errorColor dark:text-errorColor mb-4 font-medium">{error}</p>
                <button
                  onClick={loadData}
                  className="px-6 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : totalRows === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <PiTable className="text-4xl text-n300 dark:text-n600 mx-auto mb-4" />
                <p className="text-n600 dark:text-n30 font-medium">
                  {(searchTerm || urlFilter || categoryFilter)
                    ? 'No data matches your filters'
                    : 'No data found for this index'
                  }
                </p>
                {(searchTerm || urlFilter || categoryFilter) && (
                  <button
                    onClick={() => {
                      setSearchTerm("");
                      setUrlFilter("");
                      setCategoryFilter("");
                    }}
                    className="mt-3 text-sm text-primaryColor hover:bg-gray-50 dark:hover:bg-gray-800/10 px-3 py-2 rounded-md transition-colors flex items-center gap-1"
                  >
                    <PiX className="text-xs" />
                    Clear all filters
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="overflow-auto h-full">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-n700 sticky top-0 z-10 shadow-sm">
                  <tr>
                    <th className="px-4 py-3 text-left border-b border-gray-200 dark:border-n600">
                      <button
                        onClick={handleSelectAll}
                        className="p-1 hover:bg-gray-200 dark:hover:bg-n600 rounded transition-colors duration-200"
                      >
                        {selectedRows.size === filteredData.length && filteredData.length > 0 ? (
                          <PiCheckSquare className="text-primaryColor" />
                        ) : (
                          <PiSquare className="text-n400 dark:text-n500 hover:text-primaryColor transition-colors duration-200" />
                        )}
                      </button>
                    </th>
                    {columns.map((column) => (
                      <th
                        key={column}
                        className="px-4 py-3 text-left text-xs font-semibold text-n600 dark:text-n30 uppercase tracking-wider border-b border-gray-200 dark:border-n600"
                      >
                        {column.replace(/_/g, ' ')}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-n800 divide-y divide-gray-200 dark:divide-n600">
                  {filteredData.map((row, index) => (
                    <tr
                      key={row.id}
                      className={`${
                        selectedRows.has(row.id)
                          ? 'bg-primaryColor/5 dark:bg-primaryColor/10 border-l-2 border-primaryColor'
                          : index % 2 === 0
                            ? 'bg-white dark:bg-n800'
                            : 'bg-gray-25 dark:bg-n800/50'
                      }`}
                    >
                      <td className="px-4 py-3">
                        <button
                          onClick={() => handleRowSelect(row.id)}
                          className="p-1 hover:bg-gray-200 dark:hover:bg-n600 rounded transition-all duration-200 hover:scale-110"
                        >
                          {selectedRows.has(row.id) ? (
                            <PiCheckSquare className="text-primaryColor" />
                          ) : (
                            <PiSquare className="text-n400 dark:text-n500 hover:text-primaryColor transition-colors duration-200" />
                          )}
                        </button>
                      </td>
                      {columns.map((column) => (
                        <td
                          key={column}
                          className="px-4 py-3 text-sm text-n700 dark:text-n20 max-w-xs truncate"
                          title={String(row[column] || '')}
                        >
                          <span className={`${
                            column === 'url' ? 'text-infoColor hover:underline cursor-pointer' :
                            column === 'category' ? 'text-warningColor font-medium' :
                            column === 'title' ? 'font-medium text-n700 dark:text-n20' :
                            'text-n700 dark:text-n20'
                          }`}>
                            {String(row[column] || '')}
                          </span>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm">
            <div className="bg-white dark:bg-n800 rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl border border-gray-200 dark:border-n600">
              <div className="flex items-center mb-4">
                <div className="p-2 bg-errorColor/10 rounded-full mr-3">
                  <PiWarning className="text-errorColor text-2xl" />
                </div>
                <h4 className="text-lg font-semibold text-n700 dark:text-n20">
                  Confirm Deletion
                </h4>
              </div>
              <p className="text-n600 dark:text-n30 mb-6 leading-relaxed">
                Are you sure you want to delete <span className="font-semibold text-errorColor">{selectedRows.size}</span> selected row{selectedRows.size !== 1 ? 's' : ''}?
                <br />
                <span className="text-sm text-n500 dark:text-n400">This action cannot be undone.</span>
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isDeleting}
                  className="px-6 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n700 rounded-lg hover:bg-gray-300 dark:hover:bg-n600 disabled:opacity-50 transition-all duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteSelected}
                  disabled={isDeleting}
                  className="px-6 py-2 text-sm font-medium text-white bg-errorColor hover:bg-errorColor/90 rounded-lg disabled:opacity-50 flex items-center transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  {isDeleting ? (
                    <>
                      <PiSpinner className="animate-spin mr-2" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <PiTrash className="mr-2" />
                      Delete
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EditDataModal;
