#!/usr/bin/env python3
"""
Test script to verify Oriya related questions functionality
"""

import requests
import json
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

def test_oriya_related_questions():
    """Test Oriya related questions generation"""
    
    # Server URL
    base_url = "http://localhost:5010"
    
    # Test cases with Oriya queries
    test_cases = [
        {
            "name": "Basic Oriya Query",
            "query": "ଅପଣମାନଙ୍କ ମଞ୍ଚର୍‌ ଅବନକେ ଗଣା",
            "index_name": "multi",
            "language": "Oriya",
            "enable_translation": False
        },
        {
            "name": "Financial Query in Oriya",
            "query": "ଅର୍ଥନୀତି ଏବଂ ବ୍ୟାଙ୍କିଂ ବିଷୟରେ ଜଣାନ୍ତୁ",
            "index_name": "multi",
            "language": "Oriya",
            "enable_translation": False
        },
        {
            "name": "Auto-Detection Test",
            "query": "ଅପଣସାବିଳ ସଂଧାର୍ଣଙକ ଲକଷଦର୍‌ ସଲାମ ମାଇ୍ବା ପାଇ ଉଳ଼କୁଭ ବହିବ ନାଢୁ",
            "index_name": "multi"
        },
        {
            "name": "Translation Enabled (Oriya to Oriya)",
            "query": "ଅର୍ଥନୀତି ଏବଂ ବିନିଯୋଗ ବିଷୟରେ ଜଣାନ୍ତୁ",
            "index_name": "multi",
            "language": "Oriya",
            "enable_translation": True,
            "target_language": "Oriya"
        },
        {
            "name": "Cross-Language Translation (English to Oriya)",
            "query": "Tell me about economics and banking",
            "index_name": "multi",
            "language": "English",
            "enable_translation": True,
            "target_language": "Oriya"
        },
        {
            "name": "User Selected Oriya (English Query)",
            "query": "What is the current economic situation?",
            "index_name": "multi",
            "language": "English",
            "target_language": "Oriya",
            "enable_translation": True
        }
    ]
    
    print("🧪 Testing Oriya Related Questions Functionality")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['name']}")
        print("-" * 40)
        
        # Prepare request data
        request_data = {
            "query": test_case["query"],
            "index_name": test_case.get("index_name", "multi"),
            "client_email": "<EMAIL>"
        }
        
        # Add optional parameters
        if "language" in test_case:
            request_data["language"] = test_case["language"]
        if "enable_translation" in test_case:
            request_data["enable_translation"] = test_case["enable_translation"]
        if "target_language" in test_case:
            request_data["target_language"] = test_case["target_language"]
        
        print(f"📤 Request: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
        
        try:
            # Make request to financial_query endpoint
            response = requests.post(
                f"{base_url}/financial_query",
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Status: Success")
                
                # Check language detection
                if "detected_language" in data:
                    print(f"🔍 Detected Language: {data['detected_language']}")
                if "language_confidence" in data:
                    print(f"🎯 Language Confidence: {data['language_confidence']:.3f}")
                
                # Check related questions
                if "related_questions" in data and data["related_questions"]:
                    print(f"❓ Related Questions ({len(data['related_questions'])}):")
                    for j, question in enumerate(data["related_questions"], 1):
                        print(f"   {j}. {question}")
                        
                    # Check if questions are in Oriya
                    oriya_questions = []
                    for question in data["related_questions"]:
                        if any('\u0B00' <= char <= '\u0B7F' for char in question):
                            oriya_questions.append(question)
                    
                    if oriya_questions:
                        print(f"✅ Found {len(oriya_questions)} questions in Oriya script")
                    else:
                        print(f"⚠️ No questions found in Oriya script")
                else:
                    print("❌ No related questions found in response")
                
                # Check AI response language
                if "ai_response" in data:
                    ai_response = data["ai_response"]
                    if any('\u0B00' <= char <= '\u0B7F' for char in ai_response):
                        print("✅ AI response contains Oriya text")
                    else:
                        print("⚠️ AI response does not contain Oriya text")
                
            else:
                print(f"❌ Status: Error {response.status_code}")
                print(f"📄 Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {str(e)}")
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed")

if __name__ == "__main__":
    test_oriya_related_questions()