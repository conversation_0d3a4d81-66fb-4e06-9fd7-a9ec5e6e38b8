#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the missing database record for the robin index.
This will add the robin index to the pine_collection table so it shows up in the frontend.
"""

import os
import sys
import json

# Add the python-fiass-backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

import database

def fix_robin_index():
    """Add the missing robin index to the database."""
    try:
        # Check if robin index files exist
        faiss_data_dir = os.path.join('python-fiass-backend', 'faiss_data', 'robin')
        faiss_file = os.path.join(faiss_data_dir, 'robin.faiss')
        json_file = os.path.join(faiss_data_dir, 'robin.json')
        
        if not os.path.exists(faiss_file) or not os.path.exists(json_file):
            print(f"❌ Robin index files not found:")
            print(f"   FAISS file: {faiss_file} - {'EXISTS' if os.path.exists(faiss_file) else 'MISSING'}")
            print(f"   JSON file: {json_file} - {'EXISTS' if os.path.exists(json_file) else 'MISSING'}")
            return False
        
        print(f"✅ Robin index files found:")
        print(f"   FAISS file: {faiss_file}")
        print(f"   JSON file: {json_file}")
        
        # Read metadata to get information about the index
        with open(json_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        print(f"📊 Robin index contains {len(metadata)} entries")
        
        # Extract client email from metadata (assuming it's consistent)
        client_emails = set()
        for item in metadata:
            if 'client_email' in item:
                client_emails.add(item['client_email'])
        
        if not client_emails:
            print("⚠️ No client_email found in metadata, using default")
            client_email = "<EMAIL>"
        else:
            client_email = list(client_emails)[0]  # Use the first client email found
            print(f"📧 Found client email: {client_email}")
        
        # Get database connection
        conn = database.get_connection()
        cursor = conn.cursor()
        
        # Check if robin index already exists in pine_collection
        cursor.execute("SELECT * FROM pine_collection WHERE index_name = ?", ("robin",))
        existing_record = cursor.fetchone()
        
        if existing_record:
            print(f"ℹ️ Robin index already exists in pine_collection: {existing_record}")
        else:
            # Add robin index to pine_collection
            cursor.execute('''
            INSERT INTO pine_collection (index_name, email)
            VALUES (?, ?)
            ''', ("robin", client_email))
            
            print(f"✅ Added robin index to pine_collection with email: {client_email}")
        
        # Check if we need to add to excel_files table as well
        cursor.execute("SELECT * FROM excel_files WHERE index_name = ?", ("robin",))
        existing_excel_record = cursor.fetchone()
        
        if not existing_excel_record:
            # Add to excel_files table
            cursor.execute('''
            INSERT INTO excel_files 
            (file_name, index_name, client_id, columns_json, row_count, 
             embedding_model, embedding_dimension, detected_language, chunks_created)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                "pdf_index_new - Edited.csv",  # Original filename
                "robin",  # index_name
                client_email,  # client_id
                "[]",  # columns_json (empty for now)
                len(metadata),  # row_count
                "all-MiniLM-L6-v2",  # embedding_model (default)
                384,  # embedding_dimension (default for MiniLM)
                "English",  # detected_language (default)
                len(metadata)  # chunks_created
            ))
            
            print(f"✅ Added robin index to excel_files table")
        else:
            print(f"ℹ️ Robin index already exists in excel_files: {existing_excel_record}")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("\n🎉 Successfully fixed robin index database records!")
        print("The robin index should now appear in the frontend FAISS index list.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing robin index: {e}")
        return False

def verify_fix():
    """Verify that the fix worked by listing all indexes."""
    try:
        conn = database.get_connection()
        cursor = conn.cursor()
        
        print("\n📋 Current pine_collection entries:")
        cursor.execute("SELECT * FROM pine_collection ORDER BY upload_date DESC")
        pine_records = cursor.fetchall()
        for record in pine_records:
            print(f"   {record}")
        
        print("\n📋 Current excel_files entries:")
        cursor.execute("SELECT id, file_name, index_name, client_id FROM excel_files ORDER BY upload_date DESC")
        excel_records = cursor.fetchall()
        for record in excel_records:
            print(f"   {record}")
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying fix: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing missing robin index database records...")
    
    if fix_robin_index():
        verify_fix()
        print("\n✅ Fix completed successfully!")
        print("Please refresh your frontend to see the robin index in the list.")
    else:
        print("\n❌ Fix failed. Please check the error messages above.")