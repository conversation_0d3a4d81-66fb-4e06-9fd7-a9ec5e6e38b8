import React, { useState } from 'react';

interface SentenceWithHoverProps {
  sentence: string;
  url?: string;
  sentenceAnalysis?: Array<{
    sentence: string;
    url: string;
    summary?: string;
  }>;
}

const SentenceWithHover: React.FC<SentenceWithHoverProps> = ({ 
  sentence, 
  url, 
  sentenceAnalysis 
}) => {
  const [isHovering, setIsHovering] = useState(false);
  
  // Find matching sentence in sentenceAnalysis
  const findMatchingSentence = () => {
    if (!sentenceAnalysis || !Array.isArray(sentenceAnalysis)) return null;
    
    return sentenceAnalysis.find(item => 
      item.sentence.trim() === sentence.trim() || 
      sentence.includes(item.sentence) || 
      item.sentence.includes(sentence)
    );
  };
  
  const matchedSentence = findMatchingSentence();
  const hasUrl = matchedSentence?.url || url;
  
  if (!hasUrl) {
    // If no URL is associated, just return the sentence as plain text
    return <span>{sentence}</span>;
  }
  
  return (
    <span 
      className={`relative ${hasUrl ? 'cursor-pointer hover:bg-primaryColor/10 hover:underline' : ''}`}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {sentence}
      
      {isHovering && hasUrl && (
        <div className="absolute bottom-full left-0 mb-2 p-2 bg-white shadow-lg rounded-md border border-primaryColor/20 z-10 w-80">
          <p className="text-xs text-n600 mb-1">Source:</p>
          <a 
            href={matchedSentence?.url || url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs text-primaryColor hover:underline break-all"
          >
            {matchedSentence?.url || url}
          </a>
          {matchedSentence?.summary && (
            <div className="mt-1 pt-1 border-t border-primaryColor/10">
              <p className="text-xs text-n600">{matchedSentence.summary}</p>
            </div>
          )}
        </div>
      )}
    </span>
  );
};

export default SentenceWithHover;
