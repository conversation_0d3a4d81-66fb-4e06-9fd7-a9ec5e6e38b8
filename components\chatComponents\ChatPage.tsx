// components\ChatPage.tsx
import React, { useEffect, useRef } from "react";
import ChatB<PERSON>, { ChatBoxRef } from './ChatBox';
import ChatMessages  from './ChatMessages';
import { usePathname } from "next/navigation";
import { useChatHandler } from "@/stores/chatList";

function ChatPage() {
  const path = usePathname();
  const chatId = path ? path.split("/chat/")[1] : null;
  const { updateChatList } = useChatHandler();
  const chatBoxRef = useRef<ChatBoxRef>(null);

  useEffect(() => {
    // Load chat data when component mounts
    updateChatList();
  }, [updateChatList]);  // Added dependency

  // Handle selecting a related question from ChatMessages
  const handleSelectQuestion: (question: string) => void = (question: string) => {
    console.log("🎯 ChatPage: Selected related question from ChatMessages:", question);
    
    // Call the ChatBox method to set the input text
    if (chatBoxRef.current) {
      console.log("✅ ChatPage: Calling setInputFromQuestion");
      chatBoxRef.current.setInputFromQuestion(question);
    } else {
      console.warn("❌ ChatPage: chatBoxRef.current is null!");
      // Fallback: Try to find and update the input element directly
      const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;
      if (inputElement) {
        inputElement.value = question;
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
        inputElement.focus();
      }
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <div className="flex-1 overflow-y-auto pb-24">
        <div className="max-w-[1200px] mx-auto px-4 sm:px-6">
          {chatId && <ChatMessages chatId={chatId} onSelectQuestion={handleSelectQuestion} />}
        </div>
      </div>
      <div className="fixed bottom-0 left-0 right-0 bg-white py-4">
        <ChatBox ref={chatBoxRef} />
      </div>
    </div>
  );
}

export default ChatPage;
