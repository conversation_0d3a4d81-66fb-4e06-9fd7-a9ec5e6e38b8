'use client';

import React, { useState, useEffect } from 'react';
import { PiTrash, PiInfo, PiToggleLeft, PiToggleRight, PiDatabase, PiClock } from 'react-icons/pi';
import { CacheUtils } from '../services/uploadService';

interface CacheStats {
  upload: {
    size: number;
    oldestItem: number;
    newestItem: number;
  };
  query: {
    size: number;
    oldestItem: number;
    newestItem: number;
  };
}

interface CacheHitRate {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: string;
}

const CacheManager: React.FC = () => {
  const [cacheStats, setCacheStats] = useState<CacheStats>({
    upload: { size: 0, oldestItem: 0, newestItem: 0 },
    query: { size: 0, oldestItem: 0, newestItem: 0 }
  });
  const [cacheHitRate, setCacheHitRate] = useState<CacheHitRate>({
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: '0.00%'
  });
  const [cacheEnabled, setCacheEnabled] = useState(true);
  const [showDetails, setShowDetails] = useState(false);

  // Update cache stats
  const updateStats = () => {
    const stats = CacheUtils.getCacheStats();
    const hitRate = CacheUtils.getCacheHitRate();
    setCacheStats(stats);
    setCacheHitRate(hitRate);
    setCacheEnabled(CacheUtils.isCacheEnabled());
  };

  useEffect(() => {
    updateStats();
    // Update stats every 5 seconds
    const interval = setInterval(updateStats, 5000);
    return () => clearInterval(interval);
  }, []);

  const formatDate = (timestamp: number) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleString();
  };

  const formatTimeAgo = (timestamp: number) => {
    if (!timestamp) return 'N/A';
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const handleClearUploadCache = () => {
    CacheUtils.clearUploadCache();
    updateStats();
  };

  const handleClearQueryCache = () => {
    CacheUtils.clearQueryCache();
    updateStats();
  };

  const handleClearAllCache = () => {
    CacheUtils.clearAllCache();
    updateStats();
  };

  const handleToggleCache = () => {
    const newState = !cacheEnabled;
    CacheUtils.toggleCache(newState);
    setCacheEnabled(newState);
  };

  const handleResetHitStats = () => {
    CacheUtils.resetCacheHitStats();
    updateStats();
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <PiDatabase className="w-5 h-5 text-blue-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Cache Manager
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            title="Toggle details"
          >
            <PiInfo className="w-4 h-4" />
          </button>
          <button
            onClick={handleToggleCache}
            className={`p-2 rounded-lg transition-colors ${
              cacheEnabled 
                ? 'text-green-600 hover:text-green-700 hover:bg-green-50 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20'
                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700'
            }`}
            title={`Cache is ${cacheEnabled ? 'enabled' : 'disabled'}`}
          >
            {cacheEnabled ? <PiToggleRight className="w-5 h-5" /> : <PiToggleLeft className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* Cache Status */}
      <div className="mb-4">
        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
          cacheEnabled 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
        }`}>
          <div className={`w-2 h-2 rounded-full ${cacheEnabled ? 'bg-green-500' : 'bg-gray-400'}`} />
          Cache {cacheEnabled ? 'Enabled' : 'Disabled'}
        </div>
      </div>

      {/* Cache Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Upload Cache</h4>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {cacheStats.upload.size} items
            </span>
          </div>
          {showDetails && cacheStats.upload.size > 0 && (
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div className="flex items-center gap-1">
                <PiClock className="w-3 h-3" />
                <span>Oldest: {formatTimeAgo(cacheStats.upload.oldestItem)}</span>
              </div>
              <div className="flex items-center gap-1">
                <PiClock className="w-3 h-3" />
                <span>Newest: {formatTimeAgo(cacheStats.upload.newestItem)}</span>
              </div>
            </div>
          )}
          <button
            onClick={handleClearUploadCache}
            disabled={cacheStats.upload.size === 0}
            className="mt-2 w-full px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30"
          >
            Clear Upload Cache
          </button>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Query Cache</h4>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {cacheStats.query.size} items
            </span>
          </div>
          {showDetails && cacheStats.query.size > 0 && (
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div className="flex items-center gap-1">
                <PiClock className="w-3 h-3" />
                <span>Oldest: {formatTimeAgo(cacheStats.query.oldestItem)}</span>
              </div>
              <div className="flex items-center gap-1">
                <PiClock className="w-3 h-3" />
                <span>Newest: {formatTimeAgo(cacheStats.query.newestItem)}</span>
              </div>
            </div>
          )}
          <button
            onClick={handleClearQueryCache}
            disabled={cacheStats.query.size === 0}
            className="mt-2 w-full px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30"
          >
            Clear Query Cache
          </button>
        </div>
      </div>

      {/* Cache Hit Rate Statistics */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Cache Performance</h4>
          <button
            onClick={handleResetHitStats}
            className="px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
          >
            Reset Stats
          </button>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {cacheHitRate.totalRequests}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Total Requests</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600 dark:text-green-400">
              {cacheHitRate.cacheHits}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Cache Hits</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-red-600 dark:text-red-400">
              {cacheHitRate.cacheMisses}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Cache Misses</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
              {cacheHitRate.hitRate}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Hit Rate</div>
          </div>
        </div>
      </div>

      {/* Clear All Button */}
      <div className="flex justify-center">
        <button
          onClick={handleClearAllCache}
          disabled={cacheStats.upload.size === 0 && cacheStats.query.size === 0}
          className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <PiTrash className="w-4 h-4" />
          Clear All Cache
        </button>
      </div>

      {/* Cache Information */}
      {showDetails && (
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
            Cache Information
          </h4>
          <div className="text-xs text-blue-700 dark:text-blue-400 space-y-1">
            <p>• Upload cache stores successful file processing results for 24 hours</p>
            <p>• Query cache stores search results for 1 hour</p>
            <p>• Cache automatically expires old items and limits size to 100 items per type</p>
            <p>• Cached responses are significantly faster than fresh API calls</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CacheManager;
