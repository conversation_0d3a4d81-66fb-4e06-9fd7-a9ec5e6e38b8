import React from 'react';
import { PiFile, PiLink, PiDatabase, PiUpload } from 'react-icons/pi';

interface UploadedContentIndicatorProps {
  hasUploadedContent?: boolean;
  selectedIndex?: string;
  uploadSources?: string[];
  selectedLanguage: string;
}

const UploadedContentIndicator: React.FC<UploadedContentIndicatorProps> = ({
  hasUploadedContent = false,
  selectedIndex,
  uploadSources = [],
  selectedLanguage
}) => {
  const getLanguageText = () => {
    switch (selectedLanguage) {
      case 'tamil':
        return {
          basedOn: 'அடிப்படையில்',
          uploadedContent: 'பதிவேற்றப்பட்ட உள்ளடக்கம்',
          selectedIndex: 'தேர்ந்தெடுக்கப்பட்ட குறியீடு',
          sources: 'ஆதாரங்கள்'
        };
      case 'telugu':
        return {
          basedOn: 'ఆధారంగా',
          uploadedContent: 'అప్‌లోడ్ చేసిన కంటెంట్',
          selectedIndex: 'ఎంచుకున్న ఇండెక్స్',
          sources: 'మూలాలు'
        };
      case 'kannada':
        return {
          basedOn: 'ಆಧಾರದ ಮೇಲೆ',
          uploadedContent: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ವಿಷಯ',
          selectedIndex: 'ಆಯ್ಕೆಮಾಡಿದ ಸೂಚ್ಯಂಕ',
          sources: 'ಮೂಲಗಳು'
        };
      default:
        return {
          basedOn: 'Based on',
          uploadedContent: 'Uploaded Content',
          selectedIndex: 'Selected Index',
          sources: 'Sources'
        };
    }
  };

  const text = getLanguageText();

  if (!hasUploadedContent && !selectedIndex) {
    return null;
  }

  return (
    <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
      <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
        <PiDatabase className="w-4 h-4" />
        <span className="font-medium">{text.basedOn}:</span>
      </div>
      
      <div className="mt-2 space-y-1">
        {hasUploadedContent && (
          <div className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400">
            <PiUpload className="w-3 h-3" />
            <span>{text.uploadedContent}</span>
            {uploadSources.length > 0 && (
              <span className="text-blue-500 dark:text-blue-400">
                ({uploadSources.length} {uploadSources.length === 1 ? 'source' : 'sources'})
              </span>
            )}
          </div>
        )}
        
        {selectedIndex && (
          <div className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400">
            <PiDatabase className="w-3 h-3" />
            <span>{text.selectedIndex}: {selectedIndex}</span>
          </div>
        )}
      </div>
      
      {uploadSources.length > 0 && (
        <div className="mt-2 pt-2 border-t border-blue-200 dark:border-blue-700">
          <div className="text-xs text-blue-600 dark:text-blue-400 font-medium mb-1">
            {text.sources}:
          </div>
          <div className="space-y-1">
            {uploadSources.map((source, index) => (
              <div key={index} className="flex items-center gap-2 text-xs text-blue-500 dark:text-blue-400">
                {source.startsWith('http') ? (
                  <PiLink className="w-3 h-3" />
                ) : (
                  <PiFile className="w-3 h-3" />
                )}
                <span className="truncate max-w-xs">{source}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default UploadedContentIndicator;
