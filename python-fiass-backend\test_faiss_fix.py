#!/usr/bin/env python3
"""
Test script to verify FAISS retrieval fix for embedding model mismatch
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from full_code import retrieve_from_faiss_query, load_faiss_index

def test_faiss_retrieval():
    """Test FAISS retrieval with the dwindele index"""
    
    print("🧪 Testing FAISS retrieval fix...")
    
    # Test loading the index first
    print("\n1. Testing index loading...")
    faiss_index, metadata_store, success = load_faiss_index("dwindele")
    
    if not success:
        print("❌ Failed to load FAISS index")
        return False
    
    print(f"✅ Successfully loaded index with {faiss_index.ntotal} vectors")
    print(f"✅ Index dimension: {faiss_index.d}")
    print(f"✅ Metadata entries: {len(metadata_store)}")
    
    # Test retrieval
    print("\n2. Testing query retrieval...")
    test_query = "Explain STRUCTURE OF THE ATMOSPHERE"
    
    try:
        results = retrieve_from_faiss_query(test_query, "dwindele", k=5)
        
        if not results:
            print("❌ No results returned from FAISS query")
            return False
        
        print(f"✅ Retrieved {len(results)} results")
        
        # Display first result
        if results:
            first_result = results[0]
            print(f"\n📄 First result:")
            print(f"   Score: {first_result.get('score', 'N/A')}")
            print(f"   Text preview: {first_result.get('metadata', {}).get('chunk_text', 'N/A')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during retrieval: {e}")
        return False

if __name__ == "__main__":
    success = test_faiss_retrieval()
    if success:
        print("\n🎉 FAISS retrieval test PASSED!")
    else:
        print("\n💥 FAISS retrieval test FAILED!")
    
    sys.exit(0 if success else 1)