# FAISS Migration Requirements
# Install these packages for FAISS functionality

# Core FAISS package (choose one based on your system)
faiss-cpu>=1.7.0  # For CPU-only systems
# faiss-gpu>=1.7.0  # For GPU-enabled systems (uncomment if needed)

# Core dependencies
flask>=2.0.0
flask-cors>=3.0.0
numpy>=1.21.0
pandas>=1.3.0
langchain-huggingface>=0.0.1
python-dotenv>=0.19.0
openai>=1.0.0
requests>=2.25.0
sentence-transformers>=2.2.0

# Article processor dependencies
beautifulsoup4>=4.11.0  # For HTML parsing (bs4)
aiohttp>=3.8.0  # For async HTTP requests
urllib3>=1.26.0  # For HTTP retry functionality (usually comes with requests)

# Document processing dependencies
PyPDF2>=3.0.0  # For PDF processing
# pdfplumber>=0.7.0  # Alternative PDF processor (uncomment if needed)

# Audio processing dependencies (if using audio processor)
# pydub>=0.25.0  # For audio file processing
# SpeechRecognition>=3.10.0  # For speech-to-text

# YouTube processing dependencies (if using youtube processor)
# yt-dlp>=2023.1.0  # For YouTube video downloading
# youtube-transcript-api>=0.6.0  # For YouTube transcript extraction

# Optional: For better performance
# numba>=0.56.0  # JIT compilation for faster operations
# Complete Requirements for FAISS Backend with All Processors
# This file includes all dependencies needed for deployment

# Core Flask and API dependencies
flask>=2.3.0
flask-cors>=4.0.0
python-dotenv>=1.0.0
requests>=2.31.0

# Data processing and ML dependencies
numpy>=1.25.0
pandas>=2.1.0
openai>=1.3.0
sentence-transformers>=2.2.0
langchain-huggingface>=0.0.2

# FAISS vector database
faiss-cpu>=1.7.0
# faiss-gpu>=1.7.0  # Uncomment for GPU support

# Article processor dependencies
beautifulsoup4>=4.11.0  # For HTML parsing
aiohttp>=3.8.0  # For async HTTP requests
urllib3>=1.26.0  # For HTTP retry functionality

# Document processing dependencies
PyPDF2>=3.0.0  # For PDF processing
# pdfplumber>=0.7.0  # Alternative PDF processor

# Excel processing dependencies
openpyxl>=3.1.0  # For Excel file processing (.xlsx)
xlrd>=2.0.0  # For legacy Excel files (.xls)

# Audio processing dependencies (optional)
# pydub>=0.25.0  # For audio file processing
# SpeechRecognition>=3.10.0  # For speech-to-text

# YouTube processing dependencies (optional)
# yt-dlp>=2023.1.0  # For YouTube video downloading
# youtube-transcript-api>=0.6.0  # For YouTube transcript extraction

# Database dependencies (if using Pinecone)
pinecone>=3.0.0

# Translation dependencies
deep-translator>=1.11.0  # For Google Translate API (more stable than googletrans)

# Deployment-specific dependencies
certifi>=2023.7.22  # For SSL certificate handling
urllib3>=1.26.0,<2.0.0  # HTTP library with retry support
charset-normalizer>=3.2.0  # Character encoding detection

# Note: Built-in modules like uuid, hashlib, datetime, json, os, threading,
# concurrent.futures, asyncio, typing, time, and re don't need to be installed

# Deployment Notes:
# 1. For Docker: COPY requirements_faiss.txt . && RUN pip install -r requirements_faiss.txt
# 2. For Heroku: Ensure this file is named requirements.txt in root directory
# 3. For AWS Lambda: Use pip install -r requirements_faiss.txt -t .
# 4. For Google Cloud: Include in requirements.txt for Cloud Functions/App Engine
