import React, { useRef, useEffect } from 'react';
import { PiGlobe, PiCaretDown } from 'react-icons/pi';

interface Language {
  name: string;
  code: string;
  color: string;
}

interface LanguageSelectorProps {
  selectedLanguage: string;
  onLanguageChange: (language: string) => void;
  showLanguageMenu: boolean;
  setShowLanguageMenu: (show: boolean) => void;
  disabled?: boolean;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  selectedLanguage,
  onLanguageChange,
  showLanguageMenu,
  setShowLanguageMenu,
  disabled = false
}) => {
  const languageMenuRef = useRef<HTMLDivElement>(null);

  // Available languages for voice input with their language codes - English, Tamil, Telugu, and Kannada
  const languages: Language[] = [
    { name: "English", code: "en-US", color: "blue" },
    { name: "Tamil", code: "ta-IN", color: "purple" },
    { name: "Telugu", code: "te-IN", color: "green" },
    { name: "Kannada", code: "kn-IN", color: "orange" }
  ];

  // <PERSON><PERSON> click outside to close language menu
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {
        setShowLanguageMenu(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [setShowLanguageMenu]);

  const handleSelectLanguage = async (e: React.MouseEvent, language: string) => {
    // Prevent the event from bubbling up and triggering form submission
    e.preventDefault();
    e.stopPropagation();

    onLanguageChange(language);
    setShowLanguageMenu(false);
  };

  const getLanguageColor = (language: string) => {
    const lang = languages.find(l => l.name === language);
    return lang ? lang.color : 'blue';
  };

  const getButtonColor = () => {
    const color = getLanguageColor(selectedLanguage);
    if (disabled) return 'text-gray-400 border-gray-300';
    
    switch (color) {
      case 'purple':
        return 'text-purple-600 border-purple-300 hover:border-purple-400';
      case 'green':
        return 'text-green-600 border-green-300 hover:border-green-400';
      case 'orange':
        return 'text-orange-600 border-orange-300 hover:border-orange-400';
      default:
        return 'text-blue-600 border-blue-300 hover:border-blue-400';
    }
  };

  const getDropdownItemColor = (language: string) => {
    const color = getLanguageColor(language);
    switch (color) {
      case 'purple':
        return 'hover:bg-purple-50 hover:text-purple-700';
      case 'green':
        return 'hover:bg-green-50 hover:text-green-700';
      case 'orange':
        return 'hover:bg-orange-50 hover:text-orange-700';
      default:
        return 'hover:bg-blue-50 hover:text-blue-700';
    }
  };

  return (
    <div className="relative" ref={languageMenuRef}>
      {/* Language selector button */}
      <button
        type="button"
        onClick={() => !disabled && setShowLanguageMenu(!showLanguageMenu)}
        disabled={disabled}
        className={`
          flex items-center gap-2 px-3 py-2 border rounded-lg text-sm font-medium transition-all
          ${getButtonColor()}
          ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
        `}
        title={disabled ? 'Language selection disabled' : 'Select language'}
      >
        <PiGlobe className="w-4 h-4" />
        <span>{selectedLanguage}</span>
        <PiCaretDown className={`w-3 h-3 transition-transform ${showLanguageMenu ? 'rotate-180' : ''}`} />
      </button>

      {/* Language dropdown menu */}
      {showLanguageMenu && !disabled && (
        <div className="absolute top-full left-0 mt-1 w-40 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-lg z-50 overflow-hidden animate-fadeIn">
          {languages.map((language) => (
            <button
              key={language.name}
              onClick={(e) => handleSelectLanguage(e, language.name)}
              className={`
                w-full flex items-center gap-3 px-3 py-2 text-left text-sm transition-colors
                ${selectedLanguage === language.name 
                  ? `bg-${language.color}-100 text-${language.color}-700 dark:bg-${language.color}-900/20` 
                  : `text-gray-700 dark:text-gray-300 ${getDropdownItemColor(language.name)}`
                }
              `}
            >
              <PiGlobe className={`w-4 h-4 text-${language.color}-500`} />
              <span>{language.name}</span>
              {selectedLanguage === language.name && (
                <div className={`ml-auto w-2 h-2 rounded-full bg-${language.color}-500`} />
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
