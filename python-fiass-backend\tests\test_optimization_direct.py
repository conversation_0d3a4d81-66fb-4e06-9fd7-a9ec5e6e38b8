#!/usr/bin/env python3
"""
Direct test of the optimization logic without making HTTP requests
"""

import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

def test_optimization_logic():
    """Test the optimization logic directly"""
    
    print("🚀 Testing Language Optimization Logic")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        {
            "name": "Tamil Query with Tamil Index",
            "selected_language": "Tamil",
            "requested_index_name": "tamil",
            "target_language": None,
            "expected_early_optimization": True
        },
        {
            "name": "Telugu Query with Telugu Index", 
            "selected_language": "Telugu",
            "requested_index_name": "telugu",
            "target_language": None,
            "expected_early_optimization": True
        },
        {
            "name": "Kannada Query with Kannada Index",
            "selected_language": "Kannada",
            "requested_index_name": "kannada",
            "target_language": None,
            "expected_early_optimization": True
        },
        {
            "name": "Oriya Query with Oriya Index",
            "selected_language": "Oriya",
            "requested_index_name": "oriya",
            "target_language": None,
            "expected_early_optimization": True
        },
        {
            "name": "English Query with Tamil Index",
            "selected_language": "English",
            "requested_index_name": "tamil",
            "target_language": None,
            "expected_early_optimization": False
        },
        {
            "name": "Tamil Query with English Index",
            "selected_language": "Tamil",
            "requested_index_name": "default",
            "target_language": None,
            "expected_early_optimization": False
        }
    ]
    
    # Regional languages and mapping (from the actual code)
    regional_languages = ["Tamil", "Telugu", "Kannada", "Oriya"]
    regional_index_map = {
        "Tamil": "tamil",
        "Telugu": "telugu", 
        "Kannada": "kannada",
        "Oriya": "oriya"
    }
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        
        # Extract test parameters
        selected_language = test_case["selected_language"]
        requested_index_name = test_case["requested_index_name"]
        target_language = test_case["target_language"]
        expected = test_case["expected_early_optimization"]
        
        # Apply the early optimization logic from the code
        early_optimization_detected = False
        if (selected_language in regional_languages and 
            requested_index_name and 
            requested_index_name.lower() == regional_index_map.get(selected_language, "").lower() and
            (not target_language or target_language == selected_language)):
            
            early_optimization_detected = True
        
        # Check result
        success = early_optimization_detected == expected
        
        print(f"   Language: {selected_language}")
        print(f"   Index: {requested_index_name}")
        print(f"   Target: {target_language}")
        print(f"   Expected Optimization: {expected}")
        print(f"   Actual Optimization: {early_optimization_detected}")
        print(f"   Result: {'✅ PASS' if success else '❌ FAIL'}")
        
        results.append({
            "name": test_case["name"],
            "success": success,
            "expected": expected,
            "actual": early_optimization_detected
        })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results if r["success"])
    total = len(results)
    
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"{status} {result['name']}")
        if not result["success"]:
            print(f"     Expected: {result['expected']}, Got: {result['actual']}")
    
    return results

def test_data_language_optimization():
    """Test the data language optimization logic"""
    
    print("\n" + "=" * 60)
    print("🚀 Testing Data Language Optimization Logic")
    print("=" * 60)
    
    # Test cases for data language optimization
    test_cases = [
        {
            "name": "Tamil Query + Tamil Data",
            "selected_language": "Tamil",
            "data_language": "Tamil",
            "target_language": None,
            "expected_optimization": True
        },
        {
            "name": "Telugu Query + Telugu Data",
            "selected_language": "Telugu", 
            "data_language": "Telugu",
            "target_language": None,
            "expected_optimization": True
        },
        {
            "name": "Kannada Query + Kannada Data",
            "selected_language": "Kannada",
            "data_language": "Kannada", 
            "target_language": None,
            "expected_optimization": True
        },
        {
            "name": "Oriya Query + Oriya Data",
            "selected_language": "Oriya",
            "data_language": "Oriya",
            "target_language": None,
            "expected_optimization": True
        },
        {
            "name": "Tamil Query + English Data",
            "selected_language": "Tamil",
            "data_language": "English",
            "target_language": None,
            "expected_optimization": False
        },
        {
            "name": "English Query + Tamil Data",
            "selected_language": "English",
            "data_language": "Tamil",
            "target_language": None,
            "expected_optimization": False
        }
    ]
    
    regional_languages = ["Tamil", "Telugu", "Kannada", "Oriya"]
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        
        # Extract parameters
        selected_language = test_case["selected_language"]
        data_language = test_case["data_language"]
        target_language = test_case["target_language"]
        expected = test_case["expected_optimization"]
        
        # Apply the data language optimization logic
        early_optimization_detected = False  # Assume this was false
        
        optimized_direct_processing = False
        if (early_optimization_detected or 
            (selected_language in regional_languages and 
             data_language == selected_language and 
             (not target_language or target_language == selected_language))):
            optimized_direct_processing = True
        
        # Check result
        success = optimized_direct_processing == expected
        
        print(f"   Query Language: {selected_language}")
        print(f"   Data Language: {data_language}")
        print(f"   Target Language: {target_language}")
        print(f"   Expected Optimization: {expected}")
        print(f"   Actual Optimization: {optimized_direct_processing}")
        print(f"   Result: {'✅ PASS' if success else '❌ FAIL'}")
        
        results.append({
            "name": test_case["name"],
            "success": success,
            "expected": expected,
            "actual": optimized_direct_processing
        })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DATA LANGUAGE TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results if r["success"])
    total = len(results)
    
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    return results

if __name__ == "__main__":
    print("🚀 Direct Language Optimization Test")
    print("Testing the optimization logic without HTTP requests")
    print("=" * 60)
    
    # Test early optimization logic
    early_results = test_optimization_logic()
    
    # Test data language optimization logic
    data_results = test_data_language_optimization()
    
    # Overall summary
    total_tests = len(early_results) + len(data_results)
    total_passed = sum(1 for r in early_results if r["success"]) + sum(1 for r in data_results if r["success"])
    
    print(f"\n🎯 OVERALL SUMMARY")
    print(f"=" * 30)
    print(f"Total Tests: {total_tests}")
    print(f"Total Passed: {total_passed}")
    print(f"Overall Success Rate: {(total_passed/total_tests)*100:.1f}%")
    
    if total_passed == total_tests:
        print(f"\n✅ ALL TESTS PASSED! Optimization logic is working correctly.")
    else:
        print(f"\n❌ Some tests failed. Please review the optimization logic.")
    
    print("\n✅ Test completed!")