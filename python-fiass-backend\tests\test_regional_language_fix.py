#!/usr/bin/env python3
"""
Test script to verify regional language processing fixes for FAISS queries.
This script tests the /financial_query endpoint with regional language queries.
"""

import requests
import json
import sys
import time

# Configuration
BASE_URL = "http://localhost:5000"
FINANCIAL_QUERY_ENDPOINT = f"{BASE_URL}/financial_query"

def test_regional_language_query(query, language, index_name, description):
    """Test a regional language query and analyze the response."""
    print(f"\n{'='*60}")
    print(f"TEST: {description}")
    print(f"Query: {query}")
    print(f"Language: {language}")
    print(f"Index: {index_name}")
    print(f"{'='*60}")
    
    # Prepare request data
    request_data = {
        "query": query,
        "language": language,
        "index_name": index_name,
        "client_email": "<EMAIL>",
        "enable_translation": False  # Test direct processing
    }
    
    try:
        # Send request
        response = requests.post(FINANCIAL_QUERY_ENDPOINT, json=request_data, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # Analyze response
            print(f"✅ Request successful!")
            print(f"🔍 Response Analysis:")
            print(f"   - AI Response Length: {len(data.get('ai_response', ''))}")
            print(f"   - AI Response Preview: {data.get('ai_response', '')[:100]}...")
            print(f"   - Retrieved Documents: {len(data.get('retrieved_documents', []))}")
            print(f"   - Index Used: {data.get('index_used', 'N/A')}")
            print(f"   - Search Engine: {data.get('search_engine', 'N/A')}")
            print(f"   - Data Language: {data.get('data_language', 'N/A')}")
            print(f"   - Direct Regional Processing: {data.get('direct_regional_processing', False)}")
            print(f"   - Regional Language Detected: {data.get('regional_language_detected', False)}")
            print(f"   - Corruption Detection Bypassed: {data.get('corruption_detection_bypassed', False)}")
            print(f"   - Query Translated: {data.get('query_translated', False)}")
            
            # Check cross-language processing
            cross_lang = data.get('cross_language_processing', {})
            if cross_lang:
                print(f"   - Cross-Language Applied: {cross_lang.get('applied', False)}")
                print(f"   - Cross-Language Reason: {cross_lang.get('reason', 'N/A')}")
            
            # Check for related questions
            related_questions = data.get('related_questions', [])
            print(f"   - Related Questions: {len(related_questions)}")
            if related_questions:
                print(f"   - First Related Question: {related_questions[0][:50]}...")
            
            # Check sentence analysis
            sentence_analysis = data.get('sentence_analysis', [])
            print(f"   - Sentence Analysis: {len(sentence_analysis)} sentences")
            
            return True, data
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ Request error: {str(e)}")
        return False, None

def main():
    """Run comprehensive tests for regional language processing."""
    print("🚀 Starting Regional Language Processing Tests")
    print(f"Testing endpoint: {FINANCIAL_QUERY_ENDPOINT}")
    
    # Test cases
    test_cases = [
        {
            "query": "பங்குச் சந்தையில் முதலீடு செய்வது எப்படி?",
            "language": "Tamil",
            "index_name": "tamil",
            "description": "Tamil query with Tamil index (direct processing)"
        },
        {
            "query": "స్టాక్ మార్కెట్‌లో ఎలా పెట్టుబడి పెట్టాలి?",
            "language": "Telugu",
            "index_name": "telugu",
            "description": "Telugu query with Telugu index (direct processing)"
        },
        {
            "query": "ಷೇರು ಮಾರುಕಟ್ಟೆಯಲ್ಲಿ ಹೂಡಿಕೆ ಮಾಡುವುದು ಹೇಗೆ?",
            "language": "Kannada",
            "index_name": "kannada",
            "description": "Kannada query with Kannada index (direct processing)"
        },
        {
            "query": "How to invest in stock market?",
            "language": "English",
            "index_name": "tamil",
            "description": "English query with Tamil index (cross-language processing)"
        },
        {
            "query": "பங்குச் சந்தையில் முதலீடு செய்வது எப்படி?",
            "language": "Tamil",
            "index_name": "default",
            "description": "Tamil query with default index (standard processing)"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Running Test {i}/{len(test_cases)}")
        success, data = test_regional_language_query(**test_case)
        results.append({
            "test_number": i,
            "success": success,
            "data": data,
            **test_case
        })
        
        # Wait between tests
        time.sleep(2)
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    successful_tests = sum(1 for r in results if r["success"])
    total_tests = len(results)
    
    print(f"Total Tests: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {total_tests - successful_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    # Detailed results
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"\nTest {result['test_number']}: {status}")
        print(f"  Description: {result['description']}")
        if result["success"] and result["data"]:
            data = result["data"]
            print(f"  Regional Processing: {data.get('direct_regional_processing', False)}")
            print(f"  Corruption Bypassed: {data.get('corruption_detection_bypassed', False)}")
            print(f"  Response Quality: {'Good' if len(data.get('ai_response', '')) > 50 else 'Poor'}")
    
    print(f"\n🎉 Testing completed!")
    
    if successful_tests == total_tests:
        print("🌟 All tests passed! Regional language processing is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the server logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())