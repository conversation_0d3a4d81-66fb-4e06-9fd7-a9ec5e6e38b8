'use client';

import React, { useState, useEffect } from 'react';
import { ApiService } from '@/components/chatComponents/services/ApiService';

/**
 * Test component to verify user email detection and index filtering functionality.
 * This component helps debug and verify that the client email filtering is working correctly.
 */
const UserEmailDetectionTest: React.FC = () => {
  const [userEmail, setUserEmail] = useState<string>('');
  const [detectedSources, setDetectedSources] = useState<string[]>([]);
  const [userIndexes, setUserIndexes] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [testResults, setTestResults] = useState<any>({});

  useEffect(() => {
    detectUserEmail();
    fetchUserIndexes();
  }, []);

  const detectUserEmail = () => {
    const sources: string[] = [];
    let detectedEmail = '';

    if (typeof window !== 'undefined') {
      // Check localStorage
      const localStorageEmail = localStorage.getItem('user_email');
      if (localStorageEmail) {
        sources.push(`localStorage.user_email: ${localStorageEmail}`);
        if (!detectedEmail) detectedEmail = localStorageEmail;
      }

      // Check sessionStorage
      const sessionStorageEmail = sessionStorage.getItem('user_email');
      if (sessionStorageEmail) {
        sources.push(`sessionStorage.user_email: ${sessionStorageEmail}`);
        if (!detectedEmail) detectedEmail = sessionStorageEmail;
      }

      // Check faiss_client_email
      const faissClientEmail = localStorage.getItem('faiss_client_email');
      if (faissClientEmail) {
        sources.push(`localStorage.faiss_client_email: ${faissClientEmail}`);
        if (!detectedEmail) detectedEmail = faissClientEmail;
      }

      // Check user session
      const userSession = sessionStorage.getItem('resultUser');
      if (userSession) {
        try {
          const userData = JSON.parse(userSession);
          const email = userData.email || userData.username;
          if (email) {
            sources.push(`sessionStorage.resultUser: ${email}`);
            if (!detectedEmail) detectedEmail = email;
          }
        } catch (error) {
          sources.push(`sessionStorage.resultUser: Error parsing - ${error}`);
        }
      }
    }

    setDetectedSources(sources);
    setUserEmail(detectedEmail);
  };

  const fetchUserIndexes = async () => {
    setIsLoading(true);
    try {
      const indexes = await ApiService.fetchUserIndexes();
      setUserIndexes(indexes);
      
      setTestResults(prev => ({
        ...prev,
        indexFetch: {
          success: true,
          count: indexes.length,
          indexes: indexes
        }
      }));
    } catch (error) {
      console.error('Error fetching user indexes:', error);
      setTestResults(prev => ({
        ...prev,
        indexFetch: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const testEmailStorage = (email: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user_email', email);
      sessionStorage.setItem('user_email', email);
      localStorage.setItem('faiss_client_email', email);
      
      // Also create a mock user session
      const mockUserData = {
        email: email,
        username: email.split('@')[0],
        id: Date.now()
      };
      sessionStorage.setItem('resultUser', JSON.stringify(mockUserData));
      
      // Re-detect after setting
      setTimeout(() => {
        detectUserEmail();
        fetchUserIndexes();
      }, 100);
    }
  };

  const clearEmailStorage = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user_email');
      sessionStorage.removeItem('user_email');
      localStorage.removeItem('faiss_client_email');
      sessionStorage.removeItem('resultUser');
      
      // Re-detect after clearing
      setTimeout(() => {
        detectUserEmail();
        fetchUserIndexes();
      }, 100);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
        🧪 User Email Detection & Index Filtering Test
      </h2>

      {/* Current Detection Status */}
      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-blue-800 dark:text-blue-200">
          📧 Current Detection Status
        </h3>
        <div className="space-y-2">
          <p className="text-sm">
            <strong>Detected Email:</strong> 
            <span className={`ml-2 px-2 py-1 rounded ${userEmail ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'}`}>
              {userEmail || 'No email detected'}
            </span>
          </p>
          <div>
            <strong>Sources Found:</strong>
            {detectedSources.length > 0 ? (
              <ul className="mt-2 space-y-1">
                {detectedSources.map((source, index) => (
                  <li key={index} className="text-sm text-gray-600 dark:text-gray-300 ml-4">
                    • {source}
                  </li>
                ))}
              </ul>
            ) : (
              <span className="ml-2 text-red-600 dark:text-red-400">No sources found</span>
            )}
          </div>
        </div>
      </div>

      {/* User Indexes */}
      <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-green-800 dark:text-green-200">
          📁 User Indexes {isLoading && <span className="text-sm">(Loading...)</span>}
        </h3>
        <div className="space-y-2">
          <p className="text-sm">
            <strong>Index Count:</strong> 
            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 rounded">
              {userIndexes.length}
            </span>
          </p>
          {userIndexes.length > 0 && (
            <div>
              <strong>Available Indexes:</strong>
              <div className="mt-2 flex flex-wrap gap-2">
                {userIndexes.map((index, i) => (
                  <span key={i} className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full text-sm">
                    {index}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Test Controls */}
      <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-yellow-800 dark:text-yellow-200">
          🔧 Test Controls
        </h3>
        <div className="space-y-3">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => testEmailStorage('<EMAIL>')}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Set Test Email 1
            </button>
            <button
              onClick={() => testEmailStorage('<EMAIL>')}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Set Test Email 2
            </button>
            <button
              onClick={() => testEmailStorage('<EMAIL>')}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Set Admin Email
            </button>
            <button
              onClick={clearEmailStorage}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              Clear All
            </button>
          </div>
          <div className="flex gap-2">
            <button
              onClick={detectUserEmail}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              Re-detect Email
            </button>
            <button
              onClick={fetchUserIndexes}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            >
              Refresh Indexes
            </button>
          </div>
        </div>
      </div>

      {/* Test Results */}
      {Object.keys(testResults).length > 0 && (
        <div className="p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
            📊 Test Results
          </h3>
          <pre className="text-sm text-gray-600 dark:text-gray-300 overflow-auto">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
          📋 Instructions
        </h3>
        <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
          <li>• Use the test controls to simulate different user email scenarios</li>
          <li>• Check that email detection works from multiple sources</li>
          <li>• Verify that indexes are filtered correctly based on the detected email</li>
          <li>• The system should automatically detect user email on page load</li>
          <li>• Each user should only see their own indexes</li>
        </ul>
      </div>
    </div>
  );
};

export default UserEmailDetectionTest;