'use client';

import { useEffect } from 'react';

export default function HideStaticIndicator() {
  useEffect(() => {
    // Function to hide static indicator
    const hideStaticIndicator = () => {
      // Hide by class names
      const selectors = [
        '.nextjs-toast',
        '.nextjs-static-indicator-toast-wrapper',
        '[data-nextjs-toast-wrapper="true"]',
        '[data-nextjs-toast]',
        'div[role="status"]:has(.nextjs-static-indicator-toast-wrapper)',
        'div[role="status"].nextjs-toast'
      ];

      selectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            if (element instanceof HTMLElement) {
              element.style.display = 'none';
              element.style.visibility = 'hidden';
              element.style.opacity = '0';
              element.style.pointerEvents = 'none';
            }
          });
        } catch (error) {
          // Ignore selector errors
        }
      });

      // Also hide by content
      const allDivs = document.querySelectorAll('div[role="status"]');
      allDivs.forEach(div => {
        if (div.textContent?.includes('Static route')) {
          if (div instanceof HTMLElement) {
            div.style.display = 'none';
          }
        }
      });
    };

    // Run immediately
    hideStaticIndicator();

    // Run on DOM changes
    const observer = new MutationObserver(() => {
      hideStaticIndicator();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Run periodically as fallback
    const interval = setInterval(hideStaticIndicator, 1000);

    return () => {
      observer.disconnect();
      clearInterval(interval);
    };
  }, []);

  return null;
}