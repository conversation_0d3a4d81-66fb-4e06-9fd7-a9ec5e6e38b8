'use client';
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ye, PiEyeSlash } from 'react-icons/pi';

import { baseUrl, uid } from '@/components/api/api';

const ChangePassword = () => {
    const [oldPassword, setOldPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [username, setUsername] = useState('');  // <-- state to hold username

    const [confirmNewPassword, setConfirmNewPassword] = useState('');
    const [showSuccess, setShowSuccess] = useState(false);
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [showPass, setShowPass] = useState({
        old: false,
        new: false,
        confirm: false,
    });

    useEffect(() => {
        const user = JSON.parse(sessionStorage.getItem('resultUser') || '{}');
        setUsername(user?.username || '');
        console.log('Username:', user?.username || '');
    }, []);


    const isValidPassword = (pass: string) =>
        /^(?=.*[A-Z]).{8,}$/.test(pass);

    const clearError = () => {
        setTimeout(() => setError(''), 3000);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Remove all validation rules related to password strength and matching old password
        if (newPassword === oldPassword) {
            setError('New password should not be same as old password.');
            clearError();
            return;
        }

        if (newPassword !== confirmNewPassword) {
            setError('New password and confirm password do not match.');
            clearError();
            return;
        }


        setLoading(true);
        try {
            const res = await fetch(`${baseUrl}/resetpwd`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    "xxxid": uid
                },
                body: JSON.stringify({
                    username,
                    password: oldPassword,
                    new_password: newPassword,
                }),
            });

            const data = await res.json();

            if (!res.ok) {
                if (data.errorCode === '103') setError('Invalid old password.');
                else setError(data.errorMsg || 'Failed to change password.');
                clearError();
            } else if (data.statusCode === 200) {
                setShowSuccess(true);
                setOldPassword('');
                setNewPassword('');
                setConfirmNewPassword('');
                setTimeout(() => setShowSuccess(false), 3000);
            }
        } catch (err) {
            console.error(err);
            setError('Unexpected error. Try again.');
            clearError();
        } finally {
            setLoading(false);
        }
    };

    const toggleVisibility = (field: keyof typeof showPass) => {
        setShowPass((prev) => ({ ...prev, [field]: !prev[field] }));
    };

    const fields = [
        { id: 'old', label: 'Old Password', value: oldPassword, setter: setOldPassword },
        { id: 'new', label: 'New Password', value: newPassword, setter: setNewPassword },
        { id: 'confirm', label: 'Confirm New Password', value: confirmNewPassword, setter: setConfirmNewPassword },
    ];

    return (
        <div className="p-6 text-sm space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 text-sm">
                    {/* Username */}
                    <div>
                        <p className="text-gray-500 font-medium mb-1">Username</p>
                        <div className="relative">
                            <input
                                type="text"
                                value={username}
                                disabled
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10 opacity-70 cursor-not-allowed"
                            />
                        </div>
                    </div>

                    {/* Old Password */}
                    <div>
                        <p className="text-gray-500 font-medium mb-1">Old Password</p>
                        <div className="relative">
                            <input
                                type={showPass.old ? 'text' : 'password'}
                                value={oldPassword}
                                onChange={(e) => setOldPassword(e.target.value)}
                                required
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
                            />
                            <button
                                type="button"
                                onClick={() => toggleVisibility('old')}
                                className="absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-300"
                            >
                                {showPass.old ? <PiEye /> : <PiEyeSlash />}
                            </button>
                        </div>
                    </div>

                    {/* New Password */}
                    <div>
                        <p className="text-gray-500 font-medium mb-1">New Password</p>
                        <div className="relative">
                            <input
                                type={showPass.new ? 'text' : 'password'}
                                value={newPassword}
                                onChange={(e) => setNewPassword(e.target.value)}
                                required
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
                            />
                            <button
                                type="button"
                                onClick={() => toggleVisibility('new')}
                                className="absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-300"
                            >
                                {showPass.new ? <PiEye /> : <PiEyeSlash />}
                            </button>
                        </div>
                    </div>

                    {/* Confirm New Password */}
                    <div>
                        <p className="text-gray-500 font-medium mb-1">Confirm New Password</p>
                        <div className="relative">
                            <input
                                type={showPass.confirm ? 'text' : 'password'}
                                value={confirmNewPassword}
                                onChange={(e) => setConfirmNewPassword(e.target.value)}
                                required
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
                            />
                            <button
                                type="button"
                                onClick={() => toggleVisibility('confirm')}
                                className="absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-300"
                            >
                                {showPass.confirm ? <PiEye /> : <PiEyeSlash />}
                            </button>
                        </div>
                    </div>
                </div>

                {error && <p className="text-sm text-red-600">{error}</p>}
                <button
                    type="submit"
                    disabled={loading}
                    className="w-48 mx-auto block bg-blue-600 text-white font-semibold py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                    {loading ? 'Changing...' : 'Change Password'}
                </button>

            </form>



            {showSuccess && (
                <div className="fixed top-4 right-4 bg-green-500 text-white py-2 px-4 rounded shadow-lg z-50">
                    Password changed successfully!
                </div>
            )}
        </div>
    );
};

export default ChangePassword;
