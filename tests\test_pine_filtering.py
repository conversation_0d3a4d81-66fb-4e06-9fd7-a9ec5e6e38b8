#!/usr/bin/env python3
"""
Test Script for PINE Collection Email-Based Filtering

This script tests the email-based filtering functionality to ensure
users only see their own data based on email authentication.
"""

import sys
import os
import sqlite3
import json
from typing import List, Dict, Any

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'python-fiass-backend'))

try:
    from pine_filter_service import PineFilterService
    from database import get_connection, init_db
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


class PineFilteringTest:
    """Test class for PINE collection email-based filtering."""
    
    def __init__(self):
        self.test_users = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        self.test_indices = [
            "user1-documents",
            "user1-articles", 
            "user2-documents",
            "shared-index",
            "admin-reports"
        ]
        
    def setup_test_data(self):
        """Set up test data in the database."""
        print("Setting up test data...")
        
        # Initialize database
        init_db()
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # Clear existing test data
        cursor.execute("DELETE FROM pine_collection WHERE email LIKE '%@example.com'")
        
        # Insert test data
        test_records = [
            ("user1-documents", "<EMAIL>"),
            ("user1-articles", "<EMAIL>"),
            ("user2-documents", "<EMAIL>"),
            ("shared-index", "<EMAIL>"),
            ("shared-index", "<EMAIL>"),  # Same index, different users
            ("admin-reports", "<EMAIL>"),
        ]
        
        for index_name, email in test_records:
            cursor.execute(
                "INSERT OR REPLACE INTO pine_collection (index_name, email) VALUES (?, ?)",
                (index_name, email)
            )
        
        conn.commit()
        conn.close()
        print("✅ Test data setup complete")
    
    def test_email_validation(self):
        """Test email validation functionality."""
        print("\n🧪 Testing email validation...")
        
        test_cases = [
            ("<EMAIL>", True),
            ("<EMAIL>", True),
            ("", False),
            ("   ", False),
            ("invalid-email", False),
            ("@domain.com", False),
            ("user@", False),
        ]
        
        for email, expected in test_cases:
            is_valid, message = PineFilterService.validate_email(email)
            if is_valid == expected:
                print(f"✅ Email '{email}': {message}")
            else:
                print(f"❌ Email '{email}': Expected {expected}, got {is_valid}")
    
    def test_user_indices_retrieval(self):
        """Test retrieving user-specific indices."""
        print("\n🧪 Testing user indices retrieval...")
        
        for user_email in self.test_users:
            success, message, indices = PineFilterService.get_user_indices(user_email)
            
            if success:
                print(f"✅ {user_email}: Found {len(indices)} indices - {indices}")
                
                # Verify no cross-user data leakage
                for index in indices:
                    has_access, _ = PineFilterService.validate_user_access(user_email, index)
                    if not has_access:
                        print(f"❌ Data leakage detected: {user_email} should not have access to {index}")
            else:
                print(f"❌ {user_email}: {message}")
    
    def test_access_validation(self):
        """Test access validation for specific indices."""
        print("\n🧪 Testing access validation...")
        
        test_cases = [
            ("<EMAIL>", "user1-documents", True),
            ("<EMAIL>", "user2-documents", False),
            ("<EMAIL>", "user2-documents", True),
            ("<EMAIL>", "user1-documents", False),
            ("<EMAIL>", "admin-reports", True),
            ("<EMAIL>", "user1-documents", False),
            ("<EMAIL>", "shared-index", True),
            ("<EMAIL>", "shared-index", True),
            ("<EMAIL>", "shared-index", False),
        ]
        
        for user_email, index_name, expected_access in test_cases:
            has_access, message = PineFilterService.validate_user_access(user_email, index_name)
            
            if has_access == expected_access:
                status = "✅" if has_access else "✅ (correctly denied)"
                print(f"{status} {user_email} -> {index_name}: {message}")
            else:
                print(f"❌ {user_email} -> {index_name}: Expected {expected_access}, got {has_access}")
    
    def test_case_insensitive_matching(self):
        """Test case-insensitive email matching."""
        print("\n🧪 Testing case-insensitive email matching...")
        
        # Test with different case variations
        email_variations = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "  <EMAIL>  ",  # With whitespace
        ]
        
        for email_variant in email_variations:
            success, message, indices = PineFilterService.get_user_indices(email_variant)
            
            if success and len(indices) > 0:
                print(f"✅ {email_variant.strip()}: Found {len(indices)} indices")
            else:
                print(f"❌ {email_variant.strip()}: {message}")
    
    def test_filter_indices_by_user(self):
        """Test filtering a list of indices by user access."""
        print("\n🧪 Testing indices filtering by user...")
        
        all_indices = ["user1-documents", "user2-documents", "shared-index", "admin-reports", "nonexistent-index"]
        
        for user_email in self.test_users:
            success, message, filtered_indices = PineFilterService.filter_indices_by_user(user_email, all_indices)
            
            if success:
                print(f"✅ {user_email}: Filtered {len(all_indices)} -> {len(filtered_indices)} indices")
                print(f"   Accessible: {filtered_indices}")
                
                # Verify all filtered indices are actually accessible
                for index in filtered_indices:
                    has_access, _ = PineFilterService.validate_user_access(user_email, index)
                    if not has_access:
                        print(f"❌ Filtering error: {user_email} should not have access to {index}")
            else:
                print(f"❌ {user_email}: {message}")
    
    def test_error_handling(self):
        """Test error handling for edge cases."""
        print("\n🧪 Testing error handling...")
        
        # Test with invalid inputs
        test_cases = [
            ("", "valid-index"),
            ("<EMAIL>", ""),
            (None, "valid-index"),
            ("<EMAIL>", None),
        ]
        
        for email, index_name in test_cases:
            try:
                success, message = PineFilterService.validate_user_access(email, index_name)
                if not success:
                    print(f"✅ Properly handled invalid input: email='{email}', index='{index_name}'")
                else:
                    print(f"❌ Should have failed for: email='{email}', index='{index_name}'")
            except Exception as e:
                print(f"✅ Exception properly caught: {e}")
    
    def cleanup_test_data(self):
        """Clean up test data from the database."""
        print("\n🧹 Cleaning up test data...")
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # Remove test data
        cursor.execute("DELETE FROM pine_collection WHERE email LIKE '%@example.com'")
        
        conn.commit()
        conn.close()
        print("✅ Test data cleanup complete")
    
    def run_all_tests(self):
        """Run all tests."""
        print("🚀 Starting PINE Collection Email-Based Filtering Tests")
        print("=" * 60)
        
        try:
            self.setup_test_data()
            self.test_email_validation()
            self.test_user_indices_retrieval()
            self.test_access_validation()
            self.test_case_insensitive_matching()
            self.test_filter_indices_by_user()
            self.test_error_handling()
            
            print("\n" + "=" * 60)
            print("✅ All tests completed successfully!")
            
        except Exception as e:
            print(f"\n❌ Test failed with error: {e}")
            import traceback
            traceback.print_exc()
            
        finally:
            self.cleanup_test_data()


if __name__ == "__main__":
    # Run the tests
    test_runner = PineFilteringTest()
    test_runner.run_all_tests()
