// Backend configuration
const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || process.env.BACKEND_URL || 'http://localhost:5010';

/**
 * Format file size to human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size (e.g., "2.5 MB")
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

/**
 * Upload a CSV file to FAISS via the backend
 * @param {File} file - The CSV file to upload
 * @param {string} clientEmail - Client email identifier
 * @param {string} indexName - Name for the FAISS index
 * @param {string} updateMode - Update mode ('update' or 'new') (optional)
 * @param {AbortSignal} signal - AbortSignal for cancelling the upload (optional)
 * @param {Function} onProgress - Callback function for upload progress (optional)
 * @param {string} embedModel - Name of the embedding model to use (optional)
 * @returns {Promise} - Promise that resolves with the server response including upload_id
 */
export const uploadCSVToFaiss = async (
  file: File,
  clientEmail: string,
  indexName: string,
  updateMode?: string | null,
  signal?: AbortSignal,
  onProgress?: (progress: number) => void,
  embedModel?: string
): Promise<any> => {
  return new Promise((resolve, reject) => {
    try {
      // Validate file type
      if (file.type !== 'text/csv') {
        reject(new Error('Only CSV files are supported for FAISS upload'));
        return;
      }

      // Create a new FormData instance
      const formData = new FormData();
      formData.append('file', file);

      // Add client information to form data if provided
      if (clientEmail) {
        formData.append('client', clientEmail);
      }

      // Add index name to form data
      if (indexName) {
        formData.append('index_name', indexName);
      }

      // Add index name to form data
      if (indexName) {
        formData.append('index_name', indexName);
      }

      // Add update mode to form data if provided
      if (updateMode) {
        formData.append('update_mode', updateMode);
      }

      // Add embedding model to form data if provided
      if (embedModel) {
        formData.append('embed_model', embedModel);
      }

      // Create a new XMLHttpRequest and connect abort signal
      const xhr = new XMLHttpRequest();

      // Handle abort signal for client-side cancellation
      if (signal) {
        signal.onabort = () => {
          xhr.abort();
          // Instead of rejecting with an error, resolve with a cancellation object
          // This prevents the error from appearing in the console
          resolve({
            success: false,
            cancelled: true,
            message: 'Upload cancelled by user'
          });
        };
      }

      // Configure the request to our backend endpoint
      xhr.open('POST', `${BACKEND_BASE_URL}/api/upload-csv`, true);
      // Add authentication header only (no Content-Type for FormData)
      xhr.setRequestHeader('xxxid', 'FAISS');

      // Track upload progress if callback provided
      if (onProgress) {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            onProgress(progress);
          }
        };
      }

      // Handle response
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            resolve({
              success: true,
              message: 'CSV file uploaded successfully to FAISS',
              indexName: indexName // Use the user-provided index name
            });
          }
        } else {
          reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));
        }
      };

      // Handle network errors
      xhr.onerror = () => {
        reject(new Error('Network error occurred while uploading CSV file'));
      };

      // Send the request
      xhr.send(formData);
    } catch (error) {
      reject(error);
    }
  });
};

// Compatibility alias for existing code
export const uploadCSVToPinecone = uploadCSVToFaiss;

/**
 * Upload a single file to the server
 * @param {File} file - The file to upload
 * @param {Function} onProgress - Callback function for upload progress
 * @returns {Promise} - Promise that resolves with the server response
 */
export const uploadFile = async (
  file: File,
  onProgress?: (progress: number) => void
): Promise<any> => {
  return new Promise((resolve, reject) => {
    try {
      // Create a new FormData instance
      const formData = new FormData();
      formData.append('file', file);

      // Create a new XMLHttpRequest
      const xhr = new XMLHttpRequest();

      // Configure the request
      xhr.open('POST', `${BACKEND_BASE_URL}/api/upload`, true);

      // Track upload progress
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      };

      // Handle response
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            resolve({
              success: true,
              message: 'File uploaded successfully',
              fileName: file.name
            });
          }
        } else {
          reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));
        }
      };

      // Handle network errors
      xhr.onerror = () => {
        reject(new Error('Network error occurred while uploading file'));
      };

      // Send the request
      xhr.send(formData);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Upload multiple files to the server
 * @param {File[]} files - Array of files to upload
 * @param {Function} onProgress - Callback function for upload progress
 * @returns {Promise} - Promise that resolves with an array of server responses
 */
export const uploadMultipleFiles = async (
  files: File[],
  onProgress?: (fileName: string, progress: number) => void
): Promise<any[]> => {
  const uploadPromises = files.map((file) => {
    return uploadFile(file, (progress) => {
      if (onProgress) {
        onProgress(file.name, progress);
      }
    });
  });

  return Promise.all(uploadPromises);
};

/**
 * Check if a file type is allowed
 * @param {string} fileType - MIME type of the file
 * @param {string[]} allowedTypes - Array of allowed MIME types
 * @returns {boolean} - True if the file type is allowed
 */
export const isFileTypeAllowed = (fileType: string, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(fileType);
};

/**
 * Check if a file size is within the limit
 * @param {number} fileSize - Size of the file in bytes
 * @param {number} maxSizeMB - Maximum allowed size in MB
 * @returns {boolean} - True if the file size is within the limit
 */
export const isFileSizeValid = (fileSize: number, maxSizeMB: number): boolean => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return fileSize <= maxSizeBytes;
};

/**
 * List all CSV files stored in the database
 * @param {string} clientEmail - Optional client email to filter by
 * @returns {Promise} - Promise that resolves with the list of CSV files
 */
export const listCSVFiles = async (clientEmail?: string): Promise<any> => {
  try {
    const response = await fetch(`${BACKEND_BASE_URL}/api/list-csv-files`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_email: clientEmail
      })
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error listing CSV files:', error);
    throw error;
  }
};

/**
 * Get CSV data from the database
 * @param {string} indexName - Name of the index to get data for
 * @param {number} limit - Maximum number of rows to return (default: 100)
 * @param {number} offset - Number of rows to skip (default: 0)
 * @returns {Promise} - Promise that resolves with the CSV data
 */
export const getCSVData = async (indexName: string, limit: number = 100, offset: number = 0): Promise<any> => {
  try {
    const response = await fetch(`${BACKEND_BASE_URL}/api/get-csv-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        index_name: indexName,
        limit,
        offset
      })
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting CSV data:', error);
    throw error;
  }
};

/**
 * Get available embedding models
 * @returns {Promise} - Promise that resolves with the list of available embedding models
 */
export const getEmbeddingModels = async (): Promise<any> => {
  try {
    const response = await fetch(`${BACKEND_BASE_URL}/api/list-embedding-models`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting embedding models:', error);

    // Return fallback data when backend is not available
    return {
      success: true,
      models: {
        "all-MiniLM-L6-v2": {
          "name": "all-MiniLM-L6-v2",
          "description": "Sentence Transformers model for semantic similarity",
          "dimensions": 384
        },
        "all-mpnet-base-v2": {
          "name": "all-mpnet-base-v2",
          "description": "High-quality sentence embeddings",
          "dimensions": 768
        },
        "paraphrase-MiniLM-L6-v2": {
          "name": "paraphrase-MiniLM-L6-v2",
          "description": "Paraphrase detection model",
          "dimensions": 384
        }
      },
      default_model: "all-MiniLM-L6-v2"
    };
  }
};

/**
 * Check if an error or response is a cancellation
 * @param {any} errorOrResponse - Error object or response from upload/cancel functions
 * @returns {boolean} - True if the error/response indicates a cancellation
 */
export const isCancellation = (errorOrResponse: any): boolean => {
  // Check for our custom cancellation response
  if (errorOrResponse && errorOrResponse.cancelled === true) {
    return true;
  }

  // Check for error message containing cancellation text
  if (errorOrResponse instanceof Error) {
    const errorMessage = errorOrResponse.message.toLowerCase();
    return errorMessage.includes('cancel') ||
           errorMessage.includes('abort') ||
           errorMessage.includes('user interrupt');
  }

  // Check for response with cancellation status
  if (errorOrResponse && errorOrResponse.status === 'cancelled') {
    return true;
  }

  // Check for response with error_type indicating cancellation
  if (errorOrResponse && errorOrResponse.error_type === 'upload_cancelled') {
    return true;
  }

  return false;
};

/**
 * Fetch emails from the API
 * @returns {Promise<string[]>} - Promise that resolves with an array of email addresses
 */
export const fetchEmails = async (): Promise<string[]> => {
  try {
    const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eSearch', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'xxxid': 'QUKTYWK'
      }
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }

    const data = await response.json();

    if (data.statusCode === 200 && Array.isArray(data.source)) {
      // Parse each JSON string in the source array and extract emails
      const emails = data.source.map((jsonStr: string) => {
        try {
          const userObj = JSON.parse(jsonStr);
          return userObj.email || '';
        } catch (error) {
          console.error('Error parsing JSON:', error);
          return '';
        }
      }).filter(Boolean); // Remove empty strings

      return emails;
    }

    return [];
  } catch (error) {
    console.error('Error fetching emails:', error);
    return [];
  }
};

/**
 * Create PINE Collection Entry for index storage
 * @param {string} embedModel - Embedding model name (stored as api_key)
 * @param {string} indexName - Index name
 * @param {string} clientEmail - Client email
 * @returns {Promise<any>} - Promise that resolves with the server response
 */
export const createPineCollectionEntry = async (
  embedModel: string,
  indexName: string,
  clientEmail: string
): Promise<any> => {
  try {
    console.log(`Creating PINE collection entry: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${clientEmail}`);

    const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'xxxid': 'PINE'
      },
      body: JSON.stringify({
        api_key: embedModel,    // Store embedding model name as api_key
        index_name: indexName,  // Store provided index name
        client: clientEmail     // Store email as client
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Server error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('PINE collection entry created successfully:', result);
    return result;
  } catch (error) {
    console.error('Error creating PINE collection entry:', error);
    throw error;
  }
};

/**
 * Get all indexes for a specific email from PINE collection
 * @param {string} clientEmail - Client email to filter by
 * @returns {Promise<any[]>} - Promise that resolves with array of index data
 */
export const getIndexesByEmail = async (clientEmail: string): Promise<any[]> => {
  try {
    console.log(`Fetching indexes for email: ${clientEmail}`);

    const response = await fetch(
      `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&filtercount=1&f1_field=client_S&f1_op=eq&f1_value=${encodeURIComponent(clientEmail)}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'xxxid': 'PINE'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }

    const data = await response.json();
    console.log('PINE collection response for email:', data);

    if (data.statusCode === 200 && Array.isArray(data.source)) {
      // Parse each JSON string in the source array
      const indexes = data.source.map((jsonStr: string, index: number) => {
        try {
          const indexObj = JSON.parse(jsonStr);
          return {
            _id: indexObj._id?.$oid || indexObj._id || `pine-item-${index}`,
            email: indexObj.client || clientEmail,
            index_name: indexObj.index_name || 'N/A',
            embed_model: indexObj.api_key || 'N/A', // api_key contains the embedding model
            source: 'PINE' as const,
            originalData: indexObj
          };
        } catch (error) {
          console.error('Error parsing PINE index JSON:', error);
          return null;
        }
      }).filter((item: any) => item !== null);

      return indexes;
    }

    return [];
  } catch (error) {
    console.error('Error fetching indexes by email:', error);
    return [];
  }
};

/**
 * Check if a FAISS index exists
 * @param {string} indexName - Name of the index to check
 * @param {string} client - Client email or identifier (optional)
 * @param {string} embedModel - Name of the embedding model to use (optional)
 * @returns {Promise<{exists: boolean, embedding_model?: string}>} - Promise that resolves with info about the index
 */
export const checkIndexExists = async (
  indexName: string,
  client?: string,
  embedModel?: string
): Promise<{exists: boolean, embedding_model?: string}> => {
  try {
    const response = await fetch(`${BACKEND_BASE_URL}/api/check-index`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        index_name: indexName,
        client: client,
        embed_model: embedModel
      })
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      return {
        exists: data.exists,
        embedding_model: data.embedding_model
      };
    }

    return { exists: false };
  } catch (error) {
    console.error('Error checking if index exists:', error);
    return { exists: false };
  }
};

/**
 * Cancel an ongoing upload
 * @param {string} uploadId - The ID of the upload to cancel
 * @param {AbortController} abortController - Optional AbortController to abort the HTTP request
 * @returns {Promise<any>} - Promise that resolves with the server response
 */
export const cancelUpload = async (uploadId: string, abortController?: AbortController): Promise<any> => {
  try {
    // First, abort the HTTP request if an AbortController is provided
    if (abortController) {
      try {
        abortController.abort();
        console.log('HTTP request aborted');
      } catch (abortError) {
        // Don't log this as an error since it's expected behavior
        console.log('Note: AbortController already used or not applicable');
        // Continue with server-side cancellation even if client-side abort fails
      }
    }

    // Then, send a cancellation request to the server
    console.log(`Sending cancellation request for upload ${uploadId}`);
    const response = await fetch(`${BACKEND_BASE_URL}/api/cancel-upload`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        upload_id: uploadId
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Server error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Cancellation response:', data);

    // Verify cancellation by checking status
    try {
      const statusResponse = await checkUploadStatus(uploadId);
      console.log('Status after cancellation:', statusResponse);
    } catch (statusError) {
      console.error('Error checking status after cancellation:', statusError);
      // Continue even if status check fails
    }

    return data;
  } catch (error) {
    console.error('Error cancelling upload:', error);
    throw error;
  }
};

/**
 * Check the status of an ongoing upload
 * @param {string} uploadId - The ID of the upload to check
 * @param {boolean} silent - Whether to suppress console errors (default: false)
 * @returns {Promise<any>} - Promise that resolves with the upload status
 */
export const checkUploadStatus = async (uploadId: string, silent: boolean = false): Promise<any> => {
  try {
    const response = await fetch(`${BACKEND_BASE_URL}/api/upload-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        upload_id: uploadId
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Server error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    // Log cancellation status if detected
    if (data.success && data.cancelled) {
      console.log(`Upload ${uploadId} is marked as cancelled. Status: ${data.status}`);
    }

    return data;
  } catch (error) {
    if (!silent) {
      console.error('Error checking upload status:', error);
    }
    throw error;
  }
};
