def handle_query():
    """
    Improved Financial Query Handler with New Translation Flow:
    1. Detect query language (Kannada/Tamil/Telugu/Oriya)
    2. Search FAISS data in matching language index
    3. Get response in FAISS data language
    4. Translate final response using @vitalets/google-translate-api
    """
    data = request.get_json()
    query = data.get("query", "").strip()

    # Get client-specific configuration
    api_key = data.get("api_key")
    requested_index_name = data.get("index_name")
    client_email = data.get("client_email")
    target_language = data.get("target_language")  # Language for final response translation

    if not query:
        return jsonify({"error": "Query is required."}), 400

    # Import new Google Translate API service
    try:
        from services.google_translate_api_service import google_translate_service
        google_translate_available = google_translate_service.is_service_available()
        print(f"✅ Google Translate API service loaded (available: {google_translate_available})")
    except ImportError:
        print("⚠️ Google Translate API service not available")
        google_translate_available = False

    # Step 1: Detect query language using Unicode ranges for better accuracy
    def detect_query_language(text):
        """Detect language using Unicode ranges for better accuracy"""
        import re
        
        if re.search(r'[\u0C80-\u0CFF]', text):  # Kannada
            return "Kannada", "kn"
        elif re.search(r'[\u0B80-\u0BFF]', text):  # Tamil
            return "Tamil", "ta"
        elif re.search(r'[\u0C00-\u0C7F]', text):  # Telugu
            return "Telugu", "te"
        elif re.search(r'[\u0B00-\u0B7F]', text):  # Oriya
            return "Oriya", "or"
        else:
            return "English", "en"

    detected_language, detected_lang_code = detect_query_language(query)
    print(f"🔍 DETECTED QUERY LANGUAGE: {detected_language} ({detected_lang_code})")
    print(f"📝 Query: '{query[:50]}...'")

    # Step 2: Determine FAISS index to search
    # Priority: 1. Explicit index request, 2. Language-matching index, 3. User's indexes, 4. Default
    index_name = requested_index_name
    
    # Language to index mapping
    language_index_map = {
        "Kannada": "kannada",
        "Tamil": "tamil", 
        "Telugu": "telugu",
        "Oriya": "oriya"
    }

    if not index_name:
        # Try to find matching language index
        if detected_language in language_index_map:
            potential_index = language_index_map[detected_language]
            index_path = os.path.join(FAISS_DATA_DIR, potential_index)
            if os.path.exists(index_path):
                index_name = potential_index
                print(f"🎯 USING LANGUAGE-MATCHING INDEX: {index_name}")
            else:
                print(f"⚠️ Language-matching index '{potential_index}' not found")
        
        # Fallback to user's indexes or default
        if not index_name:
            if client_email:
                # Fetch user's available indexes from PINE collection
                try:
                    response = requests.get(
                        f"https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&f1_field=client_S&f1_op=eq&f1_value={client_email}",
                        headers={"Content-Type": "application/json", "xxxid": "PINE"}
                    )
                    
                    if response.ok:
                        pine_data = response.json()
                        if pine_data.get("statusCode") == 200 and pine_data.get("source"):
                            user_indexes = []
                            for item in pine_data["source"]:
                                try:
                                    record = json.loads(item)
                                    if record.get("index_name"):
                                        user_indexes.append(record.get("index_name"))
                                except json.JSONDecodeError:
                                    continue
                            
                            if user_indexes:
                                index_name = user_indexes[0]
                                print(f"🎯 USING USER INDEX: {index_name}")
                except Exception as e:
                    print(f"❌ Error fetching user indexes: {e}")
            
            # Final fallback to default
            if not index_name:
                index_name = "default"
                print(f"🎯 USING DEFAULT INDEX: {index_name}")

    # Fetch API key if needed
    if client_email and not api_key:
        try:
            response = requests.get(
                f"https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&f1_field=client_S&f1_op=eq&f1_value={client_email}",
                headers={"Content-Type": "application/json", "xxxid": "PINE"}
            )
            
            if response.ok:
                pine_data = response.json()
                if pine_data.get("statusCode") == 200 and pine_data.get("source"):
                    for item in pine_data["source"]:
                        try:
                            record = json.loads(item)
                            if record.get("api_key"):
                                api_key = record.get("api_key")
                                print(f"✅ Found API key for user: {client_email}")
                                break
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"❌ Error fetching API key: {e}")

    # Step 3: Load and search FAISS index
    index_path = os.path.join(FAISS_DATA_DIR, index_name)
    if not os.path.exists(index_path):
        return jsonify({"error": f"Index '{index_name}' not found."}), 404

    try:
        faiss_index_path = os.path.join(index_path, "index.faiss")
        metadata_path = os.path.join(index_path, "metadata.json")
        
        if not os.path.exists(faiss_index_path) or not os.path.exists(metadata_path):
            return jsonify({"error": f"Index '{index_name}' is incomplete."}), 404
        
        # Load FAISS index and metadata
        index = faiss.read_index(faiss_index_path)
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        print(f"✅ Loaded index '{index_name}' with {index.ntotal} vectors")
        
        # Search using original query (no translation needed for search)
        query_embedding = embedder.embed_query(query)
        query_vector = np.array([query_embedding], dtype=np.float32)
        
        k = min(10, index.ntotal)
        distances, indices = index.search(query_vector, k)
        
        print(f"🔍 Search completed. Found {len(indices[0])} results")
        
    except Exception as e:
        print(f"❌ Error during search: {str(e)}")
        return jsonify({"error": f"Search failed: {str(e)}"}), 500

    # Retrieve relevant documents
    relevant_docs = []
    for i, idx in enumerate(indices[0]):
        if idx != -1 and idx < len(metadata):
            doc = metadata[idx]
            doc['similarity_score'] = float(1 / (1 + distances[0][i]))
            relevant_docs.append(doc)

    if not relevant_docs:
        return jsonify({
            "ai_response": "I couldn't find relevant information for your query. Please try rephrasing your question.",
            "related_questions": [],
            "sources": [],
            "query_language": detected_language,
            "index_used": index_name
        })

    # Step 4: Generate AI response in FAISS data language
    context = "\n\n".join([
        f"Document {i+1}: {doc.get('content', doc.get('text', ''))}"
        for i, doc in enumerate(relevant_docs[:5])
    ])

    try:
        if not DEEPSEEK_API_KEY:
            return jsonify({"error": "AI service not configured"}), 500

        client = OpenAI(
            api_key=DEEPSEEK_API_KEY,
            base_url="https://api.deepseek.com"
        )

        # Generate AI response
        prompt = f"""Based on the following context, provide a comprehensive and helpful response to the user's query.

Context:
{context}

User Query: {query}

Please provide:
1. A detailed and informative response
2. Specific examples or data points when available
3. Clear explanations that are easy to understand

Response:"""

        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a helpful financial assistant. Provide accurate, detailed, and well-structured responses based on the given context."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.7
        )

        ai_response = response.choices[0].message.content.strip()
        print(f"✅ AI response generated successfully")

        # Generate related questions
        related_questions_prompt = f"""Based on the user's query: "{query}" and the context provided, generate 3-5 related questions that the user might be interested in asking next.

Context: {context[:500]}...

Generate questions that are:
1. Relevant to the original query
2. Helpful for further exploration
3. Specific and actionable

Related Questions:"""

        related_response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "Generate helpful related questions based on the user's query and context."},
                {"role": "user", "content": related_questions_prompt}
            ],
            max_tokens=300,
            temperature=0.8
        )

        related_questions_text = related_response.choices[0].message.content.strip()
        
        # Parse related questions
        related_questions = []
        for line in related_questions_text.split('\n'):
            line = line.strip()
            if line and (line[0].isdigit() or line.startswith('-') or line.startswith('•')):
                question = re.sub(r'^[\d\-•\.\)\s]+', '', line).strip()
                if question:
                    related_questions.append(question)

        print(f"✅ Generated {len(related_questions)} related questions")

    except Exception as e:
        print(f"❌ Error generating AI response: {str(e)}")
        return jsonify({"error": f"Failed to generate AI response: {str(e)}"}), 500

    # Step 5: Prepare response data
    response_data = {
        "ai_response": ai_response,
        "related_questions": related_questions,
        "sources": [
            {
                "content": doc.get('content', doc.get('text', ''))[:200] + "...",
                "similarity_score": doc['similarity_score'],
                "metadata": {k: v for k, v in doc.items() if k not in ['content', 'text', 'similarity_score']}
            }
            for doc in relevant_docs[:3]
        ],
        "query_metadata": {
            "original_query": query,
            "query_language": detected_language,
            "query_language_code": detected_lang_code,
            "index_used": index_name,
            "results_count": len(relevant_docs),
            "translation_flow": "improved_flow_v2"
        }
    }

    # Step 6: Translate response if target language is specified and different from detected language
    if target_language and google_translate_available:
        target_lang_code = target_language.lower()
        
        # Convert language names to codes if needed
        lang_name_to_code = {
            'kannada': 'kn', 'tamil': 'ta', 'telugu': 'te', 'oriya': 'or', 'odia': 'or', 'english': 'en'
        }
        if target_lang_code in lang_name_to_code:
            target_lang_code = lang_name_to_code[target_lang_code]
        
        # Only translate if target language is different from detected language
        if target_lang_code != detected_lang_code:
            print(f"🌐 TRANSLATING RESPONSE: {detected_language} -> {target_language}")
            
            try:
                # Translate the entire financial response
                translation_result = google_translate_service.translate_financial_response(
                    response_data, target_lang_code
                )
                
                if translation_result.get('success'):
                    response_data = translation_result['data']
                    print(f"✅ Response translated successfully to {target_language}")
                else:
                    print(f"⚠️ Translation failed: {translation_result.get('error', 'Unknown error')}")
                    response_data["translation_error"] = translation_result.get('error', 'Translation failed')
                    
            except Exception as e:
                print(f"❌ Translation error: {str(e)}")
                response_data["translation_error"] = str(e)
        else:
            print(f"🔄 No translation needed: target language matches detected language ({detected_language})")
            response_data["query_metadata"]["translation_skipped"] = "Same language as detected"

    elif target_language and not google_translate_available:
        print("⚠️ Translation requested but Google Translate API service not available")
        response_data["translation_error"] = "Translation service not available"

    print(f"🎉 Financial query processing completed successfully")
    print(f"   - Query Language: {detected_language}")
    print(f"   - Index Used: {index_name}")
    print(f"   - Results: {len(relevant_docs)} documents")
    print(f"   - Target Language: {target_language or 'None'}")

    return jsonify(response_data)