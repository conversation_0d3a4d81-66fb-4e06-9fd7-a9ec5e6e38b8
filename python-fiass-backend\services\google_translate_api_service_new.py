"""
Google Translate API Service using @vitalets/google-translate-api
This service provides high-quality translation for financial responses
with proper handling of capital words, special characters, and sentence integrity.
"""

import json
import subprocess
import os
import sys
from typing import Dict, Any, Optional, List
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GoogleTranslateAPIService:
    """
    Google Translate API Service using @vitalets/google-translate-api
    Provides robust translation with better handling of:
    - Capital words and proper nouns
    - Special characters and formatting
    - Complete sentence integrity
    - Financial terminology
    """
    
    def __init__(self):
        self.service_available = self._check_service_availability()
        self.node_script_path = self._setup_node_script()
        
        # Language mappings
        self.language_codes = {
            'Kannada': 'kn',
            'Tamil': 'ta',
            'Telugu': 'te', 
            'Oriya': 'or',
            'Odia': 'or',
            'English': 'en',
            'Hindi': 'hi'
        }
        
        self.language_names = {
            'kn': 'Kannada',
            'ta': 'Tamil',
            'te': 'Telugu',
            'or': 'Oriya', 
            'en': 'English',
            'hi': 'Hindi'
        }
        
    def _check_service_availability(self) -> bool:
        """Check if Node.js and required packages are available"""
        try:
            # Check if Node.js is available
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                logger.warning("Node.js not found")
                return False
                
            # Check if npm is available
            result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                logger.warning("npm not found")
                return False
                
            logger.info("✅ Node.js and npm are available")
            return True
            
        except Exception as e:
            logger.error(f"Error checking Node.js availability: {e}")
            return False
    
    def _setup_node_script(self) -> Optional[str]:
        """Setup the Node.js translation script"""
        if not self.service_available:
            return None
            
        try:
            # Create the services directory if it doesn't exist
            services_dir = os.path.dirname(os.path.abspath(__file__))
            script_path = os.path.join(services_dir, 'google_translate_script.js')
            
            # Create the Node.js script
            node_script = '''
const translate = require('@vitalets/google-translate-api');

async function translateText(text, targetLang, sourceLang = 'auto') {
    try {
        const result = await translate(text, {
            from: sourceLang,
            to: targetLang,
            fetchOptions: {
                agent: null // Disable proxy for better reliability
            }
        });
        
        return {
            success: true,
            translatedText: result.text,
            detectedLanguage: result.from.language.iso,
            confidence: result.from.language.didYouMean ? 0.8 : 0.95,
            originalText: text
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            originalText: text
        };
    }
}

async function translateFinancialResponse(responseData, targetLang) {
    try {
        const results = {};
        
        // Translate AI response
        if (responseData.ai_response) {
            const aiResult = await translateText(responseData.ai_response, targetLang);
            if (aiResult.success) {
                results.ai_response = aiResult.translatedText;
            } else {
                results.ai_response_error = aiResult.error;
            }
        }
        
        // Translate related questions
        if (responseData.related_questions && Array.isArray(responseData.related_questions)) {
            results.related_questions = [];
            for (const question of responseData.related_questions) {
                if (question && question.trim()) {
                    const qResult = await translateText(question, targetLang);
                    if (qResult.success) {
                        results.related_questions.push(qResult.translatedText);
                    } else {
                        results.related_questions.push(question); // Keep original if translation fails
                    }
                } else {
                    results.related_questions.push(question);
                }
            }
        }
        
        return {
            success: true,
            data: results
        };
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}

// Main execution
async function main() {
    try {
        const args = process.argv.slice(2);
        const command = args[0];
        
        if (command === 'translate') {
            const text = args[1];
            const targetLang = args[2];
            const sourceLang = args[3] || 'auto';
            
            const result = await translateText(text, targetLang, sourceLang);
            console.log(JSON.stringify(result));
            
        } else if (command === 'translate-response') {
            const responseDataStr = args[1];
            const targetLang = args[2];
            
            const responseData = JSON.parse(responseDataStr);
            const result = await translateFinancialResponse(responseData, targetLang);
            console.log(JSON.stringify(result));
            
        } else {
            console.log(JSON.stringify({
                success: false,
                error: 'Invalid command. Use "translate" or "translate-response"'
            }));
        }
    } catch (error) {
        console.log(JSON.stringify({
            success: false,
            error: error.message
        }));
    }
}

main();
'''
            
            # Write the script
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(node_script)
            
            # Install the required package if not already installed
            self._install_translate_package(services_dir)
            
            logger.info(f"✅ Node.js translation script created at: {script_path}")
            return script_path
            
        except Exception as e:
            logger.error(f"Error setting up Node.js script: {e}")
            return None
    
    def _install_translate_package(self, services_dir: str):
        """Install @vitalets/google-translate-api package"""
        try:
            # Check if package.json exists
            package_json_path = os.path.join(services_dir, 'package.json')
            if not os.path.exists(package_json_path):
                # Create package.json
                package_json = {
                    "name": "google-translate-service",
                    "version": "1.0.0",
                    "description": "Google Translate API service for financial responses",
                    "dependencies": {
                        "@vitalets/google-translate-api": "^9.2.0"
                    }
                }
                
                with open(package_json_path, 'w') as f:
                    json.dump(package_json, f, indent=2)
                
                logger.info("📦 Created package.json")
            
            # Install the package
            result = subprocess.run(['npm', 'install'], 
                                  cwd=services_dir, 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ @vitalets/google-translate-api installed successfully")
            else:
                logger.warning(f"⚠️ npm install warning: {result.stderr}")
                
        except Exception as e:
            logger.error(f"Error installing translate package: {e}")
    
    def is_service_available(self) -> bool:
        """Check if the translation service is available"""
        return self.service_available and self.node_script_path is not None
    
    def translate_text(self, text: str, target_lang: str, source_lang: str = 'auto') -> Dict[str, Any]:
        """
        Translate text using Google Translate API
        
        Args:
            text: Text to translate
            target_lang: Target language code (e.g., 'kn', 'ta', 'te', 'or')
            source_lang: Source language code (default: 'auto')
            
        Returns:
            Dictionary with translation result
        """
        if not self.is_service_available():
            return {
                'success': False,
                'error': 'Translation service not available'
            }
        
        try:
            # Run the Node.js script
            result = subprocess.run([
                'node', self.node_script_path, 'translate', 
                text, target_lang, source_lang
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                response = json.loads(result.stdout)
                return response
            else:
                logger.error(f"Translation script error: {result.stderr}")
                return {
                    'success': False,
                    'error': f'Translation script failed: {result.stderr}'
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Translation request timed out'
            }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f'Failed to parse translation response: {e}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Translation error: {str(e)}'
            }
    
    def translate_financial_response(self, response_data: Dict[str, Any], target_lang: str) -> Dict[str, Any]:
        """
        Translate entire financial response including AI response and related questions
        
        Args:
            response_data: The complete financial response data
            target_lang: Target language code
            
        Returns:
            Dictionary with translated response data
        """
        if not self.is_service_available():
            return {
                'success': False,
                'error': 'Translation service not available'
            }
        
        try:
            # Prepare data for translation
            translation_data = {
                'ai_response': response_data.get('ai_response', ''),
                'related_questions': response_data.get('related_questions', [])
            }
            
            # Convert to JSON string for command line
            data_json = json.dumps(translation_data)
            
            # Run the Node.js script
            result = subprocess.run([
                'node', self.node_script_path, 'translate-response',
                data_json, target_lang
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                translation_result = json.loads(result.stdout)
                
                if translation_result.get('success'):
                    # Update the original response data with translations
                    updated_response = response_data.copy()
                    translated_data = translation_result['data']
                    
                    if 'ai_response' in translated_data:
                        updated_response['ai_response'] = translated_data['ai_response']
                    
                    if 'related_questions' in translated_data:
                        updated_response['related_questions'] = translated_data['related_questions']
                    
                    # Add translation metadata
                    if 'query_metadata' not in updated_response:
                        updated_response['query_metadata'] = {}
                    
                    updated_response['query_metadata']['response_translated'] = True
                    updated_response['query_metadata']['translation_service'] = '@vitalets/google-translate-api'
                    updated_response['query_metadata']['target_language'] = target_lang
                    
                    return {
                        'success': True,
                        'data': updated_response
                    }
                else:
                    return translation_result
            else:
                logger.error(f"Translation script error: {result.stderr}")
                return {
                    'success': False,
                    'error': f'Translation script failed: {result.stderr}'
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Translation request timed out'
            }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f'Failed to parse translation response: {e}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Translation error: {str(e)}'
            }
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported language codes"""
        return [
            'en',  # English
            'kn',  # Kannada
            'ta',  # Tamil
            'te',  # Telugu
            'or',  # Oriya
            'hi',  # Hindi
            'bn',  # Bengali
            'gu',  # Gujarati
            'ml',  # Malayalam
            'mr',  # Marathi
            'pa',  # Punjabi
            'ur',  # Urdu
        ]

# Create a global instance
google_translate_service = GoogleTranslateAPIService()

# Export for easy importing
__all__ = ['google_translate_service', 'GoogleTranslateAPIService']