"use client";
import React, { FormEvent, useState, useEffect } from "react";
import displayImg from "@/public/images/sign-in.png";
import Image from "next/image";
import logoLight from "@/public/images/logo5.png";
import { baseUrl, uid } from "@/components/api/api";
import logoDark from "@/public/images/logo6.png";
import FormInput from "@/components/ui/FormInput";
import GradientBackground from "@/components/ui/GradientBackground";
import Footer from "@/components/Footer";
import { PiFacebookLogo, PiGoogleLogo, PiInstagramLogo } from "react-icons/pi";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTheme } from "next-themes";


function SignIn() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [currentLogo, setCurrentLogo] = useState(logoLight);

  const { resolvedTheme } = useTheme();
  const router = useRouter();

  // Update logo based on theme
  useEffect(() => {
    setCurrentLogo(resolvedTheme === 'dark' ? logoDark : logoLight);
  }, [resolvedTheme]);

  // Handle successful sign-in
  const handleSuccessfulSignIn = (userEmail: string) => {
    localStorage.setItem("userEmail", userEmail);
    router.push("/");
  };



  // Function to fetch PINE collection data
  const fetchPineCollectionData = async (userEmail: string) => {
    try {
      // Use filtered API endpoint to get ALL entries for this user (remove filtercount=1 to get all matches)
      const filterUrl = `${baseUrl}/retrievecollection?ColName=PINE&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(userEmail.trim())}`;

      const response = await fetch(filterUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "xxxid": "PINE"
        }
      });

      if (!response.ok) {
        console.error("Failed to fetch PINE collection data");
        // If API fails, use default values
        const defaultConfig = {
          api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
          index_name: "financialnews"
        };
        localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
        localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
        console.log("Using default PINE configuration due to API error");
        return;
      }

      const data = await response.json();
      console.log("PINE collection response:", data);

      // Check if user exists in PINE collection and extract all their indexes
      if (data.statusCode === 200 && data.source && data.source.length > 0) {
        // Parse each item in the source array (they are JSON strings)
        const pineData = data.source.map((item: string) => JSON.parse(item));

        // Extract all indexes and API keys for this user
        const userIndexes: string[] = [];
        const userApiKeys: string[] = [];

        pineData.forEach((item: any) => {
          if (item.client && item.client.trim().toLowerCase() === userEmail.trim().toLowerCase()) {
            if (item.index_name && !userIndexes.includes(item.index_name)) {
              userIndexes.push(item.index_name);
            }
            if (item.api_key && !userApiKeys.includes(item.api_key)) {
              userApiKeys.push(item.api_key);
            }
          }
        });

        if (userIndexes.length > 0 && userApiKeys.length > 0) {
          // User exists in PINE collection - store all their configurations
          localStorage.setItem("pinecone_api_key", userApiKeys[0]); // Store first API key as default
          localStorage.setItem("pinecone_index_name", userIndexes[0]); // Store first index as default
          localStorage.setItem('pineconeApiKeys', JSON.stringify(userApiKeys)); // Store all API keys
          localStorage.setItem('userPineconeIndexes', JSON.stringify(userIndexes)); // Store all indexes
          console.log("Found existing PINE data for user:", userEmail);
          console.log("User indexes:", userIndexes);
          console.log("User API keys count:", userApiKeys.length);
        } else {
          // User doesn't exist in PINE collection - use default values without auto-creation
          console.log("No PINE data found for user:", userEmail, "- using default configuration without auto-creation");

          const defaultConfig = {
            api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
            index_name: "financialnews"
          };
          localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
          localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
          console.log("Using default PINE configuration for user:", userEmail);
        }
      } else {
        // No PINE data found - use default values without auto-creation
        console.log("No PINE data found for user:", userEmail, "- using default configuration without auto-creation");

        const defaultConfig = {
          api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
          index_name: "financialnews"
        };
        localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
        localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
        console.log("Using default PINE configuration for user:", userEmail);
      }
    } catch (error) {
      console.error("Error fetching PINE collection data:", error);
      // Fallback to default values on error
      const defaultConfig = {
        api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
        index_name: "financialnews"
      };
      localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
      localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
      console.log("Using fallback default PINE configuration due to error");
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!email || !password) {
      setIsError(true);
      setErrorMessage("Please enter both email and password.");
      return;
    }

    // // Special case for admin login - redirect to file upload page
    // if (email.trim() === "<EMAIL>" && password.trim() === "admin") {
    //   setIsError(false);
    //   setIsSuccess(true);
    //   setSuccessMessage("Login successful! Redirecting to File Upload...");

    //   setTimeout(() => {
    //     setIsSuccess(false);
    //   }, 500);

    //   setTimeout(() => {
    //     setEmail("");
    //     setPassword("");
    //     router.push("/file-upload-standalone");
    //   }, 500);

    //   return;
    // }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setIsError(true);
      setErrorMessage("Invalid email address.");
      return;
    }

    const apiUrl = `${baseUrl}/login`;

    const requestData = {
      username: email.trim(),
      password: password.trim(),
    };

    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "xxxid": uid,
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      if (response.ok && result?.statusCode === 200) {
        setIsError(false);
        setIsSuccess(true);
        setSuccessMessage("Login successful! Redirecting...");

        // Parse the source field safely
        try {
          const userData = JSON.parse(result.source);
          sessionStorage.setItem("resultUser", JSON.stringify(userData));
        } catch (parseError) {
          console.error("Failed to parse user data:", parseError);
        }

        // Store user email in localStorage
        localStorage.setItem("user_email", email.trim());

        // Set API environment based on deployment
        // For development, use the local suggest.py server
        // For production, use the remote server
        const isDevelopment = window.location.hostname === 'localhost';
        localStorage.setItem("use_dev_environment", isDevelopment ? 'true' : 'false');
        console.log(`Setting API environment to ${isDevelopment ? 'development' : 'production'}`);

        // Fetch PINE collection data for the logged-in user (without auto-creation)
        await fetchPineCollectionData(email.trim());

        // Check if user has Admin role
        try {
          const userData = JSON.parse(result.source);
          if (userData.role === "Admin") {
            setTimeout(() => {
              setIsSuccess(false);
              setEmail("");
              setPassword("");
              router.push("/file-upload-standalone");
            }, 500);
            return;
          }
        } catch (error) {
          console.error("Error checking user role:", error);
        }

        setTimeout(() => {
          setIsSuccess(false);
        }, 500);

        setTimeout(() => {
          setEmail("");
          setPassword("");

          // Check if there's a redirect URL stored (for shared chat links)
          const redirectUrl = localStorage.getItem('redirectAfterLogin');
          if (redirectUrl) {
            localStorage.removeItem('redirectAfterLogin');
            window.location.href = redirectUrl;
          } else {
            router.push("/new-chat");
          }
        }, 500);
      } else {
        setIsError(true);
        setIsSuccess(false);

        // 🟠 Custom error message override logic
        const rawError = result.errorMsg || result.message || "Invalid email or password";
        const mappedError =
          rawError === "Invalid username." ? "Invalid email address." : rawError;

        setErrorMessage(mappedError);

        setTimeout(() => {
          setEmail("");
          setPassword("");
          setIsError(false);
        }, 500);
      }
    } catch (error) {
      setIsError(true);
      setErrorMessage("Something went wrong. Please try again.");
      console.error("Login error:", error);

      setTimeout(() => {
        setIsError(false);
      }, 500);
    }
  };

  return (
    <div className="flex justify-between text-n500 dark:text-n30 xxl:gap-20 max-xxl:container xxl:h-dvh max-xxl:justify-center relative">
      {/* <GradientBackground /> */}
      <div className="py-6 xxl:ml-[calc((100%-1296px)/2)] flex-1 flex flex-col justify-between items-start max-xxl:max-w-[600px]">
        <div className="flex justify-start items-center gap-1.5">
          <Image src={currentLogo} alt="QuerryOne Logo" />
        </div>

        <div className="w-full pt-4">
          <p className="text-2xl font-semibold">Welcome Back!</p>
          <p className="text-sm pt-4">Sign in to your account and join us</p>

          <form onSubmit={handleSubmit} className="pt-10 grid grid-cols-2 gap-4 sm:gap-6">
            <div className="col-span-2">
              <FormInput
                title="Enter Your Email"
                placeholder="Your email here"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="col-span-2">
              <FormInput
                title="Password"
                placeholder="*******"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <Link href="/forgot-password" className="text-end block pt-4 text-primaryColor text-sm">
                Forgot password?
              </Link>
            </div>

            <p className="col-span-2 text-sm pt-2">
              Don&apos;t have an account?{" "}
              <Link href="/sign-up" className="text-errorColor font-semibold">
                Sign Up
              </Link>
            </p>

            <div className="col-span-2">
              <button
                type="submit"
                className="text-sm font-medium text-white bg-primaryColor text-center py-3 px-6 rounded-full block w-full"
              >
                Sign In
              </button>
              {isError && (
                <p className="text-errorColor text-sm pt-2">{errorMessage}</p>
              )}
              {isSuccess && (
                <p className="text-green-500 text-sm pt-2">{successMessage}</p>
              )}
            </div>
          </form>
        </div>

        <div className="flex justify-center items-center w-full pt-4">
          <Footer />
        </div>
      </div>
      <div className="w-1/2 max-xxl:hidden max-h-dvh overflow-hidden">
        <Image src={displayImg} alt="Sign in illustration" className="w-full object-cover" />
      </div>
    </div>
  );
}

export default SignIn;
