import React, { useEffect, useState, useRef } from "react";
import defaultImg from "@/public/images/logodefault.png"; // ✅ Your custom default image
import Image from "next/image";
import { baseUrl, uid } from "@/components/api/api";
import { toast, Toaster } from "react-hot-toast";
import { PiCloudArrowUp } from "react-icons/pi";

function EditProfileModal() {
  const [profileData, setProfileData] = useState({
    name: "N/A",
    mobile: "N/A",
    email: "N/A",
  });
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const storedData = sessionStorage.getItem("resultUser");
    if (storedData) {
      try {
        const user = JSON.parse(storedData);
        setProfileData({
          name: user.name || "N/A",
          mobile: user.mobileno || "N/A",
          email: user.email || "N/A",
        });

        fetchUpdatedProfileImage(user.name, user.mobileno).then((imgUrl) => {
          if (imgUrl) {
            setImagePreview(imgUrl);
          } else {
            setImagePreview(null); // fallback to default
          }
        });
      } catch (error) {
        console.error("Failed to parse session data:", error);
      }
    }
  }, []);

  const fetchUpdatedProfileImage = async (
    username: string,
    mobileno: string
  ): Promise<string | null> => {
    if (!username || !mobileno) return null;

    const apiUrl = `${baseUrl}/eRetrieve?filtercount=2&f1_field=phonenumber_S&f1_op=eq&f1_value=${mobileno}&f2_field=name_S&f2_op=eq&f2_value=${username}`;

    try {
      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          xxxid: uid,
        },
      });

      if (!response.ok) throw new Error("Failed to fetch profile image");

      const contentType = response.headers.get("content-type") || "";

      if (contentType.includes("image")) {
        const blob = await response.blob();
        return URL.createObjectURL(blob);
      } else {
        const data = await response.json();
        return data.logoUrl || null;
      }
    } catch (error) {
      console.error("Error fetching updated profile image:", error);
      return null;
    }
  };

  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const maxSizeKB = 998;
    const maxSizeBytes = maxSizeKB * 1024;

    if (file.size > maxSizeBytes) {
      toast.error(`Max image size is ${maxSizeKB}KB. Please choose a smaller image.`);
      if (fileInputRef.current) fileInputRef.current.value = ""; // reset input
      return;
    }


    setImagePreview(URL.createObjectURL(file)); // Show temp preview

    const storedData = sessionStorage.getItem("resultUser");
    if (!storedData) return;

    let userData;
    try {
      userData = JSON.parse(storedData);
    } catch {
      return;
    }

    const formData = new FormData();
    formData.append("file", file, file.name);
    formData.append(
      "data",
      JSON.stringify({
        name: userData.name || "",
        field_2: "field_2_value",
        field_3: true,
      })
    );
    formData.append("phoneNumber", userData.mobileno || "");

    setLoading(true);

    try {
      const response = await fetch(`${baseUrl}/eUpload`, {
        method: "POST",
        headers: {
          xxxid: uid,
        },
        body: formData,
      });

      const data = await response.json();

      if (response.ok) {
        if (fileInputRef.current) fileInputRef.current.value = "";

        const updatedImg = await fetchUpdatedProfileImage(
          userData.name,
          userData.mobileno
        );

        if (updatedImg) {
          setImagePreview(updatedImg);
          toast.success("Image uploaded successfully!");
          window.dispatchEvent(
            new CustomEvent("profileImageUpdated", {
              detail: updatedImg,
            })
          );
        } else {
          setImagePreview(null); // fallback if image is missing
        }
      } else {
        console.error("Upload failed:", data);
        alert("Failed to upload image.");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      alert("An error occurred during upload.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto p-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
      <Toaster position="top-right" reverseOrder={false} />

      {/* Profile Picture Upload */}
      <div className="flex items-center gap-6 mb-8">
        <div className="relative border border-primaryColor/30 p-1.5 rounded-full">
          <Image
            src={imagePreview || defaultImg}
            alt="Profile"
            className="size-20 rounded-full"
            width={80}
            height={80}
            priority
            onError={() => setImagePreview(null)} // ✅ fallback if image fails to load
          />
          <label
            htmlFor="photo-upload"
            className={`bg-white dark:bg-n0 flex justify-center items-center absolute bottom-0 right-0 rounded-full p-1 cursor-pointer shadow-md ${loading ? "opacity-50 pointer-events-none" : ""
              }`}
            title={loading ? "Uploading..." : "Upload new profile image"}
          >
            <PiCloudArrowUp />
            <input
              type="file"
              className="hidden"
              id="photo-upload"
              accept="image/*"
              onChange={handleFileChange}
              ref={fileInputRef}
              disabled={loading}
            />
          </label>
        </div>
        <div>
          <p className="text-base font-semibold text-gray-800 dark:text-white">
            Profile Picture
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-300">
            Choose an image that represents you
          </p>
        </div>
      </div>

      {/* Profile Data Display */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 text-sm">
        <div>
          <p className="text-gray-500 font-medium mb-1">Full Name</p>
          <p className="text-gray-900 dark:text-white font-semibold">
            {profileData.name}
          </p>
        </div>
        <div>
          <p className="text-gray-500 font-medium mb-1">Mobile Number</p>
          <p className="text-gray-900 dark:text-white font-semibold">
            {profileData.mobile}
          </p>
        </div>
        <div className="sm:col-span-2">
          <p className="text-gray-500 font-medium mb-1">Email</p>
          <p className="text-gray-900 dark:text-white font-semibold">
            {profileData.email}
          </p>
        </div>
      </div>
    </div>
  );
}

export default EditProfileModal;
