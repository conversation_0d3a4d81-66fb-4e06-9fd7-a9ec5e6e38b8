import React from 'react';
import Image from 'next/image';
import fav from '@/public/images/favicon.png';
import HexagonLogo from './HexagonLogo';

interface SimpleAIQuillLogoProps {
  darkMode?: boolean;
  useHexagonSvg?: boolean;
}

const SimpleAIQuillLogo: React.FC<SimpleAIQuillLogoProps> = ({
  darkMode = true,
  useHexagonSvg = false
}) => {
  return (
    <div className="flex justify-start items-center gap-1.5">
      {useHexagonSvg ? (
        <HexagonLogo size={32} color={darkMode ? '#4d6bfe' : '#3d5afe'} />
      ) : (
        <Image src={fav} alt="AIQuill Logo" width={32} height={32} />
      )}
      <span className={`text-2xl font-semibold ${darkMode ? 'text-n30' : 'text-n700'}`}>
        QueryOne
      </span>
    </div>
  );
};

export default SimpleAIQuillLogo;
