// components/chatComponents/TypingAnimation.tsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import { PiArrowRight } from 'react-icons/pi';

interface TypingAnimationProps {
  text: string;
  speed?: number; // milliseconds per character
  speedVariation?: number; // random variation in speed to make typing look more natural
  startDelay?: number; // delay before starting animation
  onComplete?: () => void;
  onProgress?: (progress: number) => void; // callback with progress (0-1)
  className?: string;
  showSkipButton?: boolean;
  skipButtonText?: string;
  cursorChar?: string; // character to use as cursor
  showCursor?: boolean;
  markdownOptions?: any; // Additional options for ReactMarkdown
}

const TypingAnimation: React.FC<TypingAnimationProps> = ({
  text,
  speed = 20, // Default speed: 20ms per character
  speedVariation = 15, // +/- 15ms variation for more natural feel
  startDelay = 200, // 200ms delay before starting
  onComplete,
  onProgress,
  className = '',
  showSkipButton = true,
  skipButtonText = 'Skip to full response',
  cursorChar = '▌',
  showCursor = true,
  markdownOptions = {},
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const [showingCursor, setShowingCursor] = useState(true);
  const [animationProgress, setAnimationProgress] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const cursorTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const indexRef = useRef(0);
  const animationStartedRef = useRef(false);
  const animationActive = useRef(true);
  const textChunksRef = useRef<string[]>([]);

  // Function to clear all timeouts
  const clearAllTimeouts = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (cursorTimeoutRef.current) {
      clearTimeout(cursorTimeoutRef.current);
      cursorTimeoutRef.current = null;
    }
  }, []);

  // Blinking cursor effect
  useEffect(() => {
    if (!showCursor || isComplete) return;

    const blinkCursor = () => {
      setShowingCursor(prev => !prev);
      cursorTimeoutRef.current = setTimeout(blinkCursor, 500);
    };

    cursorTimeoutRef.current = setTimeout(blinkCursor, 500);

    return () => {
      if (cursorTimeoutRef.current) {
        clearTimeout(cursorTimeoutRef.current);
      }
    };
  }, [showCursor, isComplete]);

  // Clear timeouts on unmount
  useEffect(() => {
    return () => {
      clearAllTimeouts();
      animationActive.current = false;
    };
  }, [clearAllTimeouts]);

  // Prepare text chunks for more efficient rendering
  useEffect(() => {
    if (!text) return;

    // Split text into chunks for more efficient rendering
    // This helps with performance for long texts
    const chunkSize = 5; // Process 5 characters at a time for efficiency
    const chunks: string[] = [];

    for (let i = 0; i < text.length; i += chunkSize) {
      chunks.push(text.substring(i, Math.min(i + chunkSize, text.length)));
    }

    textChunksRef.current = chunks;
  }, [text]);

  // Reset and start animation when text changes
  useEffect(() => {
    if (!text || animationStartedRef.current) return;

    console.log("Starting typing animation for text of length:", text.length);
    animationStartedRef.current = true;
    indexRef.current = 0;
    setDisplayedText('');
    setIsComplete(false);
    setAnimationProgress(0);

    // Start the typing animation after delay
    const startAnimation = () => {
      if (!animationActive.current) return;

      let currentChunkIndex = 0;
      let currentPositionInChunk = 0;
      let totalCharactersTyped = 0;

      const typeNextChunk = () => {
        if (!animationActive.current) return;

        if (currentChunkIndex < textChunksRef.current.length) {
          const currentChunk = textChunksRef.current[currentChunkIndex];

          // Process the next character in the current chunk
          if (currentPositionInChunk < currentChunk.length) {
            // Get the next character
            const nextChar = currentChunk.charAt(currentPositionInChunk);
            totalCharactersTyped++;

            // Update displayed text
            setDisplayedText(text.substring(0, totalCharactersTyped));
            currentPositionInChunk++;

            // Calculate and report progress
            const newProgress = totalCharactersTyped / text.length;
            setAnimationProgress(newProgress);
            if (onProgress) onProgress(newProgress);

            // Vary the speed based on character type
            let charSpeed = speed;

            // Slow down for punctuation and paragraph breaks
            if (['.', '!', '?'].includes(nextChar)) {
              charSpeed = speed * 4; // Longer pause for sentence endings
            } else if ([',', ';', ':'].includes(nextChar)) {
              charSpeed = speed * 2.5; // Medium pause for mid-sentence breaks
            } else if (nextChar === '\n') {
              charSpeed = speed * 3; // Pause for line breaks
            } else {
              // Random variation for natural feel
              charSpeed = speed + (Math.random() * speedVariation * 2) - speedVariation;
            }

            // Schedule the next character
            timeoutRef.current = setTimeout(typeNextChunk, charSpeed);
          } else {
            // Move to the next chunk
            currentChunkIndex++;
            currentPositionInChunk = 0;
            timeoutRef.current = setTimeout(typeNextChunk, 0); // No delay between chunks
          }
        } else {
          // Animation complete
          console.log("Typing animation completed");
          setIsComplete(true);
          setAnimationProgress(1);
          if (onProgress) onProgress(1);
          if (onComplete) {
            onComplete();
          }
        }
      };

      // Start typing
      timeoutRef.current = setTimeout(typeNextChunk, 10);
    };

    // Delay the start of animation
    timeoutRef.current = setTimeout(startAnimation, startDelay);

    return () => {
      clearAllTimeouts();
    };
  }, [text, speed, speedVariation, startDelay, onComplete, onProgress, clearAllTimeouts]);

  // Skip animation and show full text
  const handleSkip = useCallback(() => {
    console.log("Skipping typing animation");
    clearAllTimeouts();
    setDisplayedText(text);
    setIsComplete(true);
    setAnimationProgress(1);
    if (onProgress) onProgress(1);
    if (onComplete) {
      onComplete();
    }
  }, [text, onComplete, onProgress, clearAllTimeouts]);

  // Format the displayed text with cursor
  const formattedText = isComplete || !showCursor
    ? displayedText
    : `${displayedText}${showingCursor ? cursorChar : ''}`;

  return (
    <div className={`typing-animation ${className}`}>
      {/* Subtle progress indicator */}
      {!isComplete && text.length > 100 && (
        <div className="w-full h-0.5 bg-gray-100 rounded-full mb-2 overflow-hidden">
          <div
            className="h-full bg-primaryColor/30 transition-all duration-300 ease-out"
            style={{ width: `${animationProgress * 100}%` }}
          />
        </div>
      )}

      <div className="whitespace-pre-wrap">
        <ReactMarkdown {...markdownOptions}>{formattedText}</ReactMarkdown>
      </div>

      {showSkipButton && !isComplete && text.length > 30 && (
        <button
          onClick={handleSkip}
          className="text-xs text-primaryColor mt-2 hover:underline flex items-center gap-1 transition-opacity duration-300 opacity-70 hover:opacity-100"
          aria-label="Skip typing animation"
        >
          {skipButtonText} <PiArrowRight className="inline-block ml-1" />
        </button>
      )}
    </div>
  );
};

export default TypingAnimation;
