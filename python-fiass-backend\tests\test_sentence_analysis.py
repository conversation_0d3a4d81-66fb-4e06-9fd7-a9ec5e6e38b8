#!/usr/bin/env python3
"""
Test script to verify sentence analysis is populated with correct data from FAISS index.
"""

import requests
import json

def test_sentence_analysis():
    """Test that sentence analysis gets populated with FAISS index data."""
    
    url = "http://127.0.0.1:5010/financial_query"
    payload = {
        "query": "What are Aleutian Clouds?",
        "client_email": "<EMAIL>",
        "index_name": "new",  # Use the 'new' index which has the Aleutian Clouds data
        "language": "English"
    }
    
    print("🧪 Testing sentence analysis population...")
    print(f"Query: {payload['query']}")
    print(f"Index: {payload['index_name']}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.ok:
            data = response.json()
            
            # Check sentence analysis
            sentence_analysis = data.get('sentence_analysis', [])
            print(f"\n📊 Sentence Analysis Results:")
            print(f"   - Total sentences analyzed: {len(sentence_analysis)}")
            
            if sentence_analysis:
                print(f"\n🔍 Detailed Analysis of First 3 Sentences:")
                for i, sentence_data in enumerate(sentence_analysis[:3]):
                    print(f"\n   Sentence {i+1}:")
                    print(f"      Text: {sentence_data.get('sentence', 'N/A')[:80]}...")
                    print(f"      Source Title: {sentence_data.get('source_title', 'N/A')}")
                    print(f"      Source Type: {sentence_data.get('source_type', 'N/A')}")
                    print(f"      Summary: {sentence_data.get('summary', 'N/A')}")
                    print(f"      URL: {sentence_data.get('url', 'N/A')}")
                    
                    # Check if we got actual data from FAISS index
                    if sentence_data.get('source_title') != 'Unknown':
                        print(f"      ✅ SUCCESS: Got actual source_title from index!")
                    else:
                        print(f"      ❌ ISSUE: Still showing 'Unknown' for source_title")
                        
                    if sentence_data.get('source_type') != 'unknown':
                        print(f"      ✅ SUCCESS: Got actual source_type from index!")
                    else:
                        print(f"      ❌ ISSUE: Still showing 'unknown' for source_type")
            
            # Also check retrieved documents for comparison
            retrieved_docs = data.get('retrieved_documents', [])
            print(f"\n📄 Retrieved Documents for Comparison:")
            print(f"   - Total documents: {len(retrieved_docs)}")
            
            if retrieved_docs:
                first_doc = retrieved_docs[0]
                print(f"   - First document text: {first_doc.get('text', 'N/A')[:100]}...")
                
                # Try to extract file_name and vector_id from the document text
                doc_text = first_doc.get('text', '')
                if 'file_name:' in doc_text:
                    file_name_start = doc_text.find('file_name:') + len('file_name:')
                    file_name_end = doc_text.find(' ', file_name_start)
                    if file_name_end == -1:
                        file_name_end = doc_text.find('\n', file_name_start)
                    file_name = doc_text[file_name_start:file_name_end].strip()
                    print(f"   - Extracted file_name: {file_name}")
                
            return True
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Sentence Analysis Data Population")
    print("=" * 60)
    
    success = test_sentence_analysis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test completed successfully!")
    else:
        print("❌ Test failed!")