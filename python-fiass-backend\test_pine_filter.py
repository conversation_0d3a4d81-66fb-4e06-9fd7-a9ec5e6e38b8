#!/usr/bin/env python3
"""
Test script to verify pine_filter_service import and functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pine_filter_import():
    """Test importing and using pine_filter_service"""
    
    print("🧪 Testing pine_filter_service import...")
    
    try:
        from pine_filter_service import PineFilterService
        print("✅ Successfully imported PineFilterService")
        
        # Test email validation
        is_valid, message = PineFilterService.validate_email("<EMAIL>")
        print(f"✅ Email validation: {is_valid} - {message}")
        
        # Test user access validation
        has_access, access_message = PineFilterService.validate_user_access("<EMAIL>", "dwindele")
        print(f"✅ User access validation: {has_access} - {access_message}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_pine_filter_import()
    if success:
        print("\n🎉 Pine filter service test PASSED!")
    else:
        print("\n💥 Pine filter service test FAILED!")