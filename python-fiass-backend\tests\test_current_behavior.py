#!/usr/bin/env python3
"""
Test current behavior of capital letter preservation
"""

import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.translation_service import TranslationService

def test_current_behavior():
    """Test the current behavior with the exact example"""
    
    print("🔍 TESTING CURRENT BEHAVIOR")
    print("=" * 50)
    
    # Initialize translation service
    translation_service = TranslationService()
    
    # Test the exact example mentioned
    test_text = "The iPhone uses IOS operating system"
    target_lang = "ta"
    source_lang = "en"
    
    print(f"Input: '{test_text}'")
    print(f"Target Language: {target_lang}")
    print()
    
    # Perform translation
    result = translation_service.translate_text(test_text, target_lang, source_lang)
    
    print("RESULT:")
    print(f"Translated Text: '{result['translated_text']}'")
    print(f"Source Language: {result['source_language']}")
    print(f"Target Language: {result['target_language']}")
    print(f"Translation Provider: {result['translation_provider']}")
    print(f"Cached: {result['cached']}")
    print()
    
    # Check if IOS is preserved
    if "IOS" in result['translated_text']:
        print("✅ SUCCESS: 'IOS' is preserved in the translation")
    else:
        print("❌ ISSUE: 'IOS' is not preserved in the translation")
    
    # Check if other words are translated
    if result['translated_text'] != test_text:
        print("✅ SUCCESS: Other words are translated")
    else:
        print("❌ ISSUE: No translation occurred")
    
    print()
    print("ANALYSIS:")
    words = test_text.split()
    translated_words = result['translated_text'].split()
    
    for i, word in enumerate(words):
        if word == "IOS":
            if "IOS" in result['translated_text']:
                print(f"- '{word}' → preserved (continuous capitals)")
            else:
                print(f"- '{word}' → NOT preserved (ERROR)")
        else:
            print(f"- '{word}' → translated")

if __name__ == "__main__":
    test_current_behavior()