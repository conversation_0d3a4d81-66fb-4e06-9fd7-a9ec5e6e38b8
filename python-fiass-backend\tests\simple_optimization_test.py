#!/usr/bin/env python3
"""
Simple test to verify language optimization is working
"""

import requests
import json
import time

def test_optimization():
    """Test the optimization with a simple Tamil query"""
    
    url = "http://localhost:5010/financial_query"
    
    # Test case: Tamil query with Tamil index (should be optimized)
    payload = {
        "query": "பணம் பற்றி சொல்லுங்கள்",
        "index_name": "tamil",
        "language": "Tamil"
    }
    
    print("🧪 Testing Tamil Query with Tamil Index")
    print(f"Query: {payload['query']}")
    print(f"Index: {payload['index_name']}")
    print("Expected: Optimization should be applied")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"Response Time: {response_time:.3f}s")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Check optimization flags
            optimization_applied = data.get("optimized_direct_processing", False)
            early_optimization = data.get("early_optimization_detected", False)
            optimization_type = data.get("optimization_type", "none")
            performance_optimization = data.get("performance_optimization_applied", False)
            query_translated = data.get("query_translated", False)
            data_language = data.get("data_language", "Unknown")
            
            print(f"\n📊 OPTIMIZATION RESULTS:")
            print(f"✅ Optimization Applied: {optimization_applied}")
            print(f"🚀 Early Optimization: {early_optimization}")
            print(f"📈 Performance Optimization: {performance_optimization}")
            print(f"🔧 Optimization Type: {optimization_type}")
            print(f"🔄 Query Translated: {query_translated}")
            print(f"📊 Data Language: {data_language}")
            
            # Check cross-language processing
            cross_lang_info = data.get("cross_language_processing", {})
            cross_lang_applied = cross_lang_info.get("applied", False)
            cross_lang_reason = cross_lang_info.get("reason", "Not specified")
            
            print(f"🌐 Cross-language Applied: {cross_lang_applied}")
            print(f"📝 Cross-language Reason: {cross_lang_reason}")
            
            # Determine success
            if optimization_applied and not query_translated:
                print(f"\n✅ SUCCESS: Optimization is working correctly!")
                print(f"🚀 Benefits: No translation overhead, faster response")
            else:
                print(f"\n❌ ISSUE: Optimization may not be working as expected")
                if query_translated:
                    print(f"   - Query was translated (should be skipped)")
                if not optimization_applied:
                    print(f"   - Optimization was not applied")
            
            # Show AI response snippet
            ai_response = data.get("ai_response", "")
            if ai_response:
                print(f"\n📝 AI Response (first 100 chars): {ai_response[:100]}...")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {str(e)}")

def test_cross_language():
    """Test cross-language scenario (should NOT be optimized)"""
    
    url = "http://localhost:5010/financial_query"
    
    # Test case: English query with Tamil index (should NOT be optimized)
    payload = {
        "query": "Tell me about money",
        "index_name": "tamil",
        "language": "English"
    }
    
    print("\n" + "="*60)
    print("🧪 Testing English Query with Tamil Index (Cross-language)")
    print(f"Query: {payload['query']}")
    print(f"Index: {payload['index_name']}")
    print("Expected: NO optimization (cross-language scenario)")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"Response Time: {response_time:.3f}s")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Check optimization flags
            optimization_applied = data.get("optimized_direct_processing", False)
            early_optimization = data.get("early_optimization_detected", False)
            optimization_type = data.get("optimization_type", "none")
            
            print(f"\n📊 CROSS-LANGUAGE RESULTS:")
            print(f"❌ Optimization Applied: {optimization_applied} (should be False)")
            print(f"❌ Early Optimization: {early_optimization} (should be False)")
            print(f"🔧 Optimization Type: {optimization_type} (should be 'none')")
            
            # This should NOT be optimized
            if not optimization_applied and optimization_type == "none":
                print(f"\n✅ SUCCESS: Cross-language correctly NOT optimized!")
            else:
                print(f"\n❌ ISSUE: Cross-language scenario was incorrectly optimized")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Simple Language Optimization Test")
    print("="*60)
    
    # Test optimization scenario
    test_optimization()
    
    # Test cross-language scenario
    test_cross_language()
    
    print("\n✅ Test completed!")