"""
Translation Service for FAISS Backend
Provides language detection and translation capabilities for bot responses.
"""

import os
import re
import json
import time
import hashlib
import requests
import unicodedata
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Import Deep Translator
try:
    from deep_translator import GoogleTranslator
    TRANSLATOR_AVAILABLE = True
    print("✅ Deep Translator library loaded successfully")
except ImportError:
    TRANSLATOR_AVAILABLE = False
    print("⚠️ Deep Translator library not available - using fallback translations")

class TranslationService:
    """
    Service for handling language detection and translation of bot responses.
    Supports multiple translation providers with caching for performance.
    """
    
    def __init__(self):
        self.cache = {}  # In-memory cache for translations
        self.cache_expiry = 3600  # 1 hour cache expiry
        self.cache_lock = threading.Lock()  # Thread-safe cache access
        self._initialize_common_translations()  # Pre-populate cache with common translations
        self.performance_stats = {
            'total_translations': 0,
            'cache_hits': 0,
            'avg_translation_time': 0.0,
            'batch_translations': 0,
            'concurrent_translations': 0
        }
        self.supported_languages = {
            'en': 'English',
            'ta': 'Tamil',
            'te': 'Telugu',
            'kn': 'Kannada',
            'hi': 'Hindi',
            'or': 'Oriya',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic'
        }

        # Configuration for timeouts and retries
        self.api_timeout = 20  # Increased timeout for API calls
        self.max_retries = 3
        self.retry_delay = 1.0  # Delay between retries in seconds

        # Deep Translator doesn't need initialization - it's used directly
        self.translator_available = TRANSLATOR_AVAILABLE
        if self.translator_available:
            print("✅ Deep Translator ready for use")
        else:
            print("⚠️ Deep Translator not available")

        # Initialize error tracking
        self.error_counts = {
            'deep_translator': 0,
            'mymemory': 0,
            'libretranslate': 0,
            'total_requests': 0,
            'successful_requests': 0
        }
        
    def detect_language(self, text: str) -> str:
        """
        Detect the language of the input text.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Language code (e.g., 'en', 'ta', 'hi')
        """
        if not text or not text.strip():
            return 'en'  # Default to English
            
        # Tamil detection using Unicode ranges
        if self._is_tamil_text(text):
            return 'ta'
            
        # Telugu detection using Unicode ranges
        if self._is_telugu_text(text):
            return 'te'
            
        # Kannada detection using Unicode ranges
        if self._is_kannada_text(text):
            return 'kn'
            
        # Hindi detection using Unicode ranges
        if self._is_hindi_text(text):
            return 'hi'
            
        # Arabic detection
        if self._is_arabic_text(text):
            return 'ar'
            
        # Chinese detection
        if self._is_chinese_text(text):
            return 'zh'
            
        # Oriya detection
        if self._is_oriya_text(text):
            return 'or'
            
        # Default to English for other cases
        return 'en'
    
    def _is_tamil_text(self, text: str) -> bool:
        """Check if text contains Tamil characters"""
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))
    
    def _is_telugu_text(self, text: str) -> bool:
        """Check if text contains Telugu characters"""
        telugu_pattern = re.compile(r'[\u0C00-\u0C7F]')
        return bool(telugu_pattern.search(text))
    
    def _is_kannada_text(self, text: str) -> bool:
        """Check if text contains Kannada characters"""
        kannada_pattern = re.compile(r'[\u0C80-\u0CFF]')
        return bool(kannada_pattern.search(text))
    
    def _is_hindi_text(self, text: str) -> bool:
        """Check if text contains Hindi/Devanagari characters"""
        hindi_pattern = re.compile(r'[\u0900-\u097F]')
        return bool(hindi_pattern.search(text))
    
    def _is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        arabic_pattern = re.compile(r'[\u0600-\u06FF]')
        return bool(arabic_pattern.search(text))
    
    def _is_chinese_text(self, text: str) -> bool:
        """Check if text contains Chinese characters"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))
    
    def _is_oriya_text(self, text: str) -> bool:
        """Check if text contains Oriya characters"""
        oriya_pattern = re.compile(r'[\u0B00-\u0B7F]')
        return bool(oriya_pattern.search(text))
    
    def _generate_cache_key(self, text: str, source_lang: str, target_lang: str) -> str:
        """Generate a cache key for translation"""
        content = f"{text}|{source_lang}|{target_lang}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Check if cache entry is still valid"""
        if 'timestamp' not in cache_entry:
            return False
        
        cache_time = datetime.fromisoformat(cache_entry['timestamp'])
        expiry_time = cache_time + timedelta(seconds=self.cache_expiry)
        return datetime.now() < expiry_time
    
    def _has_continuous_capital_letters(self, text: str) -> bool:
        """
        Check if text contains words with continuous capital letters (like API, REST, JSON).
        
        Args:
            text: Input text to check
            
        Returns:
            bool: True if text contains words with 2 or more continuous capital letters
        """
        import re
        # Match words that have 2 or more continuous capital letters
        continuous_capital_words = re.findall(r'\b[A-Z]{2,}\b', text)
        return len(continuous_capital_words) > 0
    
    def _should_skip_translation(self, text: str, source_lang: str, target_lang: str) -> bool:
        """
        Determine if translation should be skipped for text containing continuous capital letters.
        
        Args:
            text: Input text
            source_lang: Source language
            target_lang: Target language
            
        Returns:
            bool: True if translation should be skipped
        """
        # Skip translation if source and target languages are the same
        if source_lang == target_lang:
            return True
            
        return False
    
    def _preserve_continuous_capital_words(self, text: str) -> tuple:
        """
        DEPRECATED: This method is disabled to prevent CAPWORD token corruption.
        Capital word preservation is now handled by the new Google Translate API service.

        Args:
            text: Input text

        Returns:
            tuple: (original_text_unchanged, empty_list)
        """
        print("⚠️ DEPRECATED: _preserve_continuous_capital_words disabled - using new translation service")
        # Return original text unchanged and empty list to disable CAPWORD tokens
        return text, []
    
    def _restore_continuous_capital_words(self, text: str, continuous_capital_words: list) -> str:
        """
        DEPRECATED: This method is disabled to prevent CAPWORD token corruption.
        Capital word preservation is now handled by the new Google Translate API service.

        Args:
            text: Text with placeholders
            continuous_capital_words: List of (word, placeholder) tuples

        Returns:
            Original text unchanged
        """
        print("⚠️ DEPRECATED: _restore_continuous_capital_words disabled - using new translation service")
        # Return original text unchanged to disable CAPWORD token restoration
        return text
        import re
        restored_text = text
        
        print(f"🔧 Restoring {len(continuous_capital_words)} capital words from placeholders")
        print(f"📝 Text before restoration: {restored_text[:200]}...")
        
        for word, placeholder in continuous_capital_words:
            original_text = restored_text
            print(f"🔍 Looking for placeholder '{placeholder}' to restore '{word}'")
            
            # Try exact match first
            if placeholder in restored_text:
                restored_text = restored_text.replace(placeholder, word)
                print(f"✅ Exact match restored: {placeholder} -> {word}")
            else:
                # Try pattern match for partially modified placeholders
                # Handle cases where translation might add spaces or modify the placeholder
                patterns_to_try = [
                    # Try with spaces around the placeholder
                    placeholder.replace('CAPWORD', r'CAPWORD\s*'),
                    # Try with partial matches
                    re.escape(placeholder).replace(r'CAPWORD', r'CAPWORD\s*'),
                    # Try to find the core pattern with case insensitive matching
                    f"CAPWORD{placeholder.split('_')[0][-1]}.*CAPWORD" if '_' in placeholder else placeholder,
                    # Case insensitive version of the placeholder
                    re.escape(placeholder).replace(r'CAPWORD', r'(?i)CAPWORD'),
                    # Very flexible pattern for edge cases
                    r'CAPWORD\d+_[a-f0-9]+_CAPWORD',
                    # Case insensitive flexible pattern
                    r'(?i)CAPWORD\d+_[a-f0-9]+_CAPWORD'
                ]
                
                restored = False
                for pattern in patterns_to_try:
                    try:
                        if re.search(pattern, restored_text):
                            restored_text = re.sub(pattern, word, restored_text, count=1)
                            print(f"✅ Pattern match restored: {pattern} -> {word}")
                            restored = True
                            break
                    except re.error as e:
                        print(f"⚠️ Pattern error: {e}")
                        continue
                
                if not restored:
                    print(f"⚠️ Could not restore placeholder: {placeholder} for word: {word}")
                    # As a last resort, try to find any CAPWORD pattern that might be our placeholder
                    capword_patterns = [
                        r'CAPWORD\d+_[a-f0-9]+_CAPWORD',  # Exact pattern
                        r'(?i)CAPWORD\d+_[a-f0-9]+_CAPWORD',  # Case insensitive
                        r'CAPWORD\d+.*CAPWORD',  # Very flexible
                        r'(?i)CAPWORD\d+.*CAPWORD'  # Very flexible case insensitive
                    ]
                    
                    for fallback_pattern in capword_patterns:
                        try:
                            if re.search(fallback_pattern, restored_text):
                                # Replace the first occurrence of any CAPWORD pattern
                                restored_text = re.sub(fallback_pattern, word, restored_text, count=1)
                                print(f"🔄 Fallback restoration applied for: {word} using pattern: {fallback_pattern}")
                                restored = True
                                break
                        except re.error:
                            continue
            
            # Log if restoration made changes
            if original_text != restored_text:
                print(f"🔧 Successfully restored: {word}")
        
        # Check for any remaining CAPWORD placeholders that weren't restored
        remaining_placeholders = re.findall(r'CAPWORD\d+_[a-f0-9]+_CAPWORD', restored_text)
        if remaining_placeholders:
            print(f"⚠️ Warning: {len(remaining_placeholders)} placeholders could not be restored: {remaining_placeholders}")
        
        print(f"📝 Text after restoration: {restored_text[:200]}...")
        return restored_text

    def translate_text(self, text: str, target_lang: str, source_lang: str = None) -> Dict[str, str]:
        """
        Translate text to target language.
        
        Args:
            text: Text to translate
            target_lang: Target language code
            source_lang: Source language code (auto-detect if None)
            
        Returns:
            Dictionary with translation result and metadata
        """
        if not text or not text.strip():
            return {
                'translated_text': text,
                'source_language': 'unknown',
                'target_language': target_lang,
                'translation_provider': 'none',
                'cached': False
            }
        
        # Auto-detect source language if not provided
        if not source_lang:
            source_lang = self.detect_language(text)
        
        # Check if translation should be skipped (same language only)
        if self._should_skip_translation(text, source_lang, target_lang):
            return {
                'translated_text': text,
                'source_language': source_lang,
                'target_language': target_lang,
                'translation_provider': 'same_language',
                'cached': False
            }
        
        # Preserve continuous capital words before translation
        text_for_translation, continuous_capital_words = self._preserve_continuous_capital_words(text)
        
        # Performance tracking
        start_time = time.time()
        self.performance_stats['total_translations'] += 1
        
        # Check cache first (thread-safe) - use original text for cache key
        cache_key = self._generate_cache_key(text, source_lang, target_lang)
        with self.cache_lock:
            if cache_key in self.cache and self._is_cache_valid(self.cache[cache_key]):
                cached_result = self.cache[cache_key]
                self.performance_stats['cache_hits'] += 1
                elapsed_time = time.time() - start_time
                self._update_avg_time(elapsed_time)
                return {
                    'translated_text': cached_result['translated_text'],
                    'source_language': source_lang,
                    'target_language': target_lang,
                    'translation_provider': cached_result.get('provider', 'cached'),
                    'cached': True
                }
        
        # Track request
        self.error_counts['total_requests'] += 1

        # Attempt translation using available providers with modified text
        translation_result = self._attempt_translation(text_for_translation, source_lang, target_lang)

        # Track success/failure
        if translation_result['success']:
            self.error_counts['successful_requests'] += 1
            
            # Restore continuous capital words in the translated text
            translation_result['translated_text'] = self._restore_continuous_capital_words(
                translation_result['translated_text'], 
                continuous_capital_words
            )

        # Cache the result (thread-safe) - cache the final result with continuous capital words restored
        if translation_result['success']:
            with self.cache_lock:
                self.cache[cache_key] = {
                    'translated_text': translation_result['translated_text'],
                    'provider': translation_result['provider'],
                    'timestamp': datetime.now().isoformat()
                }

        return {
            'translated_text': translation_result['translated_text'],
            'source_language': source_lang,
            'target_language': target_lang,
            'translation_provider': translation_result['provider'],
            'cached': False
        }
    
    def _attempt_translation(self, text: str, source_lang: str, target_lang: str) -> Dict:
        """
        Attempt translation using available providers with improved error handling.

        Returns:
            Dictionary with translation result
        """
        # Enhanced logging for debugging
        print(f"🔄 Starting translation attempt: '{text[:30]}...' ({source_lang} -> {target_lang})")

        # Try multiple translation approaches

        # 1. Try Deep Translator first if available
        if self.translator_available and TRANSLATOR_AVAILABLE:
            try:
                print(f"🌐 Attempting Deep Translator: {source_lang} -> {target_lang}")

                # Use Deep Translator (GoogleTranslator) with retry logic
                translator = GoogleTranslator(source=source_lang, target=target_lang)

                # Add retry logic for Deep Translator
                max_retries = 2
                for attempt in range(max_retries):
                    try:
                        # Normalize input text before translation
                        normalized_text = unicodedata.normalize('NFC', text)
                        result = translator.translate(normalized_text)

                        if result and result.strip() and result != text and len(result.strip()) > 0:
                            # Normalize the result to ensure proper Unicode handling
                            normalized_result = unicodedata.normalize('NFC', result.strip())
                            
                            # Enhanced validation for multilingual content
                            if not self._is_translation_valid(text, normalized_result, source_lang, target_lang):
                                print(f"⚠️ Deep Translator result failed validation on attempt {attempt + 1}")
                                if attempt < max_retries - 1:
                                    time.sleep(1)  # Brief delay before retry
                                    continue

                            # Additional check for Unicode corruption
                            if self._has_unicode_corruption(normalized_result):
                                print(f"⚠️ Deep Translator result has Unicode corruption on attempt {attempt + 1}")
                                if attempt < max_retries - 1:
                                    time.sleep(1)
                                    continue

                            print(f"✅ Deep Translator successful: {normalized_result[:50]}...")
                            return {
                                'success': True,
                                'translated_text': normalized_result,
                                'provider': 'deep_translator_google'
                            }
                        else:
                            print(f"⚠️ Deep Translator returned empty or unchanged result on attempt {attempt + 1}")
                            if attempt < max_retries - 1:
                                time.sleep(1)  # Brief delay before retry

                    except Exception as retry_e:
                        print(f"❌ Deep Translator retry {attempt + 1} error: {retry_e}")
                        if attempt < max_retries - 1:
                            time.sleep(1)  # Brief delay before retry

            except Exception as e:
                print(f"❌ Deep Translator error: {e}")

        # 2. Try MyMemory Translation API as backup
        try:
            print(f"🌐 Attempting MyMemory API: {source_lang} -> {target_lang}")
            result = self._translate_with_mymemory(text, source_lang, target_lang)
            if result and result != text:
                print(f"✅ MyMemory API successful: {result[:50]}...")
                return {
                    'success': True,
                    'translated_text': result,
                    'provider': 'mymemory_api'
                }
        except Exception as e:
            print(f"❌ MyMemory API error: {e}")

        # 3. Try LibreTranslate API as another backup
        try:
            print(f"🌐 Attempting LibreTranslate API: {source_lang} -> {target_lang}")
            result = self._translate_with_libretranslate(text, source_lang, target_lang)
            if result and result != text:
                print(f"✅ LibreTranslate API successful: {result[:50]}...")
                return {
                    'success': True,
                    'translated_text': result,
                    'provider': 'libretranslate_api'
                }
        except Exception as e:
            print(f"❌ LibreTranslate API error: {e}")

        # 4. Try the enhanced retry method as a comprehensive fallback
        print(f"🔄 Attempting comprehensive retry translation")
        retry_result = self._translate_with_retry(text, source_lang, target_lang)
        if retry_result:
            print(f"✅ Retry translation successful: {retry_result[:50]}...")
            return {
                'success': True,
                'translated_text': retry_result,
                'provider': 'retry_comprehensive'
            }

        # 5. Fallback to pattern-based translations for common cases
        fallback_translations = self._get_fallback_translations(text, source_lang, target_lang)

        if fallback_translations and fallback_translations != text:
            print(f"🔄 Using fallback translation")
            return {
                'success': True,
                'translated_text': fallback_translations,
                'provider': 'fallback'
            }

        # If no translation available, return original text
        print(f"⚠️ No translation available, returning original text")
        return {
            'success': False,
            'translated_text': text,
            'provider': 'none'
        }
    
    def _get_fallback_translations(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Provide fallback translations for common phrases and responses.
        This is a simplified approach - in production you'd use proper translation APIs.
        """
        
        # Common response patterns and their translations
        translation_patterns = {
            ('en', 'ta'): {
                'Based on the provided information': 'வழங்கப்பட்ட தகவலின் அடிப்படையில்',
                'According to the document': 'ஆவணத்தின் படி',
                'The key points are': 'முக்கிய புள்ளிகள்',
                'In summary': 'சுருக்கமாக',
                'Therefore': 'எனவே',
                'However': 'இருப்பினும்',
                'Additionally': 'கூடுதலாக',
                'Furthermore': 'மேலும்',
                'Reserve Bank of India': 'இந்திய ரிசர்வ் வங்கி',
                'cryptocurrency': 'கிரிப்டோகரன்சி',
                'digital assets': 'டிஜிட்டல் ஆசெட்கள்',
                'regulation': 'ஒழுங்குமுறை',
                'financial institution': 'நிதி நிறுவனம்',
                'ban': 'தடை',
                'Supreme Court': 'உச்ச நீதிமன்றம்',
                'government': 'அரசு',
                'bill': 'சட்டமுன்வடிவு'
            },
            ('ta', 'en'): {
                'வழங்கப்பட்ட தகவலின் அடிப்படையில்': 'Based on the provided information',
                'ஆவணத்தின் படி': 'According to the document',
                'முக்கிய புள்ளிகள்': 'The key points are',
                'சுருக்கமாக': 'In summary',
                'எனவே': 'Therefore',
                'இருப்பினும்': 'However',
                'கூடுதலாக': 'Additionally',
                'மேலும்': 'Furthermore'
            },
            ('en', 'te'): {
                'Based on the provided information': 'అందించిన సమాచారం ఆధారంగా',
                'According to the document': 'పత్రం ప్రకారం',
                'The key points are': 'ముఖ్య అంశాలు',
                'In summary': 'సంక్షేపంలో',
                'Therefore': 'కాబట్టి',
                'However': 'అయితే',
                'Additionally': 'అదనంగా',
                'Furthermore': 'ఇంకా',
                'Reserve Bank of India': 'భారతీయ రిజర్వ్ బ్యాంక్',
                'cryptocurrency': 'క్రిప్టోకరెన్సీ',
                'digital assets': 'డిజిటల్ ఆస్తులు',
                'regulation': 'రెగ్యులేషన్',
                'financial institution': 'ఫైనాన్షియల్ ఇన్స్టిట్యూషన్',
                'ban': 'నిషేధం',
                'Supreme Court': 'సుప్రీం కోర్టు',
                'government': 'ప్రభుత్వం',
                'bill': 'బిల్లు',
                # Additional financial terms
                'mutual funds': 'మ్యూచువల్ ఫండ్స్',
                'investment': 'పెట్టుబడి',
                'strategies': 'వ్యూహాలు',
                'benefits': 'ప్రయోజనాలు',
                'Systematic Investment Plan': 'సిస్టమాటిక్ ఇన్వెస్ట్‌మెంట్ ప్లాన్',
                'SIP': 'SIP',
                'Invest a fixed amount regularly': 'క్రమం తప్పకుండా నిర్ణీత మొత్తాన్ని పెట్టుబడి పెట్టండి',
                'monthly': 'నెలవారీ',
                'quarterly': 'త్రైమాసిక',
                'rupee cost averaging': 'రూపాయి కాస్ట్ యావరేజింగ్',
                'Reduces market timing risk': 'మార్కెట్ టైమింగ్ రిస్క్‌ను తగ్గిస్తుంది',
                'Diversification': 'వైవిధ్యీకరణ',
                'Invest across different categories': 'వివిధ వర్గాలలో పెట్టుబడి పెట్టండి',
                'equity': 'ఈక్విటీ',
                'debt': 'డెట్',
                'hybrid': 'హైబ్రిడ్',
                'minimize risk': 'రిస్క్‌ను తగ్గించండి',
                'Example': 'ఉదాహరణ',
                'Large-cap': 'లార్జ్-క్యాప్',
                'mid-cap': 'మిడ్-క్యాప్',
                'sectoral funds': 'సెక్టోరల్ ఫండ్స్',
                'Long-Term Investing': 'దీర్ఘకాలిక పెట్టుబడి',
                'Staying invested for': 'పెట్టుబడిలో ఉండటం',
                'years': 'సంవత్సరాలు',
                'helps ride out market volatility': 'మార్కెట్ అస్థిరతను అధిగమించడంలో సహాయపడుతుంది',
                'benefit from compounding': 'కాంపౌండింగ్ నుండి ప్రయోజనం పొందండి',
                'Asset Allocation': 'ఆస్తి కేటాయింపు',
                'Adjust equity-debt ratio': 'ఈక్విటీ-డెట్ నిష్పత్తిని సర్దుబాటు చేయండి',
                'based on risk appetite': 'రిస్క్ ఆకలి ఆధారంగా',
                'moderate risk': 'మోడరేట్ రిస్క్',
                'Tax-Efficient Funds': 'పన్ను-సమర్థవంతమైన ఫండ్స్',
                'ELSS': 'ELSS',
                'Equity-Linked Savings Scheme': 'ఈక్విటీ-లింక్డ్ సేవింగ్స్ స్కీమ్',
                'tax deductions': 'పన్ను మినహాయింపులు',
                'Section 80C': 'సెక్షన్ 80C',
                'Benefits of Mutual Funds': 'మ్యూచువల్ ఫండ్స్ యొక్క ప్రయోజనాలు',
                'Professional Management': 'వృత్తిపరమైన నిర్వహణ',
                'Managed by experts': 'నిపుణులచే నిర్వహించబడుతుంది'
            },
            ('te', 'en'): {
                'అందించిన సమాచారం ఆధారంగా': 'Based on the provided information',
                'పత్రం ప్రకారం': 'According to the document',
                'ముఖ్య అంశాలు': 'The key points are',
                'సంక్షేపంలో': 'In summary',
                'కాబట్టి': 'Therefore',
                'అయితే': 'However',
                'అదనంగా': 'Additionally',
                'ఇంకా': 'Furthermore'
            },
            ('en', 'kn'): {
                'Based on the provided information': 'ಒದಗಿಸಿದ ಮಾಹಿತಿಯ ಆಧಾರದ ಮೇಲೆ',
                'According to the document': 'ದಾಖಲೆಯ ಪ್ರಕಾರ',
                'The key points are': 'ಮುಖ್ಯ ಅಂಶಗಳು',
                'In summary': 'ಸಂಕ್ಷೇಪವಾಗಿ',
                'Therefore': 'ಆದ್ದರಿಂದ',
                'However': 'ಆದರೆ',
                'Additionally': 'ಹೆಚ್ಚುವರಿಯಾಗಿ',
                'Furthermore': 'ಇದಲ್ಲದೆ',
                'Reserve Bank of India': 'ಭಾರತೀಯ ರಿಸರ್ವ್ ಬ್ಯಾಂಕ್',
                'cryptocurrency': 'ಕ್ರಿಪ್ಟೋಕರೆನ್ಸಿ',
                'digital assets': 'ಡಿಜಿಟಲ್ ಆಸ್ತಿಗಳು',
                'regulation': 'ನಿಯಂತ್ರಣ',
                'financial institution': 'ಹಣಕಾಸು ಸಂಸ್ಥೆ',
                'ban': 'ನಿಷೇಧ',
                'Supreme Court': 'ಸುಪ್ರೀಂ ಕೋರ್ಟ್',
                'government': 'ಸರ್ಕಾರ',
                'bill': 'ಬಿಲ್ಲು'
            },
            ('kn', 'en'): {
                'ಒದಗಿಸಿದ ಮಾಹಿತಿಯ ಆಧಾರದ ಮೇಲೆ': 'Based on the provided information',
                'ದಾಖಲೆಯ ಪ್ರಕಾರ': 'According to the document',
                'ಮುಖ್ಯ ಅಂಶಗಳು': 'The key points are',
                'ಸಂಕ್ಷೇಪವಾಗಿ': 'In summary',
                'ಆದ್ದರಿಂದ': 'Therefore',
                'ಆದರೆ': 'However',
                'ಹೆಚ್ಚುವರಿಯಾಗಿ': 'Additionally',
                'ಇದಲ್ಲದೆ': 'Furthermore'
            },
            ('en', 'or'): {
                'Based on the provided information': 'ପ୍ରଦାନ କରାଯାଇଥିବା ସୂଚନା ଆଧାରରେ',
                'According to the document': 'ଦଲିଲ ଅନୁଯାୟୀ',
                'The key points are': 'ମୁଖ୍ୟ ବିଷୟଗୁଡ଼ିକ ହେଉଛି',
                'In summary': 'ସଂକ୍ଷେପରେ',
                'Therefore': 'ତେଣୁ',
                'However': 'ତଥାପି',
                'Additionally': 'ଅତିରିକ୍ତ ଭାବରେ',
                'Furthermore': 'ଆହୁରି ମଧ୍ୟ',
                'Reserve Bank of India': 'ଭାରତୀୟ ରିଜର୍ଭ ବ୍ୟାଙ୍କ',
                'cryptocurrency': 'କ୍ରିପ୍ଟୋକରେନ୍ସି',
                'digital assets': 'ଡିଜିଟାଲ ସମ୍ପତ୍ତି',
                'regulation': 'ନିୟମାବଳୀ',
                'financial institution': 'ଆର୍ଥିକ ପ୍ରତିଷ୍ଠାନ',
                'ban': 'ନିଷେଧ',
                'Supreme Court': 'ସର୍ବୋଚ୍ଚ ନ୍ୟାୟାଳୟ',
                'government': 'ସରକାର',
                'bill': 'ବିଲ୍'
            },
            ('or', 'en'): {
                'ପ୍ରଦାନ କରାଯାଇଥିବା ସୂଚନା ଆଧାରରେ': 'Based on the provided information',
                'ଦଲିଲ ଅନୁଯାୟୀ': 'According to the document',
                'ମୁଖ୍ୟ ବିଷୟଗୁଡ଼ିକ ହେଉଛି': 'The key points are',
                'ସଂକ୍ଷେପରେ': 'In summary',
                'ତେଣୁ': 'Therefore',
                'ତଥାପି': 'However',
                'ଅତିରିକ୍ତ ଭାବରେ': 'Additionally',
                'ଆହୁରି ମଧ୍ୟ': 'Furthermore'
            }
        }
        
        pattern_key = (source_lang, target_lang)
        if pattern_key in translation_patterns:
            patterns = translation_patterns[pattern_key]

            # Enhanced pattern matching and replacement
            translated_text = text
            replacements_made = 0

            # Sort patterns by length (longest first) for better matching
            sorted_patterns = sorted(patterns.items(), key=lambda x: len(x[0]), reverse=True)

            for source_phrase, target_phrase in sorted_patterns:
                # Case-insensitive replacement for better matching
                import re
                pattern = re.compile(re.escape(source_phrase), re.IGNORECASE)
                if pattern.search(translated_text):
                    translated_text = pattern.sub(target_phrase, translated_text)
                    replacements_made += 1

            # If any replacements were made, return the translated text
            if replacements_made > 0:
                print(f"✅ Fallback translation made {replacements_made} replacements")
                return translated_text

        return None

    def _translate_with_mymemory(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Translate using MyMemory API (free translation service)
        """
        try:
            url = "https://api.mymemory.translated.net/get"
            params = {
                'q': text,
                'langpair': f"{source_lang}|{target_lang}"
            }

            # Use configurable timeout
            response = requests.get(url, params=params, timeout=self.api_timeout)
            if response.status_code == 200:
                data = response.json()
                if data.get('responseStatus') == 200:
                    translated_text = data.get('responseData', {}).get('translatedText', '')
                    if translated_text and translated_text.strip() and translated_text != text:
                        return translated_text.strip()

        except requests.exceptions.Timeout:
            print(f"MyMemory API timeout error for {source_lang} -> {target_lang}")
            self.error_counts['mymemory'] += 1
        except requests.exceptions.ConnectionError:
            print(f"MyMemory API connection error for {source_lang} -> {target_lang}")
            self.error_counts['mymemory'] += 1
        except Exception as e:
            print(f"MyMemory API error: {e}")
            self.error_counts['mymemory'] += 1

        return None

    def _translate_with_libretranslate(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Translate using LibreTranslate API (free and open source)
        """
        # List of LibreTranslate instances to try
        libretranslate_urls = [
            "https://libretranslate.de/translate",
            "https://translate.argosopentech.com/translate",
            "https://libretranslate.com/translate"
        ]

        for url in libretranslate_urls:
            try:
                data = {
                    'q': text,
                    'source': source_lang,
                    'target': target_lang,
                    'format': 'text'
                }

                headers = {
                    'Content-Type': 'application/json'
                }

                # Use configurable timeout
                response = requests.post(url, json=data, headers=headers, timeout=self.api_timeout)
                if response.status_code == 200:
                    result = response.json()
                    translated_text = result.get('translatedText', '')
                    if translated_text and translated_text.strip() and translated_text != text:
                        print(f"✅ LibreTranslate successful with {url}")
                        return translated_text.strip()

            except requests.exceptions.Timeout:
                print(f"LibreTranslate timeout error with {url}")
                self.error_counts['libretranslate'] += 1
                continue
            except requests.exceptions.ConnectionError:
                print(f"LibreTranslate connection error with {url}")
                self.error_counts['libretranslate'] += 1
                continue
            except Exception as e:
                print(f"LibreTranslate API error with {url}: {e}")
                self.error_counts['libretranslate'] += 1
                continue

        return None

    def _is_translation_valid(self, original_text: str, translated_text: str, source_lang: str, target_lang: str) -> bool:
        """
        Validate if the translation result is reasonable
        """
        if not translated_text or not translated_text.strip():
            return False

        # Check if translation is identical to original (might indicate failure)
        if translated_text.strip() == original_text.strip():
            return False

        # Check if translation is too short compared to original (might indicate truncation)
        if len(translated_text.strip()) < len(original_text.strip()) * 0.3:
            return False

        # Check if translation contains only special characters or numbers
        import re
        if re.match(r'^[^a-zA-Z\u0080-\uFFFF]*$', translated_text.strip()):
            return False

        # Check for obvious translation failures (repeated characters, etc.)
        if len(set(translated_text.strip())) < 3 and len(translated_text.strip()) > 10:
            return False

        return True

    def _has_unicode_corruption(self, text: str) -> bool:
        """
        Check if the text has Unicode corruption patterns
        """
        if not text:
            return False
            
        # Check for common Unicode corruption patterns
        corruption_patterns = [
            # Replacement character (indicates encoding issues)
            '\ufffd',
            # Common corruption patterns for Indian languages
            '??',
            # Broken Unicode sequences
            '\ud800',  # High surrogate without low surrogate
            '\udc00',  # Low surrogate without high surrogate
        ]
        
        for pattern in corruption_patterns:
            if pattern in text:
                try:
                    print(f"⚠️ Unicode corruption detected: {pattern}")
                except UnicodeEncodeError:
                    print(f"⚠️ Unicode corruption detected: [unprintable character]")
                return True
                
        # Check for excessive question marks (often indicates encoding failure)
        question_mark_ratio = text.count('?') / len(text) if len(text) > 0 else 0
        if question_mark_ratio > 0.3:  # More than 30% question marks
            print(f"⚠️ Excessive question marks detected: {question_mark_ratio:.2%}")
            return True
            
        return False

    def _translate_with_retry(self, text: str, source_lang: str, target_lang: str, max_retries: int = 3) -> Optional[str]:
        """
        Translate text with retry logic and multiple providers
        """
        providers = [
            ('deep_translator', self._translate_with_deep_translator),
            ('mymemory', self._translate_with_mymemory),
            ('libretranslate', self._translate_with_libretranslate)
        ]

        for provider_name, provider_func in providers:
            for attempt in range(max_retries):
                try:
                    print(f"🔄 Trying {provider_name} (attempt {attempt + 1}/{max_retries})")
                    result = provider_func(text, source_lang, target_lang)

                    if result and self._is_translation_valid(text, result, source_lang, target_lang):
                        print(f"✅ {provider_name} successful on attempt {attempt + 1}")
                        return result
                    elif result:
                        print(f"⚠️ {provider_name} returned invalid result on attempt {attempt + 1}")
                    else:
                        print(f"⚠️ {provider_name} returned no result on attempt {attempt + 1}")

                except Exception as e:
                    print(f"❌ {provider_name} error on attempt {attempt + 1}: {e}")

                # Brief delay between retries
                if attempt < max_retries - 1:
                    time.sleep(0.5)

        return None

    def translate_multiple_texts_concurrent(self, texts: List[str], target_lang: str, source_lang: str = None, max_workers: int = 3) -> List[Dict[str, str]]:
        """
        Translate multiple texts concurrently for better performance.
        
        Args:
            texts: List of texts to translate
            target_lang: Target language code
            source_lang: Source language code (auto-detect if None)
            max_workers: Maximum number of concurrent workers
            
        Returns:
            List of translation results
        """
        if not texts:
            return []
        
        print(f"🚀 Starting concurrent translation of {len(texts)} texts to {target_lang}")
        start_time = time.time()
        
        results = []
        
        # Use ThreadPoolExecutor for concurrent translation
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all translation tasks
            future_to_index = {
                executor.submit(self.translate_text, text, target_lang, source_lang): i 
                for i, text in enumerate(texts)
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results.append((index, result))
                except Exception as e:
                    print(f"❌ Translation failed for text {index}: {str(e)}")
                    # Return original text on failure
                    results.append((index, {
                        'translated_text': texts[index],
                        'source_language': source_lang or 'unknown',
                        'target_language': target_lang,
                        'translation_provider': 'failed',
                        'cached': False
                    }))
        
        # Sort results by original index to maintain order
        results.sort(key=lambda x: x[0])
        final_results = [result[1] for result in results]
        
        elapsed_time = time.time() - start_time
        print(f"✅ Concurrent translation completed in {elapsed_time:.2f}s (avg: {elapsed_time/len(texts):.2f}s per text)")
        
        return final_results

    def translate_batch_optimized(self, texts: List[str], target_lang: str, source_lang: str = None) -> List[str]:
        """
        Optimized batch translation with intelligent caching and chunking.
        
        Args:
            texts: List of texts to translate
            target_lang: Target language code
            source_lang: Source language code (auto-detect if None)
            
        Returns:
            List of translated texts
        """
        if not texts:
            return []
        
        print(f"🚀 Starting optimized batch translation of {len(texts)} texts")
        start_time = time.time()
        self.performance_stats['batch_translations'] += 1
        
        # Check cache for all texts first
        cached_results = {}
        texts_to_translate = []
        text_indices = {}
        
        with self.cache_lock:
            for i, text in enumerate(texts):
                if not text or not text.strip():
                    cached_results[i] = text
                    continue
                    
                # Auto-detect source language if not provided
                detected_source = source_lang or self.detect_language(text)
                
                # Skip translation if source and target are the same
                if detected_source == target_lang:
                    cached_results[i] = text
                    continue
                
                cache_key = self._generate_cache_key(text, detected_source, target_lang)
                if cache_key in self.cache and self._is_cache_valid(self.cache[cache_key]):
                    cached_results[i] = self.cache[cache_key]['translated_text']
                else:
                    texts_to_translate.append(text)
                    text_indices[len(texts_to_translate) - 1] = i
        
        print(f"📊 Cache hit rate: {len(cached_results)}/{len(texts)} ({len(cached_results)/len(texts)*100:.1f}%)")
        
        # Translate remaining texts concurrently
        if texts_to_translate:
            translation_results = self.translate_multiple_texts_concurrent(
                texts_to_translate, target_lang, source_lang, max_workers=4
            )
            
            # Merge results
            for j, result in enumerate(translation_results):
                original_index = text_indices[j]
                cached_results[original_index] = result['translated_text']
        
        # Reconstruct final results in original order
        final_results = [cached_results.get(i, texts[i]) for i in range(len(texts))]
        
        elapsed_time = time.time() - start_time
        print(f"✅ Optimized batch translation completed in {elapsed_time:.2f}s")
        
        return final_results

    def _translate_with_deep_translator(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Translate using Deep Translator (separated for retry logic)
        """
        if not (self.translator_available and TRANSLATOR_AVAILABLE):
            return None

        try:
            translator = GoogleTranslator(source=source_lang, target=target_lang)
            result = translator.translate(text)

            if result and result.strip() and result != text:
                return result.strip()

        except Exception as e:
            print(f"Deep Translator specific error: {e}")
            self.error_counts['deep_translator'] += 1

        return None

    def _translate_long_text(self, text: str, source_lang: str, target_lang: str) -> str:
        """
        Enhanced translation for long text with better Unicode preservation and word-level capital preservation
        """
        # Check if translation should be skipped (same language only)
        if self._should_skip_translation(text, source_lang, target_lang):
            return text
        
        # Preserve continuous capital words before translation
        text_for_translation, continuous_capital_words = self._preserve_continuous_capital_words(text)
        
        if len(text_for_translation) < 500:  # For shorter texts, translate directly
            result = self._attempt_translation(text_for_translation, source_lang, target_lang)
            translated_text = result['translated_text']
            # Restore continuous capital words
            return self._restore_continuous_capital_words(translated_text, continuous_capital_words)

        # Enhanced sentence splitting that preserves Unicode punctuation
        import re
        # Split on multiple punctuation marks including Unicode punctuation
        sentences = re.split(r'(?<=[.!?।॥])\s+', text_for_translation)
        translated_sentences = []

        print(f"🔄 Translating long text with {len(sentences)} sentences")

        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if sentence:
                # Check if this sentence should be skipped (same language only)
                if self._should_skip_translation(sentence, source_lang, target_lang):
                    print(f"🔒 Skipping sentence {i+1} due to same language: {sentence[:50]}...")
                    translated_sentences.append(sentence)
                    continue
                
                # Preserve Unicode normalization before translation
                normalized_sentence = unicodedata.normalize('NFC', sentence)
                
                print(f"📝 Translating sentence {i+1}/{len(sentences)}: {normalized_sentence[:50]}...")
                
                result = self._attempt_translation(normalized_sentence, source_lang, target_lang)
                translated_text = result['translated_text']
                
                # Ensure Unicode normalization after translation
                if translated_text:
                    translated_text = unicodedata.normalize('NFC', translated_text)
                
                translated_sentences.append(translated_text)
                
                # Brief pause between sentences to avoid rate limiting
                if i < len(sentences) - 1:
                    time.sleep(0.1)
            else:
                translated_sentences.append(sentence)

        # Join with proper spacing and normalize the final result
        final_text = unicodedata.normalize('NFC', ' '.join(translated_sentences))
        
        # Restore continuous capital words in the final translated text
        final_text = self._restore_continuous_capital_words(final_text, continuous_capital_words)
        
        print(f"✅ Long text translation completed: {len(final_text)} characters")
        
        return final_text

    def translate_response_data(self, response_data: Dict, target_lang: str) -> Dict:
        """
        Enhanced translation for response data with better Unicode preservation.
        
        Args:
            response_data: Response data containing text fields to translate
            target_lang: Target language code
            
        Returns:
            Translated response data with preserved Unicode integrity
        """
        translated_data = response_data.copy()
        
        print(f"🌐 Starting response data translation to {target_lang}")
        
        # Translate main AI response using enhanced method for better results
        if 'ai_response' in translated_data and translated_data['ai_response']:
            original_text = translated_data['ai_response']
            
            # Normalize the original text before translation
            normalized_original = unicodedata.normalize('NFC', original_text) if isinstance(original_text, str) else str(original_text)
            
            print(f"📝 Translating AI response: {len(normalized_original)} characters")
            
            # Use the enhanced long text translation method
            translated_text = self._translate_long_text(
                normalized_original,
                'en',  # Assume AI response is in English
                target_lang
            )
            
            # Validate the translation result
            if self._has_unicode_corruption(translated_text):
                print("⚠️ Unicode corruption detected in AI response translation, using fallback")
                # Try fallback translation
                fallback_result = self._get_fallback_translations(normalized_original, 'en', target_lang)
                if fallback_result and not self._has_unicode_corruption(fallback_result):
                    translated_text = fallback_result
                else:
                    # If all else fails, return original text
                    translated_text = normalized_original
                    print("⚠️ Using original text due to translation issues")
            
            translated_data['ai_response'] = translated_text
            translated_data['translation_metadata'] = {
                'ai_response': {
                    'translated_text': translated_text,
                    'source_language': 'en',
                    'target_language': target_lang,
                    'translation_provider': 'enhanced_unicode_safe',
                    'cached': False,
                    'unicode_safe': not self._has_unicode_corruption(translated_text)
                }
            }
        
        # Translate related questions with Unicode safety using concurrent processing
        if 'related_questions' in translated_data and translated_data['related_questions']:
            questions = translated_data['related_questions']
            print(f"📝 Translating {len(questions)} related questions concurrently")
            # Normalize questions before translation
            
            normalized_questions = [
                unicodedata.normalize('NFC', question) if isinstance(question, str) else str(question)
                for question in questions
            ]
            
            # Use optimized batch translation for better performance
            translated_question_texts = self.translate_batch_optimized(
                normalized_questions, target_lang, 'en'
            )
            
            # Convert to expected format
            question_translations = []
            for i, translated_text in enumerate(translated_question_texts):
                question_translations.append({
                    'translated_text': translated_text,
                    'source_language': 'en',
                    'target_language': target_lang,
                    'translation_provider': 'batch_optimized',
                    'cached': translated_text == normalized_questions[i]  # Same text means cached/no translation needed
                })
            
            translated_questions = []
            final_translations = []
            
            for i, (original_question, translation_result) in enumerate(zip(normalized_questions, question_translations)):
                translated_question = translation_result['translated_text']
                
                # Check for Unicode corruption in question translation
                if self._has_unicode_corruption(translated_question):
                    print(f"⚠️ Unicode corruption in question {i+1}, using fallback")
                    fallback_result = self._get_fallback_translations(original_question, 'en', target_lang)
                    if fallback_result and not self._has_unicode_corruption(fallback_result):
                        translated_question = fallback_result
                        translation_result['translated_text'] = translated_question
                        translation_result['translation_provider'] = 'fallback_unicode_safe'
                    else:
                        translated_question = original_question
                        translation_result['translated_text'] = translated_question
                        translation_result['translation_provider'] = 'original_text'
                
                translated_questions.append(translated_question)
                translation_result['unicode_safe'] = not self._has_unicode_corruption(translated_question)
                final_translations.append(translation_result)
            
            translated_data['related_questions'] = translated_questions
            if 'translation_metadata' not in translated_data:
                translated_data['translation_metadata'] = {}
            translated_data['translation_metadata']['related_questions'] = final_translations
        
        # Translate sentence analysis if present
        if 'sentence_analysis' in translated_data and translated_data['sentence_analysis']:
            sentence_analysis = translated_data['sentence_analysis']
            print(f"📝 Translating sentence analysis: {len(sentence_analysis)} sentences")
            
            translated_sentence_analysis = []
            for sentence_data in sentence_analysis:
                if isinstance(sentence_data, dict) and 'sentence' in sentence_data:
                    # Translate the sentence text
                    original_sentence = sentence_data['sentence']
                    if original_sentence and isinstance(original_sentence, str):
                        normalized_sentence = unicodedata.normalize('NFC', original_sentence)
                        
                        # Use the enhanced translation method
                        translated_sentence = self._translate_long_text(
                            normalized_sentence,
                            'en',  # Assume sentence is in English
                            target_lang
                        )
                        
                        # Check for Unicode corruption
                        if self._has_unicode_corruption(translated_sentence):
                            print(f"⚠️ Unicode corruption in sentence translation, using fallback")
                            fallback_result = self._get_fallback_translations(normalized_sentence, 'en', target_lang)
                            if fallback_result and not self._has_unicode_corruption(fallback_result):
                                translated_sentence = fallback_result
                            else:
                                translated_sentence = normalized_sentence
                        
                        # Create translated sentence data
                        translated_sentence_data = sentence_data.copy()
                        translated_sentence_data['sentence'] = translated_sentence
                        translated_sentence_analysis.append(translated_sentence_data)
                    else:
                        # Keep original if sentence is empty or not a string
                        translated_sentence_analysis.append(sentence_data)
                else:
                    # Keep original if not in expected format
                    translated_sentence_analysis.append(sentence_data)
            
            translated_data['sentence_analysis'] = translated_sentence_analysis
            if 'translation_metadata' not in translated_data:
                translated_data['translation_metadata'] = {}
            translated_data['translation_metadata']['sentence_analysis'] = {
                'sentences_translated': len(translated_sentence_analysis),
                'target_language': target_lang,
                'translation_provider': 'enhanced_unicode_safe'
            }
        
        # Add enhanced language metadata
        translated_data['response_language'] = target_lang
        translated_data['translation_timestamp'] = datetime.now().isoformat()
        translated_data['unicode_normalized'] = True
        
        print(f"✅ Response data translation completed for {target_lang}")
        
        return translated_data
    
    def get_cache_stats(self) -> Dict:
        """Get translation cache and error statistics"""
        valid_entries = 0
        expired_entries = 0

        for cache_entry in self.cache.values():
            if self._is_cache_valid(cache_entry):
                valid_entries += 1
            else:
                expired_entries += 1

        # Calculate success rate
        total_requests = self.error_counts['total_requests']
        success_rate = (self.error_counts['successful_requests'] / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_info': {
                'total_entries': len(self.cache),
                'valid_entries': valid_entries,
                'expired_entries': expired_entries,
                'cache_expiry_seconds': self.cache_expiry
            },
            'translation_stats': {
                'total_requests': total_requests,
                'successful_requests': self.error_counts['successful_requests'],
                'success_rate_percent': round(success_rate, 2),
                'error_counts': {
                    'deep_translator': self.error_counts['deep_translator'],
                    'mymemory': self.error_counts['mymemory'],
                    'libretranslate': self.error_counts['libretranslate']
                }
            },
            'configuration': {
                'api_timeout': self.api_timeout,
                'max_retries': self.max_retries,
                'retry_delay': self.retry_delay,
                'translator_available': self.translator_available
            },
            'supported_languages': self.supported_languages
        }
    
    def clear_cache(self):
        """Clear the translation cache"""
        self.cache.clear()
        print("🧹 Translation cache cleared")

    def reset_error_stats(self):
        """Reset error tracking statistics"""
        self.error_counts = {
            'deep_translator': 0,
            'mymemory': 0,
            'libretranslate': 0,
            'total_requests': 0,
            'successful_requests': 0
        }
        print("📊 Error statistics reset")

    def _initialize_common_translations(self):
        """Pre-populate cache with common financial and question translations for faster response"""
        common_translations = {
            # Common financial questions in English to regional languages
            ('en', 'ta'): [
                ('What is mutual fund?', 'மியூச்சுவல் ஃபண்ட் என்றால் என்ன?'),
                ('How to invest in SIP?', 'SIP இல் எப்படி முதலீடு செய்வது?'),
                ('What are the benefits?', 'நன்மைகள் என்ன?'),
                ('Tell me more about this', 'இதைப் பற்றி மேலும் சொல்லுங்கள்'),
                ('How does this work?', 'இது எப்படி வேலை செய்கிறது?'),
                ('What are the risks?', 'அபாயங்கள் என்ன?'),
            ],
            ('en', 'te'): [
                ('What is mutual fund?', 'మ్యూచువల్ ఫండ్ అంటే ఏమిటి?'),
                ('How to invest in SIP?', 'SIP లో ఎలా పెట్టుబడి పెట్టాలి?'),
                ('What are the benefits?', 'ప్రయోజనాలు ఏమిటి?'),
                ('Tell me more about this', 'దీని గురించి మరింత చెప్పండి'),
                ('How does this work?', 'ఇది ఎలా పని చేస్తుంది?'),
                ('What are the risks?', 'రిస్క్‌లు ఏమిటి?'),
            ],
            ('en', 'kn'): [
                ('What is mutual fund?', 'ಮ್ಯೂಚುವಲ್ ಫಂಡ್ ಎಂದರೇನು?'),
                ('How to invest in SIP?', 'SIP ನಲ್ಲಿ ಹೇಗೆ ಹೂಡಿಕೆ ಮಾಡುವುದು?'),
                ('What are the benefits?', 'ಪ್ರಯೋಜನಗಳು ಯಾವುವು?'),
                ('Tell me more about this', 'ಇದರ ಬಗ್ಗೆ ಹೆಚ್ಚು ಹೇಳಿ'),
                ('How does this work?', 'ಇದು ಹೇಗೆ ಕೆಲಸ ಮಾಡುತ್ತದೆ?'),
                ('What are the risks?', 'ಅಪಾಯಗಳು ಯಾವುವು?'),
            ],
            ('en', 'or'): [
                ('What is mutual fund?', 'ମ୍ୟୁଚୁଆଲ ଫଣ୍ଡ କଣ?'),
                ('How to invest in SIP?', 'SIP ରେ କିପରି ବିନିଯୋଗ କରିବେ?'),
                ('What are the benefits?', 'ଲାଭଗୁଡ଼ିକ କଣ?'),
                ('Tell me more about this', 'ଏହା ବିଷୟରେ ଅଧିକ କୁହନ୍ତୁ'),
                ('How does this work?', 'ଏହା କିପରି କାମ କରେ?'),
                ('What are the risks?', 'ବିପଦଗୁଡ଼ିକ କଣ?'),
            ]
        }
        
        # Pre-populate cache with common translations
        current_time = datetime.now().isoformat()
        for (source_lang, target_lang), translations in common_translations.items():
            for english_text, translated_text in translations:
                cache_key = self._generate_cache_key(english_text, source_lang, target_lang)
                self.cache[cache_key] = {
                    'translated_text': translated_text,
                    'provider': 'pre_cached',
                    'timestamp': current_time
                }
        
        print(f"✅ Pre-populated translation cache with {sum(len(t) for t in common_translations.values())} common translations")
        
        # Warm up cache with common financial terms
        self._warm_up_financial_terms_cache()

    def _warm_up_financial_terms_cache(self):
        """Pre-populate cache with common financial terms for faster translation"""
        financial_terms = {
            ('en', 'ta'): [
                ('investment', 'முதலீடு'),
                ('mutual fund', 'மியூச்சுவல் ஃபண்ட்'),
                ('SIP', 'SIP'),
                ('returns', 'வருமானம்'),
                ('risk', 'அபாயம்'),
                ('portfolio', 'போர்ட்ஃபோலியோ'),
                ('equity', 'ஈக்விட்டி'),
                ('debt', 'கடன்'),
                ('dividend', 'ஈவுத்தொகை'),
                ('NAV', 'NAV'),
            ],
            ('en', 'te'): [
                ('investment', 'పెట్టుబడి'),
                ('mutual fund', 'మ్యూచువల్ ఫండ్'),
                ('SIP', 'SIP'),
                ('returns', 'రిటర్న్స్'),
                ('risk', 'రిస్క్'),
                ('portfolio', 'పోర్ట్‌ఫోలియో'),
                ('equity', 'ఈక్విటీ'),
                ('debt', 'డెట్'),
                ('dividend', 'డివిడెండ్'),
                ('NAV', 'NAV'),
            ],
            ('en', 'kn'): [
                ('investment', 'ಹೂಡಿಕೆ'),
                ('mutual fund', 'ಮ್ಯೂಚುವಲ್ ಫಂಡ್'),
                ('SIP', 'SIP'),
                ('returns', 'ಆದಾಯ'),
                ('risk', 'ಅಪಾಯ'),
                ('portfolio', 'ಪೋರ್ಟ್‌ಫೋಲಿಯೋ'),
                ('equity', 'ಇಕ್ವಿಟಿ'),
                ('debt', 'ಸಾಲ'),
                ('dividend', 'ಲಾಭಾಂಶ'),
                ('NAV', 'NAV'),
            ],
            ('en', 'or'): [
                ('investment', 'ବିନିଯୋଗ'),
                ('mutual fund', 'ମ୍ୟୁଚୁଆଲ ଫଣ୍ଡ'),
                ('SIP', 'SIP'),
                ('returns', 'ରିଟର୍ନ'),
                ('risk', 'ବିପଦ'),
                ('portfolio', 'ପୋର୍ଟଫୋଲିଓ'),
                ('equity', 'ଇକ୍ୱିଟି'),
                ('debt', 'ଋଣ'),
                ('dividend', 'ଡିଭିଡେଣ୍ଡ'),
                ('NAV', 'NAV'),
            ]
        }
        
        current_time = datetime.now().isoformat()
        terms_cached = 0
        
        for (source_lang, target_lang), terms in financial_terms.items():
            for english_term, translated_term in terms:
                cache_key = self._generate_cache_key(english_term, source_lang, target_lang)
                self.cache[cache_key] = {
                    'translated_text': translated_term,
                    'provider': 'financial_terms_cache',
                    'timestamp': current_time
                }
                terms_cached += 1
        
        print(f"🏦 Warmed up financial terms cache with {terms_cached} terms")

    def _update_avg_time(self, elapsed_time: float):
        """Update average translation time with exponential moving average"""
        alpha = 0.1  # Smoothing factor
        if self.performance_stats['avg_translation_time'] == 0:
            self.performance_stats['avg_translation_time'] = elapsed_time
        else:
            self.performance_stats['avg_translation_time'] = (
                alpha * elapsed_time + (1 - alpha) * self.performance_stats['avg_translation_time']
            )

    def get_performance_stats(self) -> Dict:
        """Get translation performance statistics"""
        cache_hit_rate = (
            self.performance_stats['cache_hits'] / self.performance_stats['total_translations'] * 100
            if self.performance_stats['total_translations'] > 0 else 0
        )
        
        return {
            'total_translations': self.performance_stats['total_translations'],
            'cache_hits': self.performance_stats['cache_hits'],
            'cache_hit_rate_percent': round(cache_hit_rate, 2),
            'avg_translation_time_ms': round(self.performance_stats['avg_translation_time'] * 1000, 2),
            'batch_translations': self.performance_stats['batch_translations'],
            'concurrent_translations': self.performance_stats['concurrent_translations'],
            'cache_size': len(self.cache)
        }

    def get_health_status(self) -> Dict:
        """Get the health status of translation services"""
        total_requests = self.error_counts['total_requests']
        if total_requests == 0:
            return {
                'status': 'unknown',
                'message': 'No translation requests made yet',
                'recommendations': ['Make some translation requests to assess health']
            }

        success_rate = (self.error_counts['successful_requests'] / total_requests) * 100

        if success_rate >= 80:
            status = 'healthy'
            message = f'Translation services are working well ({success_rate:.1f}% success rate)'
            recommendations = []
        elif success_rate >= 50:
            status = 'degraded'
            message = f'Translation services are experiencing some issues ({success_rate:.1f}% success rate)'
            recommendations = [
                'Check network connectivity',
                'Consider increasing timeout values',
                'Monitor specific provider errors'
            ]
        else:
            status = 'unhealthy'
            message = f'Translation services are having significant problems ({success_rate:.1f}% success rate)'
            recommendations = [
                'Check internet connection',
                'Verify translation service availability',
                'Consider using fallback translations only',
                'Check firewall/proxy settings'
            ]

        return {
            'status': status,
            'message': message,
            'success_rate': success_rate,
            'total_requests': total_requests,
            'recommendations': recommendations,
            'provider_errors': self.error_counts
        }

# Global translation service instance
translation_service = TranslationService()
