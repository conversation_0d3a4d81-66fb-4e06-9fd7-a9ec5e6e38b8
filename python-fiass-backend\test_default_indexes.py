#!/usr/bin/env python3
"""
Test script to verify that both default and default1 indexes are properly supported.
"""

import os
import sys
import requests
import json

# Add the parent directory to the path so we can import from full_code
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_list_indexes():
    """Test the list-faiss-indexes endpoint to see both default indexes."""
    print("🧪 Testing list-faiss-indexes endpoint...")
    
    try:
        # Test GET request (all indexes)
        response = requests.get("http://localhost:5000/api/list-faiss-indexes")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ GET request successful")
            print(f"📋 Available indexes: {data.get('indexes', [])}")
            print(f"🎯 Default index: {data.get('default_index')}")
            print(f"🔧 Available defaults: {data.get('available_defaults', {})}")
        else:
            print(f"❌ GET request failed: {response.status_code}")
            print(f"Response: {response.text}")
            
        # Test POST request with user email
        test_email = "<EMAIL>"
        response = requests.post(
            "http://localhost:5000/api/list-faiss-indexes",
            json={"email": test_email}
        )
        if response.status_code == 200:
            data = response.json()
            print(f"✅ POST request successful for user: {test_email}")
            print(f"📋 User's available indexes: {data.get('indexes', [])}")
            print(f"🎯 Default index: {data.get('default_index')}")
            print(f"🔧 Available defaults: {data.get('available_defaults', {})}")
        else:
            print(f"❌ POST request failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing list-indexes: {e}")

def test_query_with_different_defaults():
    """Test querying with different default indexes."""
    print("\n🧪 Testing queries with different default indexes...")
    
    test_query = "What is artificial intelligence?"
    
    # Test with default index
    try:
        response = requests.post(
            "http://localhost:5000/api/query-faiss",
            json={
                "query": test_query,
                "index_name": "default",
                "k": 3
            }
        )
        if response.status_code == 200:
            print("✅ Query with 'default' index successful")
        else:
            print(f"❌ Query with 'default' index failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error querying default index: {e}")
    
    # Test with default1 index
    try:
        response = requests.post(
            "http://localhost:5000/api/query-faiss",
            json={
                "query": test_query,
                "index_name": "default1",
                "k": 3
            }
        )
        if response.status_code == 200:
            print("✅ Query with 'default1' index successful")
        else:
            print(f"❌ Query with 'default1' index failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error querying default1 index: {e}")

def test_financial_query_with_defaults():
    """Test financial query endpoint with different default indexes."""
    print("\n🧪 Testing financial queries with different default indexes...")
    
    test_query = "What are the latest market trends?"
    
    # Test with default index
    try:
        response = requests.post(
            "http://localhost:5000/financial_query",
            json={
                "query": test_query,
                "index_name": "default",
                "language": "English"
            }
        )
        if response.status_code == 200:
            print("✅ Financial query with 'default' index successful")
        else:
            print(f"❌ Financial query with 'default' index failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error with financial query on default index: {e}")
    
    # Test with default1 index
    try:
        response = requests.post(
            "http://localhost:5000/financial_query",
            json={
                "query": test_query,
                "index_name": "default1",
                "language": "English"
            }
        )
        if response.status_code == 200:
            print("✅ Financial query with 'default1' index successful")
        else:
            print(f"❌ Financial query with 'default1' index failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error with financial query on default1 index: {e}")

def main():
    """Run all tests."""
    print("🚀 Starting tests for dual default index support...")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:5000/api/health")
        if response.status_code != 200:
            print("❌ Server is not running or not responding. Please start the Flask server first.")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("Please make sure the Flask server is running on localhost:5000")
        return
    
    print("✅ Server is running, proceeding with tests...\n")
    
    # Run tests
    test_list_indexes()
    test_query_with_different_defaults()
    test_financial_query_with_defaults()
    
    print("\n" + "=" * 60)
    print("🏁 Tests completed!")

if __name__ == "__main__":
    main()