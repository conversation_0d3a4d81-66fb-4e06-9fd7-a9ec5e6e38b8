#!/usr/bin/env python3
"""
Test script to verify the financial_query endpoint works with the fix
"""

import requests
import json

def test_financial_query():
    """Test the financial_query endpoint"""
    
    print("🧪 Testing financial_query endpoint...")
    
    # Test data
    test_data = {
        "query": "Explain STRUCTURE OF THE ATMOSPHERE ? Why is under...",
        "index_name": "dwindele",
        "client_email": "<EMAIL>",
        "language": "English"
    }
    
    try:
        # Make request to the endpoint
        url = "http://localhost:5000/financial_query"
        
        print(f"📡 Sending request to {url}")
        print(f"📋 Request data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Check if we have AI response
            if 'ai_response' in result:
                print(f"🤖 AI Response preview: {result['ai_response'][:200]}...")
            
            # Check if we have retrieved documents
            if 'retrieved_docs' in result:
                print(f"📚 Retrieved {len(result['retrieved_docs'])} documents")
                
            return True
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure the Flask server is running on localhost:5000")
        return False
    except Exception as e:
        print(f"❌ Error during request: {e}")
        return False

if __name__ == "__main__":
    success = test_financial_query()
    if success:
        print("\n🎉 Financial query test PASSED!")
    else:
        print("\n💥 Financial query test FAILED!")
        print("💡 Make sure to start the Flask server first: python full_code.py")