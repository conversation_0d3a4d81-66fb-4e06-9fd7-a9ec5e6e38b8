#!/usr/bin/env python3
"""
Test script to verify multilingual text processing improvements
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

from services.translation_service import TranslationService

def test_unicode_corruption_detection():
    """Test Unicode corruption detection"""
    print("🧪 Testing Unicode corruption detection...")
    
    service = TranslationService()
    
    # Test cases
    test_cases = [
        ("Normal English text", False),
        ("தமிழ் மொழி", False),  # Tamil text
        ("తెలుగు భాష", False),  # Telugu text
        ("ಕನ್ನಡ ಭಾಷೆ", False),  # Kannada text
        ("Text with replacement char \ufffd", True),
        ("Text with ?? corruption", True),
        ("Text with \ud800 surrogate", True),
        ("???????????????????", True),  # Excessive question marks
        ("Normal text with one ?", False),
    ]
    
    for text, expected_corruption in test_cases:
        has_corruption = service._has_unicode_corruption(text)
        status = "✅" if has_corruption == expected_corruption else "❌"
        try:
            print(f"{status} '{text[:30]}...' - Corruption: {has_corruption}")
        except UnicodeEncodeError:
            print(f"{status} '[unprintable text]' - Corruption: {has_corruption}")
    
    print()

def test_text_cleaning():
    """Test multilingual text cleaning"""
    print("🧪 Testing multilingual text cleaning...")
    
    service = TranslationService()
    
    # Test cases with corrupted text
    test_cases = [
        # Tamil repetition
        "தமிழ் தமிழ் தமிழ் மொழி",
        # Telugu repetition  
        "తెలుగు తెలుగు తెలుగు భాష",
        # English repetition
        "English English English language",
        # Mixed punctuation
        "This is a sentence... Another sentence।। Final sentence.",
        # Broken line breaks
        "First line\n\nSecond line\n\n\nThird line",
    ]
    
    import unicodedata
    
    for text in test_cases:
        # Simulate the cleaning process that would happen in translation
        normalized = unicodedata.normalize('NFC', text)
        print(f"Original: {text}")
        print(f"Normalized: {normalized}")
        print(f"Has corruption: {service._has_unicode_corruption(normalized)}")
        print()

def test_translation_validation():
    """Test translation validation"""
    print("🧪 Testing translation validation...")
    
    service = TranslationService()
    
    test_cases = [
        ("Hello world", "வணக்கம் உலகம்", True),  # Good translation
        ("Hello world", "Hello world", False),  # Same as original
        ("Hello world", "", False),  # Empty result
        ("Hello world", "???", False),  # Corrupted result
        ("Long text here", "Hi", False),  # Too short
    ]
    
    for original, translated, expected_valid in test_cases:
        is_valid = service._is_translation_valid(original, translated, 'en', 'ta')
        status = "✅" if is_valid == expected_valid else "❌"
        print(f"{status} '{original}' -> '{translated}' - Valid: {is_valid}")
    
    print()

def test_language_detection():
    """Test language detection"""
    print("🧪 Testing language detection...")
    
    service = TranslationService()
    
    test_cases = [
        ("Hello world", "en"),
        ("தமிழ் மொழி", "ta"),
        ("తెలుగు భాష", "te"),
        ("ಕನ್ನಡ ಭಾಷೆ", "kn"),
        ("हिंदी भाषा", "hi"),
        ("مرحبا بالعالم", "ar"),
        ("你好世界", "zh"),
        ("", "en"),  # Empty defaults to English
    ]
    
    for text, expected_lang in test_cases:
        detected = service.detect_language(text)
        status = "✅" if detected == expected_lang else "❌"
        print(f"{status} '{text}' -> {detected} (expected: {expected_lang})")
    
    print()

def test_fallback_translations():
    """Test fallback translations"""
    print("🧪 Testing fallback translations...")
    
    service = TranslationService()
    
    test_cases = [
        ("Based on the provided information", "en", "ta"),
        ("According to the document", "en", "te"),
        ("The key points are", "en", "kn"),
        ("Random text that won't match", "en", "ta"),
    ]
    
    for text, source_lang, target_lang in test_cases:
        result = service._get_fallback_translations(text, source_lang, target_lang)
        status = "✅" if result else "❌"
        print(f"{status} '{text}' ({source_lang}->{target_lang}) -> {result}")
    
    print()

def main():
    """Run all tests"""
    print("🚀 Starting multilingual improvements test suite...\n")
    
    test_unicode_corruption_detection()
    test_text_cleaning()
    test_translation_validation()
    test_language_detection()
    test_fallback_translations()
    
    print("✅ All tests completed!")
    print("\n📋 Summary of improvements:")
    print("- Enhanced Unicode corruption detection")
    print("- Better multilingual text cleaning")
    print("- Improved translation validation")
    print("- Robust language detection")
    print("- Fallback translation patterns")
    print("- Unicode normalization (NFC)")
    print("- Professional UI styling")

if __name__ == "__main__":
    main()