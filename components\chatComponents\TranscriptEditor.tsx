import React, { useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PiWaveform } from 'react-icons/pi';

interface TranscriptEditorProps {
  transcript: string;
  editedTranscript: string;
  setEditedTranscript: (transcript: string) => void;
  isEditingTranscript: boolean;
  setIsEditingTranscript: (editing: boolean) => void;
  isListening: boolean;
  speaking: boolean;
  wordCount: number;
  selectedLanguage: string;
  onTranscriptChange: (transcript: string) => void;
  onWordCountChange: (count: number) => void;
  onLanguageErrorClear: () => void;
}

const TranscriptEditor: React.FC<TranscriptEditorProps> = ({
  transcript,
  editedTranscript,
  setEditedTranscript,
  isEditingTranscript,
  setIsEditingTranscript,
  isListening,
  speaking,
  wordCount,
  selectedLanguage,
  onTranscriptChange,
  onWordCountChange,
  onLanguageErrorClear
}) => {
  const transcriptRef = useRef<HTMLDivElement>(null);
  const editableTranscriptRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll transcript to bottom when it gets too long
  useEffect(() => {
    if (transcriptRef.current && transcript) {
      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;
    }
  }, [transcript]);

  // Toggle between edit and view modes for the transcript
  const toggleTranscriptEditMode = () => {
    if (isEditingTranscript) {
      // Save the edited transcript
      if (editedTranscript.trim() !== "") {
        onTranscriptChange(editedTranscript);
      }
      setIsEditingTranscript(false);
    } else {
      // Enter edit mode
      setIsEditingTranscript(true);

      // Focus the editable textarea after a short delay to ensure it's rendered
      setTimeout(() => {
        if (editableTranscriptRef.current) {
          editableTranscriptRef.current.focus();
        }
      }, 50);
    }
  };

  // Handle changes to the editable transcript
  const handleTranscriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedTranscript(e.target.value);

    // Update word count
    const words = e.target.value.trim().split(/\s+/).filter(word => word !== "");
    onWordCountChange(words.length);

    // Clear any language error when the user edits the transcript
    onLanguageErrorClear();
  };

  const getLanguageColor = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return "purple";
      case "Telugu":
        return "green";
      case "Kannada":
        return "orange";
      default:
        return "blue";
    }
  };

  const getLanguageText = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return {
          editTranscript: "டிரான்ஸ்கிரிப்டைத் திருத்து",
          saveTranscript: "டிரான்ஸ்கிரிப்டைச் சேமி",
          listening: "கேட்கிறது...",
          speaking: "பேசுகிறது...",
          wordsDetected: "வார்த்தைகள் கண்டறியப்பட்டன"
        };
      case "Telugu":
        return {
          editTranscript: "ట్రాన్స్‌క్రిప్ట్‌ను సవరించండి",
          saveTranscript: "ట్రాన్స్‌క్రిప్ట్‌ను సేవ్ చేయండి",
          listening: "వింటోంది...",
          speaking: "మాట్లాడుతోంది...",
          wordsDetected: "పదాలు గుర్తించబడ్డాయి"
        };
      case "Kannada":
        return {
          editTranscript: "ಟ್ರಾನ್ಸ್‌ಕ್ರಿಪ್ಟ್ ಸಂಪಾದಿಸಿ",
          saveTranscript: "ಟ್ರಾನ್ಸ್‌ಕ್ರಿಪ್ಟ್ ಉಳಿಸಿ",
          listening: "ಕೇಳುತ್ತಿದೆ...",
          speaking: "ಮಾತನಾಡುತ್ತಿದೆ...",
          wordsDetected: "ಪದಗಳನ್ನು ಪತ್ತೆಮಾಡಲಾಗಿದೆ"
        };
      default:
        return {
          editTranscript: "Edit transcript",
          saveTranscript: "Save transcript",
          listening: "Listening...",
          speaking: "Speaking...",
          wordsDetected: "words detected"
        };
    }
  };

  if (!isListening && !transcript && !isEditingTranscript) {
    return null;
  }

  const color = getLanguageColor();
  const text = getLanguageText();

  return (
    <div className={`mb-3 p-3 bg-${color}-50 dark:bg-${color}-900/20 rounded-lg border border-${color}-200 dark:border-${color}-700`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {isListening && speaking && (
            <PiWaveform className={`w-4 h-4 text-${color}-500 animate-pulse`} />
          )}
          <span className={`text-sm font-medium text-${color}-700 dark:text-${color}-300`}>
            {isListening ? (speaking ? text.speaking : text.listening) : `${wordCount} ${text.wordsDetected}`}
          </span>
        </div>
        
        {transcript && !isListening && (
          <button
            onClick={toggleTranscriptEditMode}
            className={`p-1 text-${color}-500 hover:text-${color}-600 transition-colors`}
            title={isEditingTranscript ? text.saveTranscript : text.editTranscript}
          >
            {isEditingTranscript ? (
              <PiCheck className="w-4 h-4" />
            ) : (
              <PiPencilSimple className="w-4 h-4" />
            )}
          </button>
        )}
      </div>

      {isEditingTranscript ? (
        <textarea
          ref={editableTranscriptRef}
          value={editedTranscript}
          onChange={handleTranscriptChange}
          className={`
            w-full p-2 border border-${color}-300 rounded-lg resize-none
            focus:outline-none focus:ring-2 focus:ring-${color}-500 focus:border-transparent
            dark:bg-gray-700 dark:border-${color}-600 dark:text-white
          `}
          rows={3}
          placeholder={`Type in ${selectedLanguage}...`}
        />
      ) : (
        <div
          ref={transcriptRef}
          className={`
            max-h-20 overflow-y-auto p-2 bg-white dark:bg-gray-700 rounded-lg border border-${color}-300 dark:border-${color}-600
            text-sm text-gray-900 dark:text-gray-100
          `}
        >
          {transcript || "..."}
        </div>
      )}
    </div>
  );
};

export default TranscriptEditor;
