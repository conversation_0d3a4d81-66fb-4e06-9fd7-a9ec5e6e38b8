#!/usr/bin/env python3
"""
Test script to verify the enhanced language optimization for regional languages.
This script tests that queries in Tamil, Telugu, Kannada, and Oriya with matching
index data skip translation services for improved performance.
"""

import requests
import json
import time
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:5010"
ENDPOINT = "/financial_query"

def test_language_optimization():
    """Test the language optimization for all regional languages."""
    
    print("🚀 Testing Enhanced Language Optimization")
    print("=" * 60)
    
    # Test cases for each regional language
    test_cases = [
        {
            "name": "Tamil Query with Tamil Index",
            "query": "பணம் பற்றி சொல்லுங்கள்",
            "index_name": "tamil",
            "expected_optimization": True,
            "language": "Tamil"
        },
        {
            "name": "Telugu Query with Telugu Index", 
            "query": "డబ్బు గురించి చెప్పండి",
            "index_name": "telugu",
            "expected_optimization": True,
            "language": "Telugu"
        },
        {
            "name": "Kannada Query with Kannada Index",
            "query": "ಹಣದ ಬಗ್ಗೆ ಹೇಳಿ",
            "index_name": "kannada", 
            "expected_optimization": True,
            "language": "Kannada"
        },
        {
            "name": "Oriya Query with Oriya Index",
            "query": "ଟଙ୍କା ବିଷୟରେ କୁହନ୍ତୁ",
            "index_name": "oriya",
            "expected_optimization": True,
            "language": "Oriya"
        },
        {
            "name": "English Query with Tamil Index (Cross-language)",
            "query": "Tell me about money",
            "index_name": "tamil",
            "expected_optimization": False,
            "language": "English"
        },
        {
            "name": "Tamil Query with English Index (Cross-language)",
            "query": "பணம் பற்றி சொல்லுங்கள்",
            "index_name": "default",
            "expected_optimization": False,
            "language": "Tamil"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print(f"   Query: {test_case['query']}")
        print(f"   Index: {test_case['index_name']}")
        print(f"   Expected Optimization: {test_case['expected_optimization']}")
        
        # Prepare request
        payload = {
            "query": test_case["query"],
            "index_name": test_case["index_name"],
            "language": test_case["language"]
        }
        
        # Measure response time
        start_time = time.time()
        
        try:
            response = requests.post(f"{BASE_URL}{ENDPOINT}", json=payload, timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                # Check optimization flags
                optimization_applied = data.get("optimized_direct_processing", False)
                performance_optimization = data.get("performance_optimization_applied", False)
                direct_regional = data.get("direct_regional_processing", False)
                query_translated = data.get("query_translated", False)
                data_language = data.get("data_language", "Unknown")
                
                # Analyze cross-language processing
                cross_lang_info = data.get("cross_language_processing", {})
                cross_lang_applied = cross_lang_info.get("applied", False)
                cross_lang_reason = cross_lang_info.get("reason", "Not specified")
                
                # Determine if optimization worked as expected
                optimization_success = optimization_applied == test_case["expected_optimization"]
                
                result = {
                    "test_name": test_case["name"],
                    "success": optimization_success,
                    "response_time": round(response_time, 3),
                    "optimization_applied": optimization_applied,
                    "performance_optimization": performance_optimization,
                    "direct_regional_processing": direct_regional,
                    "query_translated": query_translated,
                    "data_language": data_language,
                    "cross_language_applied": cross_lang_applied,
                    "cross_language_reason": cross_lang_reason,
                    "expected_optimization": test_case["expected_optimization"]
                }
                
                results.append(result)
                
                # Print results
                print(f"   ✅ Response Time: {response_time:.3f}s")
                print(f"   🚀 Optimization Applied: {optimization_applied}")
                print(f"   🌏 Direct Regional Processing: {direct_regional}")
                print(f"   🔄 Query Translated: {query_translated}")
                print(f"   📊 Data Language: {data_language}")
                print(f"   🌐 Cross-language Applied: {cross_lang_applied}")
                print(f"   📝 Cross-language Reason: {cross_lang_reason}")
                
                if optimization_success:
                    print(f"   ✅ OPTIMIZATION TEST PASSED")
                else:
                    print(f"   ❌ OPTIMIZATION TEST FAILED")
                    print(f"      Expected: {test_case['expected_optimization']}, Got: {optimization_applied}")
                
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                results.append({
                    "test_name": test_case["name"],
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "response_time": round(response_time, 3)
                })
                
        except requests.exceptions.RequestException as e:
            end_time = time.time()
            response_time = end_time - start_time
            print(f"   ❌ Request Error: {str(e)}")
            results.append({
                "test_name": test_case["name"],
                "success": False,
                "error": str(e),
                "response_time": round(response_time, 3)
            })
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = sum(1 for r in results if r.get("success", False))
    total_tests = len(results)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Performance analysis
    optimized_times = [r["response_time"] for r in results if r.get("optimization_applied", False)]
    non_optimized_times = [r["response_time"] for r in results if not r.get("optimization_applied", False)]
    
    if optimized_times and non_optimized_times:
        avg_optimized = sum(optimized_times) / len(optimized_times)
        avg_non_optimized = sum(non_optimized_times) / len(non_optimized_times)
        performance_improvement = ((avg_non_optimized - avg_optimized) / avg_non_optimized) * 100
        
        print(f"\n🚀 PERFORMANCE ANALYSIS:")
        print(f"Average Optimized Response Time: {avg_optimized:.3f}s")
        print(f"Average Non-Optimized Response Time: {avg_non_optimized:.3f}s")
        print(f"Performance Improvement: {performance_improvement:.1f}%")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
        print(f"{status} {result['test_name']} ({result['response_time']}s)")
        if not result.get("success", False) and "error" in result:
            print(f"     Error: {result['error']}")
    
    return results

def test_performance_comparison():
    """Compare performance between optimized and non-optimized queries."""
    
    print("\n🏃 Performance Comparison Test")
    print("=" * 40)
    
    # Test same query with and without optimization
    tamil_query = "பணம் பற்றி சொல்லுங்கள்"
    
    # Optimized case: Tamil query with Tamil index
    print("Testing optimized case (Tamil query + Tamil index)...")
    optimized_payload = {
        "query": tamil_query,
        "index_name": "tamil",
        "language": "Tamil"
    }
    
    optimized_times = []
    for i in range(3):  # Run 3 times for average
        start_time = time.time()
        try:
            response = requests.post(f"{BASE_URL}{ENDPOINT}", json=optimized_payload, timeout=30)
            end_time = time.time()
            if response.status_code == 200:
                optimized_times.append(end_time - start_time)
                data = response.json()
                print(f"  Run {i+1}: {end_time - start_time:.3f}s (Optimization: {data.get('optimized_direct_processing', False)})")
        except Exception as e:
            print(f"  Run {i+1}: Error - {e}")
    
    # Non-optimized case: Tamil query with English index (forces translation)
    print("\nTesting non-optimized case (Tamil query + English index)...")
    non_optimized_payload = {
        "query": tamil_query,
        "index_name": "default",
        "language": "Tamil"
    }
    
    non_optimized_times = []
    for i in range(3):  # Run 3 times for average
        start_time = time.time()
        try:
            response = requests.post(f"{BASE_URL}{ENDPOINT}", json=non_optimized_payload, timeout=30)
            end_time = time.time()
            if response.status_code == 200:
                non_optimized_times.append(end_time - start_time)
                data = response.json()
                print(f"  Run {i+1}: {end_time - start_time:.3f}s (Optimization: {data.get('optimized_direct_processing', False)})")
        except Exception as e:
            print(f"  Run {i+1}: Error - {e}")
    
    # Calculate performance improvement
    if optimized_times and non_optimized_times:
        avg_optimized = sum(optimized_times) / len(optimized_times)
        avg_non_optimized = sum(non_optimized_times) / len(non_optimized_times)
        improvement = ((avg_non_optimized - avg_optimized) / avg_non_optimized) * 100
        
        print(f"\n📊 Performance Results:")
        print(f"Optimized Average: {avg_optimized:.3f}s")
        print(f"Non-Optimized Average: {avg_non_optimized:.3f}s")
        print(f"Performance Improvement: {improvement:.1f}%")
        print(f"Time Saved: {avg_non_optimized - avg_optimized:.3f}s per query")

if __name__ == "__main__":
    print("🚀 Enhanced Language Optimization Test Suite")
    print("Testing optimization for Tamil, Telugu, Kannada, and Oriya")
    print("=" * 60)
    
    # Run main optimization tests
    results = test_language_optimization()
    
    # Run performance comparison
    test_performance_comparison()
    
    print("\n✅ Test suite completed!")