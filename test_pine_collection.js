// Test script to verify PINE collection API for fetching user indexes
// This simulates the frontend fetchUserIndexes function

async function testPineCollection() {
    console.log("🧪 Testing PINE Collection API for FAISS indexes...");
    console.log("=" * 50);

    // Test emails to try
    const testEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];

    for (const userEmail of testEmails) {
        console.log(`\n👤 Testing with email: ${userEmail}`);
        
        try {
            // Always start with both default indexes
            const allIndexes = ['default', 'default1'];
            console.log(`🎯 Starting with default indexes: [${allIndexes.join(', ')}]`);

            // Call the PINE collection API to filter by client email
            const url = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(userEmail.trim())}`;
            console.log(`🔗 API URL: ${url}`);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'xxxid': 'FAISS'
                }
            });

            console.log(`📡 Response status: ${response.status}`);

            if (response.ok) {
                const data = await response.json();
                console.log(`📋 Response data:`, JSON.stringify(data, null, 2));

                if (data.statusCode === 200 && Array.isArray(data.source) && data.source.length > 0) {
                    // Extract index names from the PINE collection data
                    const userIndexes = [];
                    
                    console.log(`📦 Processing ${data.source.length} source records...`);
                    
                    data.source.forEach((jsonStr, index) => {
                        try {
                            const record = JSON.parse(jsonStr);
                            console.log(`   Record ${index + 1}:`, record);
                            
                            // Extract index_name from the record
                            if (record.index_name && 
                                !userIndexes.includes(record.index_name) && 
                                record.index_name !== 'default' && 
                                record.index_name !== 'default1') {
                                userIndexes.push(record.index_name);
                                console.log(`   ✅ Added user index: ${record.index_name}`);
                            } else if (record.index_name) {
                                console.log(`   ⏭️ Skipped index: ${record.index_name} (duplicate or default)`);
                            }
                        } catch (parseError) {
                            console.error(`   ❌ Error parsing record ${index + 1}:`, parseError);
                        }
                    });

                    if (userIndexes.length > 0) {
                        // Add user-specific indexes to the default indexes
                        allIndexes.push(...userIndexes);
                        console.log(`✅ Retrieved ${userIndexes.length} user-specific indexes: [${userIndexes.join(', ')}]`);
                    } else {
                        console.log(`ℹ️ No user-specific indexes found, using defaults only`);
                    }
                } else {
                    console.log(`ℹ️ No data found in PINE collection for ${userEmail}`);
                    console.log(`   StatusCode: ${data.statusCode}`);
                    console.log(`   Source length: ${data.source ? data.source.length : 'N/A'}`);
                }
            } else {
                console.warn(`⚠️ PINE collection API failed: ${response.status} ${response.statusText}`);
                const errorText = await response.text();
                console.log(`   Error response: ${errorText}`);
            }

            console.log(`🎯 Final available indexes for ${userEmail}: [${allIndexes.join(', ')}]`);
            
        } catch (error) {
            console.error(`❌ Error testing ${userEmail}:`, error);
        }
    }

    console.log("\n🏁 Test completed!");
}

// Test without user email (should return default indexes only)
async function testWithoutEmail() {
    console.log("\n🧪 Testing without user email...");
    const allIndexes = ['default', 'default1'];
    console.log(`✅ Should return default indexes: [${allIndexes.join(', ')}]`);
    return allIndexes;
}

// Run tests
async function runAllTests() {
    console.log("🚀 Starting PINE Collection Tests...");
    
    await testWithoutEmail();
    await testPineCollection();
    
    console.log("\n✨ All tests completed!");
}

// Check if running in Node.js or browser
if (typeof window === 'undefined') {
    // Node.js environment
    const fetch = require('node-fetch');
    runAllTests().catch(console.error);
} else {
    // Browser environment
    runAllTests().catch(console.error);
}