import { create } from "zustand";
import { baseUrl, uid } from "../components/api/api";


export interface UploadedFile {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

export interface UploadedURL {
  url: string;
  type: 'youtube' | 'article';
}

export interface Message {
  isUser: boolean;
  text: string | any;
  timestamp: string;
  messageId?: string;
  uploadedFiles?: UploadedFile[];
  uploadedURLs?: UploadedURL[];
}

export interface Chat {
  id: string;
  title: string;
  createdAt: string;
  messages: Message[];
  indexUsed?: string; // Store which FAISS index was used for this chat
  embedModel?: string; // Store which embedding model was used for this chat
}

interface ChatState {
  chatList: Chat[];
  userQuery: string;
  isLoading: boolean;
  isAnimation: boolean;
  updateChatList: () => Promise<void>;
  handleSubmit: (userQuery: string, chatId: string, aiResponse?: any, options?: { index?: string, embedModel?: string }) => void;
  addMessage: (message: Message, chatId: string) => void;
  addChat: (chat: Chat) => void;
  setUserQuery: (query: string) => void;
  setIsLoading: (isLoading: boolean) => void;
}


const syncToServer = async (chatList: Chat[]) => {

  const resultUser = JSON.parse(sessionStorage.getItem("resultUser") || '{}');
  const userId = resultUser._id?.$oid;
  const username = resultUser.username;
  const mobileno = resultUser.mobileno;

  if (!userId) return;

  try {
    // Step 1: Fetch existing data
    const fetchRes = await fetch(`${baseUrl}/eSearch?userId=${userId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "xxxid": uid,
      },
    });

    const fetchData = await fetchRes.json();

    // Step 2: Only delete resources that have the `chats` key
    if (Array.isArray(fetchData?.source)) {
      for (const item of fetchData.source) {
        try {
          const parsed = JSON.parse(item);
          const resourceId = parsed?._id?.$oid;
          const hasChats = Array.isArray(parsed?.chats);

          if (resourceId && hasChats) {
            await fetch(`${baseUrl}/eDelete?resourceId=${resourceId}&userId=${userId}`, {
              method: "DELETE",
              headers: {
                "Content-Type": "application/json",
                "xxxid": uid,
              },
            });
          }
        } catch (err) {
          console.warn("Skipping invalid or non-chat item:", item);
        }
      }
    }

    // Step 3: Save new chat list with extra user info
    const bodyData = {
      chats: chatList,
      username: username || "",      // fallback to empty string if missing
      mobileno: mobileno || "",      // fallback to empty string if missing
    };

    await fetch(`${baseUrl}/eCreate?userId=${userId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "xxxid": uid,
      },
      body: JSON.stringify(bodyData),
    });
  } catch (error) {
    console.error("Failed to sync chats to server:", error);
  }
};


export const useChatHandler = create<ChatState>()((set, get) => ({
  chatList: [],
  userQuery: "",
  isLoading: false,
  isAnimation: false,


  updateChatList: async () => {
    // Only run on client side to prevent hydration errors
    if (typeof window === 'undefined') return;

    const resultUser = JSON.parse(sessionStorage.getItem("resultUser") || '{}');
    const userId = resultUser._id?.$oid;
    if (!userId) return;

    try {
      const response = await fetch(`${baseUrl}/eSearch?userId=${userId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "xxxid": uid,
        },
      });

      const data = await response.json();
      const seenChatIds = new Set();
      const chatList: Chat[] = [];

      if (Array.isArray(data?.source)) {
        for (const item of data.source) {
          try {
            const parsed = JSON.parse(item);
            const chats = parsed?.chats;

            if (Array.isArray(chats)) {
              for (const chat of chats) {
                if (!seenChatIds.has(chat.id)) {
                  seenChatIds.add(chat.id);

                  // Here is the key fix:
                  const processedMessages = (chat.messages || []).map((msg: Message) => {
                    if (!msg.isUser && msg.text && typeof msg.text === "object" && "ai_response" in msg.text) {
                      // Preserve the full structured AI response object
                      return {
                        ...msg,
                        text: {
                          ai_response: msg.text.ai_response,
                          sentence_analysis: msg.text.sentence_analysis || [],
                          related_questions: msg.text.related_questions || [],
                        },
                      };
                    } else {
                      // Leave user messages or plain strings unchanged
                      return msg;
                    }
                  });


                  chatList.push({
                    id: chat.id,
                    title: chat.title || "Untitled",
                    createdAt: chat.createdAt || new Date().toISOString(),
                    messages: processedMessages,
                    indexUsed: chat.indexUsed || 'default', // Preserve the index used for this chat
                  });
                }
              }
            }
          } catch (err) {
            console.warn("Failed to parse one chat source item:", item);
          }
        }
      }

      set((state) => {
        // Preserve any recently added chats that might not be on the server yet
        const serverChatIds = new Set(chatList.map(chat => chat.id));
        const recentLocalChats = state.chatList.filter(chat => {
          // Keep chats that are not on the server and were created in the last 5 minutes
          if (serverChatIds.has(chat.id)) return false;

          const chatAge = Date.now() - new Date(chat.createdAt).getTime();
          return chatAge < 5 * 60 * 1000; // 5 minutes
        });

        // Merge server chats with recent local chats
        const mergedChatList = [...recentLocalChats, ...chatList];

        return {
          ...state,
          chatList: mergedChatList,
        };
      });
    } catch (err) {
      console.error("Failed to fetch chat data:", err);
    }
  },



  handleSubmit: (userQuery, chatId, aiResponse, options = {}) => {
    const timestamp = new Date().toISOString();
    let messageText: string;
    let sentenceAnalysis = [];
    let relatedQuestions = [];

    // Get the current index being used
    const currentIndex = options?.index ||
      (typeof window !== 'undefined' ? (localStorage.getItem('faiss_index_name') || localStorage.getItem('selectedFaissIndex')) : null) ||
      'default';

    // Store the selected index in localStorage if provided in options
    if (options?.index && typeof window !== 'undefined') {
      localStorage.setItem('faiss_index_name', options.index);
      localStorage.setItem('selectedFaissIndex', options.index);
      console.log(`Stored selected index in localStorage: ${options.index}`);

      // If this is just an index selection with no real query, return early without creating a message
      if (userQuery === "text" || !userQuery) {
        console.log("Skipping message creation for index selection");
        return;
      }
    }

    if (aiResponse === undefined || aiResponse === null) {
      messageText = "Sorry, there was an issue with the response.";
    } else if (typeof aiResponse === "string") {
      messageText = aiResponse;
    } else if (typeof aiResponse === "object") {
      if ("sentence_analysis" in aiResponse && Array.isArray(aiResponse.sentence_analysis)) {
        sentenceAnalysis = aiResponse.sentence_analysis;
      }
      if ("related_questions" in aiResponse && Array.isArray(aiResponse.related_questions)) {
        relatedQuestions = aiResponse.related_questions;
      }

      if ("ai_response" in aiResponse) {
        const response = aiResponse.ai_response;
        messageText = typeof response === "string" ? response : JSON.stringify(response);
      } else {
        try {
          messageText = JSON.stringify(aiResponse);
        } catch (e) {
          messageText = "Error: Could not process the response.";
        }
      }
    } else {
      messageText = String(aiResponse);
    }

    set((state) => {
      const existingChatIndex = state.chatList.findIndex((chat) => chat.id === chatId);

      if (existingChatIndex !== -1) {
        const updatedChatList = [...state.chatList];
        const messages = updatedChatList[existingChatIndex].messages;
        const loadingMessageIndex = messages.findIndex((msg) => !msg.isUser && msg.text === "__LOADING__");

        if (loadingMessageIndex !== -1) {
          const loadingMessage = messages[loadingMessageIndex];
          messages[loadingMessageIndex] = {
            isUser: false,
            text: {
              ai_response: messageText,
              sentence_analysis: sentenceAnalysis,
              related_questions: relatedQuestions,
            },
            timestamp,
            messageId: loadingMessage.messageId,
          };
        } else {
          messages.push({
            isUser: false,
            text: {
              ai_response: messageText,
              sentence_analysis: sentenceAnalysis,
              related_questions: relatedQuestions,
            },
            timestamp,
          });
        }

        const newState = {
          ...state,
          chatList: updatedChatList,
        };
        syncToServer(newState.chatList);
        return newState;
      } else {
        const chatTitle = userQuery
          ? userQuery.slice(0, 30) + (userQuery.length > 30 ? "..." : "")
          : "New Chat";

        const newChat: Chat = {
          id: chatId,
          title: chatTitle,
          createdAt: timestamp,
          indexUsed: currentIndex, // Store the index used for this chat
          messages: [
            {
              isUser: false,
              text: {
                ai_response: messageText,
                sentence_analysis: sentenceAnalysis,
                related_questions: relatedQuestions,
              },
              timestamp,
            },
          ],
        };

        const newState = {
          ...state,
          chatList: [newChat, ...state.chatList],
        };
        syncToServer(newState.chatList);
        return newState;
      }
    });
  },




  addMessage: (message, chatId) => {
    set((state) => {
      const existingChatIndex = state.chatList.findIndex((chat) => chat.id === chatId);

      if (existingChatIndex !== -1) {
        const updatedChatList = [...state.chatList];
        updatedChatList[existingChatIndex].messages.push(message);

        return {
          ...state,
          chatList: updatedChatList,
        };
      } else {
        // Get the current index for new chats
        const currentIndex = typeof window !== 'undefined'
          ? localStorage.getItem('faiss_index_name') || localStorage.getItem('selectedFaissIndex') || 'default'
          : 'default';

        const newChat: Chat = {
          id: chatId,
          title:
            typeof message.text === "string"
              ? message.text.slice(0, 30) + (message.text.length > 30 ? "..." : "")
              : "New Chat",
          createdAt: message.timestamp,
          indexUsed: currentIndex, // Store the index used for this chat
          messages: [message],
        };

        return {
          ...state,
          chatList: [newChat, ...state.chatList],
        };
      }
    });
  },


  addChat: (chat) => {
    set((state) => {
      // Check if chat already exists to avoid duplicates
      const existingChatIndex = state.chatList.findIndex(existingChat => existingChat.id === chat.id);

      let newChatList;
      if (existingChatIndex !== -1) {
        // Update existing chat
        newChatList = [...state.chatList];
        newChatList[existingChatIndex] = chat;
      } else {
        // Add new chat to the beginning
        newChatList = [chat, ...state.chatList];
      }

      const newState = {
        ...state,
        chatList: newChatList,
      };

      // Sync to server asynchronously
      syncToServer(newState.chatList).catch(error => {
        console.error("Failed to sync new chat to server:", error);
      });

      return newState;
    });
  },

  setUserQuery: (query) => set({ userQuery: query }),
  setIsLoading: (isLoading) => set({ isLoading }),
}));
