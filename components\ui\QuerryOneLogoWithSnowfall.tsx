'use client';

import React, { useEffect, useRef } from 'react';
import Image from 'next/image';
import fav from '@/public/images/favicon.png';

const AIQuillLogoWithSnowfall: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Only run on client side to prevent hydration errors
    if (typeof window === 'undefined') return;

    // Create snowfall effect
    const container = containerRef.current;
    if (!container) return;

    const createSnowflake = () => {
      const snowflake = document.createElement('div');
      const size = Math.random() * 4 + 1; // 1-5px

      snowflake.style.position = 'absolute';
      snowflake.style.width = `${size}px`;
      snowflake.style.height = `${size}px`;
      snowflake.style.backgroundColor = 'white';
      snowflake.style.borderRadius = '50%';
      snowflake.style.opacity = `${Math.random() * 0.7 + 0.3}`;
      snowflake.style.pointerEvents = 'none';

      // Random starting position
      snowflake.style.left = `${Math.random() * 100}%`;
      snowflake.style.top = '-5px';

      // Add animation
      snowflake.style.animation = `snowfall ${Math.random() * 3 + 5}s linear forwards`;

      container.appendChild(snowflake);

      // Remove snowflake after animation completes
      setTimeout(() => {
        snowflake.remove();
      }, 8000);
    };

    // Create snowflakes at intervals
    const interval = setInterval(() => {
      createSnowflake();
    }, 200);

    // Initial snowflakes
    for (let i = 0; i < 10; i++) {
      setTimeout(() => {
        createSnowflake();
      }, i * 300);
    }

    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="relative overflow-hidden bg-[#1a2231] p-4 rounded-md"
      style={{ minHeight: '60px', minWidth: '200px' }}
    >
      <style jsx>{`
        @keyframes snowfall {
          0% {
            transform: translateY(0) translateX(0);
            opacity: 0.3;
          }
          10% {
            opacity: 0.8;
          }
          90% {
            opacity: 0.7;
          }
          100% {
            transform: translateY(60px) translateX(20px);
            opacity: 0;
          }
        }
      `}</style>

      <div className="flex justify-start items-center gap-1.5 relative z-10">
        <div className="relative">
          <Image
            src={fav}
            alt="AIQuill Logo"
            width={32}
            height={32}
            className="relative z-10"
          />
        </div>
        <span className="text-2xl font-semibold text-gray-200">
          QueryOne
        </span>
      </div>
    </div>
  );
};

export default AIQuillLogoWithSnowfall;
