#!/usr/bin/env python3
"""
Test script to verify the /financial_query endpoint is working with FAISS
"""

import requests
import json
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_financial_query_endpoint():
    """Test the /financial_query endpoint"""
    
    print("🧪 Testing /financial_query endpoint with FAISS")
    print("=" * 60)
    
    # Test data
    test_query = {
        "query": "What is artificial intelligence?",
        "index_name": "default",
        "language": "English"
    }
    
    # Test URL (server is running on localhost:5010)
    url = "http://localhost:5010/financial_query"
    
    try:
        print(f"📡 Sending request to: {url}")
        print(f"📝 Query data: {json.dumps(test_query, indent=2)}")
        
        response = requests.post(url, json=test_query, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS! Endpoint is working")
            print(f"🔍 Query: {result.get('query', 'N/A')}")
            print(f"🏗️ Search Engine: {result.get('search_engine', 'N/A')}")
            print(f"📚 Index Used: {result.get('index_used', 'N/A')}")
            print(f"📄 Documents Found: {len(result.get('retrieved_documents', []))}")
            print(f"🤖 AI Response Length: {len(result.get('ai_response', ''))}")
            
            if result.get('retrieved_documents'):
                print(f"📋 First Document Score: {result['retrieved_documents'][0].get('score', 'N/A')}")
                
        elif response.status_code == 404:
            print("❌ 404 Error: Endpoint not found")
            print("🔧 This suggests the server is not running or the endpoint path is incorrect")
            
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Server is not running")
        print("🔧 Please start the Flask server first:")
        print("   cd python-fiass-backend")
        print("   python full_code.py")
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")

def test_server_health():
    """Test if the server is running"""
    
    print("\n🏥 Testing server health")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:5010/api/health", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Server is running and healthy")
            print(f"📊 Available endpoints: {len(health_data.get('data', {}).get('available_endpoints', []))}")
            
            endpoints = health_data.get('data', {}).get('available_endpoints', [])
            if '/financial_query' in endpoints:
                print("✅ /financial_query endpoint is registered")
            else:
                print("❌ /financial_query endpoint is NOT registered")
                print(f"📋 Available endpoints: {endpoints}")
        else:
            print(f"⚠️ Server responded with status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running")
        return False
        
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False
        
    return True

if __name__ == "__main__":
    # First check if server is running
    if test_server_health():
        # If server is running, test the financial_query endpoint
        test_financial_query_endpoint()
    else:
        print("\n🚀 To start the server, run:")
        print("   cd python-fiass-backend")
        print("   python full_code.py")