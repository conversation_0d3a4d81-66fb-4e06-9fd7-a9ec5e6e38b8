#!/usr/bin/env python3
"""
Test script to verify Tamil Excel upload functionality
"""

import requests
import json
import os

# Configuration
BASE_URL = os.getenv("BACKEND_URL", "http://localhost:5010")
EXCEL_FILE_PATH = "dinamalar_dataset_first_500.xlsx"
CLIENT_EMAIL = "<EMAIL>"
INDEX_NAME = "tamil_test_index"

def test_excel_upload():
    """Test Excel file upload with Tamil content"""

    print("🧪 Testing Tamil Excel Upload Functionality")
    print("=" * 50)

    # Check if Excel file exists
    if not os.path.exists(EXCEL_FILE_PATH):
        print(f"❌ Excel file not found: {EXCEL_FILE_PATH}")
        return False

    print(f"📁 Found Excel file: {EXCEL_FILE_PATH}")

    # Prepare upload data
    upload_url = f"{BASE_URL}/api/upload-excel"

    files = {
        'file': open(EXCEL_FILE_PATH, 'rb')
    }

    data = {
        'client_email': CLIENT_EMAIL,
        'index_name': INDEX_NAME,
        'update_mode': 'new',  # Force new upload
        'embed_model': 'all-MiniLM-L6-v2'
    }

    print(f"📤 Uploading to: {upload_url}")
    print(f"📧 Client email: {CLIENT_EMAIL}")
    print(f"🏷️  Index name: {INDEX_NAME}")
    print(f"📋 Form data: {data}")

    try:
        # Make the upload request
        response = requests.post(upload_url, files=files, data=data)

        print(f"📊 Response status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ Upload successful!")
            print(f"📝 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ Upload failed with status {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error during upload: {e}")
        return False

    finally:
        files['file'].close()

def test_excel_upload_without_client_email():
    """Test Excel file upload without client_email to test fallback"""

    print("\n🧪 Testing Excel Upload WITHOUT client_email (fallback test)")
    print("=" * 60)

    # Check if Excel file exists
    if not os.path.exists(EXCEL_FILE_PATH):
        print(f"❌ Excel file not found: {EXCEL_FILE_PATH}")
        return False

    print(f"📁 Found Excel file: {EXCEL_FILE_PATH}")

    # Prepare upload data WITHOUT client_email
    upload_url = f"{BASE_URL}/api/upload-excel"

    files = {
        'file': open(EXCEL_FILE_PATH, 'rb')
    }

    data = {
        'index_name': INDEX_NAME + "_fallback",
        'update_mode': 'new',  # Force new upload
        'embed_model': 'all-MiniLM-L6-v2'
        # Intentionally NOT including client_email
    }

    print(f"📤 Uploading to: {upload_url}")
    print(f"📋 Form data (no client_email): {data}")

    try:
        # Make the upload request
        response = requests.post(upload_url, files=files, data=data)

        print(f"📊 Response status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ Upload successful with fallback!")
            print(f"📝 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ Upload failed with status {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error during upload: {e}")
        return False

    finally:
        files['file'].close()

def test_list_excel_files():
    """Test listing Excel files"""
    
    print("\n🧪 Testing List Excel Files")
    print("=" * 30)
    
    list_url = f"{BASE_URL}/api/list-excel-files"
    
    # Test with client email filter
    params = {'client_email': CLIENT_EMAIL}
    
    try:
        response = requests.get(list_url, params=params)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ List successful!")
            print(f"📝 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ List failed with status {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during list: {e}")
        return False

def test_search_tamil_content():
    """Test searching Tamil content"""
    
    print("\n🧪 Testing Tamil Content Search")
    print("=" * 35)
    
    search_url = f"{BASE_URL}/api/search"
    
    # Tamil search query
    tamil_query = "தமிழ்"
    
    search_data = {
        'query': tamil_query,
        'index_name': INDEX_NAME,
        'k': 5
    }
    
    print(f"🔍 Searching for: {tamil_query}")
    
    try:
        response = requests.post(search_url, json=search_data)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Search successful!")
            print(f"📝 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ Search failed with status {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during search: {e}")
        return False

def main():
    """Run all tests"""

    print("🚀 Starting Tamil Excel Upload Tests")
    print("=" * 60)

    # Test 1: Upload Excel file with client_email
    upload_success = test_excel_upload()

    # Test 1b: Upload Excel file without client_email (fallback test)
    fallback_success = test_excel_upload_without_client_email()

    # Test 2: List Excel files
    list_success = test_list_excel_files()

    # Test 3: Search Tamil content (only if upload was successful)
    search_success = False
    if upload_success or fallback_success:
        search_success = test_search_tamil_content()

    # Summary
    print("\n📋 Test Summary")
    print("=" * 20)
    print(f"📤 Upload (with email): {'✅ PASS' if upload_success else '❌ FAIL'}")
    print(f"📤 Upload (fallback): {'✅ PASS' if fallback_success else '❌ FAIL'}")
    print(f"📋 List: {'✅ PASS' if list_success else '❌ FAIL'}")
    print(f"🔍 Search: {'✅ PASS' if search_success else '❌ FAIL'}")

    if (upload_success or fallback_success) and list_success:
        print("\n🎉 Tamil Excel functionality is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the server logs.")

if __name__ == "__main__":
    main()
