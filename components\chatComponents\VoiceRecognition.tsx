import React, { useRef, useEffect } from 'react';
import { PiMicrophone, PiStop, PiWaveform } from 'react-icons/pi';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';

interface Language {
  name: string;
  code: string;
  color: string;
}

interface VoiceRecognitionProps {
  selectedLanguage: string;
  isListening: boolean;
  setIsListening: (listening: boolean) => void;
  speaking: boolean;
  setSpeaking: (speaking: boolean) => void;
  wordCount: number;
  setWordCount: (count: number) => void;
  recentWords: string[];
  setRecentWords: (words: string[]) => void;
  transcript: string;
  setTranscript: (transcript: string) => void;
  onTranscriptChange: (transcript: string) => void;
  disabled?: boolean;
}

const VoiceRecognition: React.FC<VoiceRecognitionProps> = ({
  selectedLanguage,
  isListening,
  setIsListening,
  speaking,
  setSpeaking,
  wordCount,
  setWordCount,
  recentWords,
  setR<PERSON>entWords,
  transcript,
  setTranscript,
  onTranscriptChange,
  disabled = false
}) => {
  // Speech detection timer
  const speakingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Available languages for voice input with their language codes - English, Tamil, Telugu, and Kannada
  const languages: Language[] = [
    { name: "English", code: "en-US", color: "blue" },
    { name: "Tamil", code: "ta-IN", color: "purple" },
    { name: "Telugu", code: "te-IN", color: "green" },
    { name: "Kannada", code: "kn-IN", color: "orange" }
  ];

  // Get the language code for the selected language
  const getLanguageCode = () => {
    const language = languages.find(lang => lang.name === selectedLanguage);
    return language ? language.code : "en-US"; // Default to English if not found
  };

  // Initialize with default values for server-side rendering
  const [clientTranscript, setClientTranscript] = React.useState("");
  const [listening, setListening] = React.useState(false);
  const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = React.useState(false);
  const [isMicrophoneAvailable, setIsMicrophoneAvailable] = React.useState(false);

  // Speech recognition setup - only run on client side
  const {
    transcript: speechTranscript,
    listening: speechListening,
    resetTranscript,
    browserSupportsSpeechRecognition: speechBrowserSupport,
    isMicrophoneAvailable: speechMicrophoneAvailable,
  } = typeof window !== 'undefined' ? useSpeechRecognition({
    clearTranscriptOnListen: false,
    transcribing: true,
    commands: [
      {
        command: '*',
        callback: (command) => {
          console.log('Voice command detected:', command);
        }
      }
    ]
  }) : {
    transcript: "",
    listening: false,
    resetTranscript: () => {},
    browserSupportsSpeechRecognition: false,
    isMicrophoneAvailable: false
  };

  // Update client state when speech recognition changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setClientTranscript(speechTranscript);
      setListening(speechListening);
      setBrowserSupportsSpeechRecognition(speechBrowserSupport);
      setIsMicrophoneAvailable(speechMicrophoneAvailable);
    }
  }, [speechTranscript, speechListening, speechBrowserSupport, speechMicrophoneAvailable]);

  // Update transcript when client transcript changes
  useEffect(() => {
    if (clientTranscript !== transcript) {
      setTranscript(clientTranscript);
      onTranscriptChange(clientTranscript);
    }
  }, [clientTranscript, transcript, setTranscript, onTranscriptChange]);

  // Update word count when transcript changes
  useEffect(() => {
    if (transcript) {
      const words = transcript.trim().split(/\s+/).filter(word => word !== "");
      setWordCount(words.length);

      // Track most recent words for animation
      if (words.length > 0) {
        const lastWord = words[words.length - 1];
        if (lastWord && lastWord.length > 0) {
          setRecentWords((prev: string[]) => {
            const newWords = [...prev, lastWord];
            return newWords.slice(-5); // Keep only the last 5 words
          });
        }
      }

      // Set speaking state when new words are detected
      if (isListening) {
        setSpeaking(true);

        // Clear previous timer if it exists
        if (speakingTimerRef.current) {
          clearTimeout(speakingTimerRef.current);
        }

        // Set a timer to detect when speaking has paused
        speakingTimerRef.current = setTimeout(() => {
          setSpeaking(false);
        }, 1500); // 1.5 seconds of silence is considered a pause
      }
    }
  }, [transcript, isListening, setWordCount, setRecentWords, setSpeaking]);

  // Update listening state when speech recognition status changes
  useEffect(() => {
    if (typeof window === 'undefined') return; // Skip on server-side

    console.log("Speech recognition listening state changed:", listening);

    if (isListening !== listening) {
      setIsListening(listening);
    }

    if (!listening && isListening) {
      console.warn("Speech recognition stopped unexpectedly. Checking if we should restart...");

      // Only attempt to restart if we're still supposed to be listening
      // This prevents infinite restart loops
      const restartTimeout = setTimeout(() => {
        if (isListening && typeof window !== 'undefined') {
          console.log("Attempting to restart speech recognition...");

          // Try to restart speech recognition
          const languageCode = getLanguageCode();
          SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });
        }
      }, 1000);

      return () => clearTimeout(restartTimeout);
    }
  }, [listening, isListening, setIsListening]);

  // Effect to handle language changes
  useEffect(() => {
    if (typeof window === 'undefined') return; // Skip on server-side

    console.log(`Language changed to: ${selectedLanguage}`);

    // If we're listening, we need to restart with the new language
    if (isListening) {
      const restartWithNewLanguage = async () => {
        try {
          await SpeechRecognition.stopListening();
          await new Promise(resolve => setTimeout(resolve, 300));

          const languageCode = getLanguageCode();
          await SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });

          console.log(`Restarted speech recognition with language: ${selectedLanguage} (${languageCode})`);
        } catch (error) {
          console.error("Error restarting speech recognition with new language:", error);
        }
      };

      restartWithNewLanguage();
    }
  }, [selectedLanguage, isListening]);

  // Start listening with the selected language
  const startListening = async () => {
    // Skip on server-side
    if (typeof window === 'undefined') {
      console.warn("Attempted to start listening on server-side");
      return;
    }

    const languageCode = getLanguageCode();
    console.log(`Starting speech recognition in ${selectedLanguage} (${languageCode})`);

    try {
      setIsListening(true);

      await new Promise(resolve => setTimeout(resolve, 100));

      if (!browserSupportsSpeechRecognition) {
        console.error("Browser doesn't support speech recognition");
        alert("Your browser doesn't support speech recognition. Please try using Chrome.");
        setIsListening(false);
        return;
      }

      if (!isMicrophoneAvailable) {
        console.error("Microphone is not available");
        alert("Microphone is not available. Please check your microphone permissions.");
        setIsListening(false);
        return;
      }

      await SpeechRecognition.startListening({
        continuous: true,
        language: languageCode,
        interimResults: true
      });

      console.log("Speech recognition started successfully");

      setTimeout(() => {
        if (!listening && typeof window !== 'undefined') {
          console.warn("Speech recognition may not have started properly. Trying again...");
          SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });
        }
      }, 500);

    } catch (error) {
      console.error("Error starting speech recognition:", error);
      setIsListening(false);
      alert("There was an error starting speech recognition. Please try again.");
    }
  };

  // Toggle voice recognition
  const toggleListening = async () => {
    // Skip on server-side
    if (typeof window === 'undefined') {
      console.warn("Attempted to toggle listening on server-side");
      return;
    }

    if (isListening) {
      try {
        await SpeechRecognition.stopListening();
        setIsListening(false); // Explicitly set to false to ensure state is updated

        console.log("Speech recognition stopped successfully");
      } catch (error) {
        console.error("Error stopping speech recognition:", error);
      }
    } else {
      await startListening();
    }
  };

  const getLanguageColor = () => {
    const language = languages.find(l => l.name === selectedLanguage);
    return language ? language.color : 'blue';
  };

  const getMicrophoneColor = () => {
    const color = getLanguageColor();
    if (disabled) return 'text-gray-400';
    
    if (isListening) {
      switch (color) {
        case 'purple':
          return 'text-purple-600';
        case 'green':
          return 'text-green-600';
        case 'orange':
          return 'text-orange-600';
        default:
          return 'text-blue-600';
      }
    }
    
    switch (color) {
      case 'purple':
        return 'text-purple-500 hover:text-purple-600';
      case 'green':
        return 'text-green-500 hover:text-green-600';
      case 'orange':
        return 'text-orange-500 hover:text-orange-600';
      default:
        return 'text-blue-500 hover:text-blue-600';
    }
  };

  return (
    <button
      type="button"
      onClick={toggleListening}
      disabled={disabled}
      className={`p-3 rounded-full transition-all duration-200 transform hover:scale-105 min-w-[48px] min-h-[48px] flex items-center justify-center ${getMicrophoneColor()} ${
        disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm hover:shadow-md'
      }`}
      title={
        disabled
          ? 'Voice input disabled'
          : isListening
            ? 'Stop voice input'
            : 'Start voice input'
      }
    >
      {isListening ? (
        speaking ? (
          <PiWaveform className="w-5 h-5 animate-pulse" />
        ) : (
          <PiStop className="w-5 h-5" />
        )
      ) : (
        <PiMicrophone className="w-5 h-5" />
      )}
    </button>
  );
};

export default VoiceRecognition;
