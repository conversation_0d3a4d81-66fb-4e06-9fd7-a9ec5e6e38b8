import React from 'react';
import { PiFile, PiFileText, PiMusicNote, PiYoutubeLogo, PiLink, PiX } from 'react-icons/pi';

interface UploadedContentDisplayProps {
  uploadedFiles: File[];
  uploadedURLs: Array<{url: string, type: 'youtube' | 'article'}>;
  showUploadedContent: boolean;
  selectedLanguage: string;
  onRemoveFile: (index: number) => void;
  onRemoveURL: (index: number) => void;
}

const UploadedContentDisplay: React.FC<UploadedContentDisplayProps> = ({
  uploadedFiles,
  uploadedURLs,
  showUploadedContent,
  selectedLanguage,
  onRemoveFile,
  onRemoveURL
}) => {
  // Function to get language-specific text for uploaded content display
  const getUploadDisplayText = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return {
          uploadedFiles: 'பதிவேற்றப்பட்ட கோப்புகள்',
          uploadedUrls: 'பதிவேற்றப்பட்ட இணைப்புகள்',
          removeFile: 'கோப்பை அகற்று',
          removeUrl: 'இணைப்பை அகற்று',
          pdfDocument: 'PDF ஆவணம்',
          mp3Audio: 'MP3 ஆடியோ',
          youtubeVideo: 'YouTube வீடியோ',
          articleLink: 'கட்டுரை இணைப்பு'
        };
      case "Telugu":
        return {
          uploadedFiles: 'అప్‌లోడ్ చేసిన ఫైల్‌లు',
          uploadedUrls: 'అప్‌లోడ్ చేసిన లింక్‌లు',
          removeFile: 'ఫైల్‌ను తొలగించండి',
          removeUrl: 'లింక్‌ను తొలగించండి',
          pdfDocument: 'PDF డాక్యుమెంట్',
          mp3Audio: 'MP3 ఆడియో',
          youtubeVideo: 'YouTube వీడియో',
          articleLink: 'వ్యాసం లింక్'
        };
      case "Kannada":
        return {
          uploadedFiles: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಫೈಲ್‌ಗಳು',
          uploadedUrls: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಲಿಂಕ್‌ಗಳು',
          removeFile: 'ಫೈಲ್ ತೆಗೆದುಹಾಕಿ',
          removeUrl: 'ಲಿಂಕ್ ತೆಗೆದುಹಾಕಿ',
          pdfDocument: 'PDF ಡಾಕ್ಯುಮೆಂಟ್',
          mp3Audio: 'MP3 ಆಡಿಯೋ',
          youtubeVideo: 'YouTube ವೀಡಿಯೊ',
          articleLink: 'ಲೇಖನ ಲಿಂಕ್'
        };
      default:
        return {
          uploadedFiles: 'Uploaded Files',
          uploadedUrls: 'Uploaded URLs',
          removeFile: 'Remove file',
          removeUrl: 'Remove URL',
          pdfDocument: 'PDF Document',
          mp3Audio: 'MP3 Audio',
          youtubeVideo: 'YouTube Video',
          articleLink: 'Article Link'
        };
    }
  };

  // Function to get file icon based on file type
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    // Document files
    if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension || '')) {
      return <PiFileText className="w-5 h-5" />;
    }

    // Excel files
    if (['xlsx', 'xls', 'csv'].includes(extension || '')) {
      return <PiFileText className="w-5 h-5" />;
    }

    // Audio files
    if (['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg', 'wma'].includes(extension || '')) {
      return <PiMusicNote className="w-5 h-5" />;
    }

    // Default file icon
    return <PiFile className="w-5 h-5" />;
  };

  // Function to get URL icon based on type
  const getUrlIcon = (type: 'youtube' | 'article') => {
    if (type === 'youtube') {
      return <PiYoutubeLogo className="w-5 h-5" />;
    }
    return <PiLink className="w-5 h-5" />;
  };

  const getLanguageColor = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return "purple";
      case "Telugu":
        return "green";
      case "Kannada":
        return "orange";
      default:
        return "blue";
    }
  };

  if (!showUploadedContent || (uploadedFiles.length === 0 && uploadedURLs.length === 0)) {
    return null;
  }

  const text = getUploadDisplayText();
  const color = getLanguageColor();

  return (
    <div className="mb-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
      {/* Uploaded Files Section */}
      {uploadedFiles.length > 0 && (
        <div className="mb-3">
          <h4 className={`text-sm font-medium text-${color}-700 dark:text-${color}-300 mb-2 flex items-center gap-2`}>
            <PiFile className="w-4 h-4" />
            {text.uploadedFiles}
          </h4>
          <div className="space-y-2">
            {uploadedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center gap-3 p-2 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
              >
                <div className={`text-${color}-500`}>
                  {getFileIcon(file.name)}
                </div>
                <div className="flex-grow min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <button
                  onClick={() => onRemoveFile(index)}
                  className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  title={text.removeFile}
                >
                  <PiX className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Uploaded URLs Section */}
      {uploadedURLs.length > 0 && (
        <div>
          <h4 className={`text-sm font-medium text-${color}-700 dark:text-${color}-300 mb-2 flex items-center gap-2`}>
            <PiLink className="w-4 h-4" />
            {text.uploadedUrls}
          </h4>
          <div className="space-y-2">
            {uploadedURLs.map((urlItem, index) => (
              <div
                key={index}
                className="flex items-center gap-3 p-2 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
              >
                <div className={`text-${color}-500`}>
                  {getUrlIcon(urlItem.type)}
                </div>
                <div className="flex-grow min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {urlItem.type === 'youtube' ? text.youtubeVideo : text.articleLink}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {urlItem.url}
                  </p>
                </div>
                <button
                  onClick={() => onRemoveURL(index)}
                  className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  title={text.removeUrl}
                >
                  <PiX className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default UploadedContentDisplay;
