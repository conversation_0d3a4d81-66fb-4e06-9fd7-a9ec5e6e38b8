# Client Email Filtering Implementation

## Overview

This document describes the complete implementation of client email-based filtering for FAISS indexes. The system ensures that users only see and can access their own uploaded data based on their email address, with full integration into the existing authentication system.

## 🔐 Authentication Integration

### Current Login System
The application uses a standard email/password authentication system:

**Login Process (app/sign-in/page.tsx)**:
1. User enters email and password
2. System authenticates via backend API
3. On success, stores user data:
   ```javascript
   localStorage.setItem("user_email", email.trim());           // Primary email storage
   sessionStorage.setItem("resultUser", JSON.stringify(userData)); // Full user session
   ```

### Email Detection Priority
The system retrieves user email in this exact order:
1. `localStorage.getItem('user_email')` ← **Primary source from login**
2. `sessionStorage.getItem('user_email')` ← **Fallback**
3. `sessionStorage.getItem('resultUser')` ← **Parsed for email/username**

## Architecture

### Backend Components

#### 1. Database Schema Enhancement
- **pine_collection table** now includes:
  - `id`: Primary key
  - `index_name`: FAISS index name
  - `email`: Client email address
  - `file_name`: Original uploaded file name
  - `upload_date`: Timestamp of upload

#### 2. Upload Endpoint Enhancement (`/api/upload-csv`)
- **Validation**: Now requires client email to be provided
- **File Name Tracking**: Stores the original file name in the database
- **Client Association**: Associates each upload with a specific client email

#### 3. Index Listing Endpoint (`/api/list-faiss-indexes`)
- **User Filtering**: Filters indexes based on the requesting user's email
- **Database Integration**: Uses `PineFilterService` for email-based filtering
- **Fallback Support**: Provides default index access for new users

#### 4. PineFilterService
- **Email Validation**: Validates and normalizes email addresses
- **Index Filtering**: Retrieves only indexes accessible to specific users
- **Access Control**: Validates user access to specific indexes

### Frontend Components

#### 1. FileUploadPage Enhancement
- **Auto-Detection**: Automatically detects logged-in user's email
- **Multiple Sources**: Checks localStorage, sessionStorage, and user session data
- **Fallback Options**: Provides manual selection if auto-detection fails

#### 2. ApiService Enhancement
- **User Email Detection**: Centralized method to get current user email
- **Index Filtering**: Fetches only user-specific indexes
- **Error Handling**: Graceful fallback to default configuration

#### 3. New Chat Page
- **Dynamic Loading**: Loads only user-accessible indexes
- **Auto-Selection**: Automatically selects appropriate index for user

## Implementation Details

### 1. Upload Process Flow

```
1. User selects CSV file and provides index name
2. System auto-detects user email from multiple sources:
   - localStorage.user_email
   - sessionStorage.user_email  
   - localStorage.faiss_client_email
   - sessionStorage.resultUser (parsed JSON)
3. Upload endpoint validates:
   - File type (CSV only)
   - Index name (required)
   - Client email (required)
4. CSV data is processed and stored:
   - FAISS index creation/update
   - Database table creation
   - pine_collection entry with email, index_name, file_name
5. Success response includes upload metadata
```

### 2. Index Listing Flow

```
1. Frontend requests user indexes via ApiService.fetchUserIndexes()
2. ApiService detects current user email
3. Backend /api/list-faiss-indexes endpoint:
   - Receives user email in POST request
   - Uses PineFilterService to get user's accessible indexes
   - Filters filesystem indexes to match user access
   - Always includes 'default' index for all users
4. Frontend receives filtered index list
5. UI displays only user-accessible indexes
```

### 3. Email Detection Priority

The system checks for user email in this order:
1. `localStorage.user_email`
2. `sessionStorage.user_email`
3. `localStorage.faiss_client_email`
4. `sessionStorage.resultUser` (parsed for email/username)

## Database Changes

### Schema Updates
```sql
-- Enhanced pine_collection table
CREATE TABLE pine_collection (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    index_name TEXT NOT NULL,
    email TEXT,
    file_name TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(index_name, email)
);
```

### Migration Support
- Automatic column addition for existing databases
- Backward compatibility maintained
- No data loss during upgrades

## API Changes

### Upload Endpoint
```javascript
// Request
POST /api/upload-csv
FormData: {
    file: <CSV file>,
    client: <user email> (required),
    index_name: <index name> (required),
    embed_model: <model name> (optional),
    update_mode: 'update'|'new' (optional)
}

// Response
{
    "success": true,
    "data": {
        "index_name": "user_index",
        "vector_count": 150,
        "client": "<EMAIL>",
        "file_name": "data.csv"
    }
}
```

### Index Listing Endpoint
```javascript
// Request
POST /api/list-faiss-indexes
{
    "email": "<EMAIL>"
}

// Response
{
    "success": true,
    "indexes": ["user_index_1", "user_index_2", "default"],
    "filtered_by_user": true,
    "user_email": "<EMAIL>"
}
```

## Security Features

### 1. Email Validation
- Format validation (contains @ and .)
- Length validation (minimum 5 characters)
- Normalization (trim and lowercase)

### 2. Access Control
- Users can only see their own indexes
- Default index accessible to all users
- Database-level filtering prevents data leakage

### 3. Input Sanitization
- SQL injection prevention
- File type validation
- Parameter validation

## Testing

### 1. Backend Testing
Use the provided test script:
```bash
python test_client_email_filtering.py
```

This tests:
- CSV upload with different client emails
- Index filtering by client email
- Database-level access control

### 2. Frontend Testing
Access the test page at `/email-test` to verify:
- User email auto-detection
- Index filtering in UI
- Multiple email source handling

### 3. Manual Testing Steps

1. **Upload Test**:
   - Upload CSV files with different client emails
   - Verify each upload creates separate index entries
   - Check database contains correct email associations

2. **Filtering Test**:
   - Set different user emails in localStorage
   - Refresh the new-chat page
   - Verify only relevant indexes are shown

3. **Cross-User Test**:
   - Upload data as User A
   - Switch to User B email
   - Verify User B cannot see User A's indexes

## Configuration

### Environment Variables
```bash
# Database path
DB_PATH=csv_data.db

# FAISS data directory
FAISS_DATA_DIR=./faiss_data

# Default embedding model
DEFAULT_EMBED_MODEL=all-MiniLM-L6-v2
```

### Frontend Configuration
```javascript
// Backend URL
BACKEND_BASE_URL=http://localhost:5010

// User email storage keys
USER_EMAIL_KEYS = [
    'user_email',
    'faiss_client_email'
]
```

## Troubleshooting

### Common Issues

1. **No indexes showing for user**:
   - Check if user email is properly detected
   - Verify database contains entries for that email
   - Check backend logs for filtering errors

2. **Upload fails with "Client email required"**:
   - Ensure user email is set in localStorage/sessionStorage
   - Check FileUploadPage auto-detection logic
   - Manually select client email if auto-detection fails

3. **User sees other users' indexes**:
   - Verify PineFilterService is working correctly
   - Check database email normalization
   - Ensure frontend sends correct user email

### Debug Tools

1. **Email Detection Test**: Visit `/email-test` page
2. **Backend Logs**: Check console output for filtering messages
3. **Database Inspection**: Query pine_collection table directly
4. **Network Tab**: Inspect API requests/responses

## Future Enhancements

### Planned Features
1. **Role-based Access**: Admin users can see all indexes
2. **Shared Indexes**: Allow users to share indexes with others
3. **Index Permissions**: Fine-grained access control
4. **Audit Logging**: Track all access attempts

### Performance Optimizations
1. **Index Caching**: Cache user index lists
2. **Database Indexing**: Add indexes on email column
3. **Lazy Loading**: Load indexes on demand

## Conclusion

The client email filtering implementation provides secure, user-specific access to FAISS indexes while maintaining backward compatibility and ease of use. The system automatically detects user emails and ensures data isolation between different users.

For support or questions, refer to the test scripts and debug tools provided in this implementation.