import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Pi<PERSON>, Pi<PERSON>heck<PERSON><PERSON><PERSON>, PiWarning, PiDatabase } from 'react-icons/pi';
import UploadDropdown from './UploadDropdown';
import FileDropZone from './FileDropZone';
import URLInput from './URLInput';
import { handleUpload, UploadResponse } from '../../services/uploadService';
import toast from 'react-hot-toast';

interface ChatInputUploadProps {
  selectedLanguage: string;
  onFileUpload?: (files: File[]) => void;
  onURLSubmit?: (url: string, type: 'youtube' | 'article') => void;
  onUploadStateChange?: (isActive: boolean, showDropdown: boolean) => void;
  onNetworkError?: () => void; // Callback for network errors
  disabled?: boolean;
}

export type UploadType = 'pdf' | 'youtube' | 'article' | 'mp3';

interface UploadState {
  type: UploadType | null;
  files: File[];
  url: string;
  isActive: boolean;
  isProcessing: boolean;
  uploadResult: UploadResponse | null;
}

const ChatInputUpload: React.FC<ChatInputUploadProps> = ({
  selectedLanguage,
  onFileUpload,
  onURLSubmit,
  onUploadStateChange,
  onNetworkError,
  disabled = false
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [uploadState, setUploadState] = useState<UploadState>({
    type: null,
    files: [],
    url: '',
    isActive: false,
    isProcessing: false,
    uploadResult: null
  });

  const dropdownRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLButtonElement>(null);
  const autoCloseTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Close dropdown when clicking outside or pressing Escape
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        iconRef.current &&
        !iconRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    }

    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Escape' && showDropdown) {
        setShowDropdown(false);
        // Return focus to the pin button
        iconRef.current?.focus();
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showDropdown]);

  // Cleanup timeout on component unmount
  useEffect(() => {
    return () => {
      if (autoCloseTimeoutRef.current) {
        clearTimeout(autoCloseTimeoutRef.current);
      }
    };
  }, []);

  // Notify parent about upload state changes
  useEffect(() => {
    if (onUploadStateChange) {
      onUploadStateChange(uploadState.isActive, showDropdown);
    }
  }, [uploadState.isActive, showDropdown, onUploadStateChange]);

  const handleUploadTypeSelect = (type: UploadType) => {
    // If switching to a different type, clear previous content
    const isSwitchingType = uploadState.isActive && uploadState.type !== type;

    setUploadState({
      type,
      files: isSwitchingType ? [] : uploadState.files,
      url: isSwitchingType ? '' : uploadState.url,
      isActive: true,
      isProcessing: false,
      uploadResult: null
    });
    setShowDropdown(false);

    // If switching types, clear previous uploads
    if (isSwitchingType) {
      if (onFileUpload) {
        onFileUpload([]);
      }
    }
  };

  const handleAddMoreContent = () => {
    // Allow adding more content of the same type or switching types
    setShowDropdown(!showDropdown);
  };

  const getUploadStatusText = () => {
    if (!uploadState.isActive) return '';

    // Show processing status
    if (uploadState.isProcessing) {
      return 'Processing...';
    }

    // Show upload result status
    if (uploadState.uploadResult) {
      if (uploadState.uploadResult.success) {
        return '✅ Processed successfully';
      } else {
        return '❌ Processing failed';
      }
    }

    const fileCount = uploadState.files.length;
    const hasUrl = uploadState.url.trim().length > 0;

    if (uploadState.type === 'pdf' || uploadState.type === 'mp3') {
      return fileCount > 0 ? `${fileCount} file${fileCount > 1 ? 's' : ''} selected` : 'No files selected';
    } else {
      return hasUrl ? 'URL added' : 'No URL added';
    }
  };

  const handleFileUpload = async (files: File[]) => {
    if (!files.length || !uploadState.type || (uploadState.type !== 'pdf' && uploadState.type !== 'mp3')) {
      return;
    }

    const file = files[0];
    console.log('📁 Files selected for upload:', files);

    // Validate file type and size before processing
    const fileName = file.name.toLowerCase();
    const fileSize = file.size;
    const maxSize = 50 * 1024 * 1024; // 50MB

    // Check file size
    if (fileSize > maxSize) {
      toast.error('File too large. Please upload a file smaller than 50MB.');
      return;
    }

    // Validate file type based on upload type
    if (uploadState.type === 'pdf') {
      const isPDF = fileName.endsWith('.pdf');
      const isDocument = fileName.endsWith('.doc') || fileName.endsWith('.docx') ||
                        fileName.endsWith('.txt') || fileName.endsWith('.rtf');

      if (!isPDF && !isDocument) {
        toast.error('Please upload PDF, DOC, DOCX, TXT, or RTF files only.');
        return;
      }
    } else if (uploadState.type === 'mp3') {
      const isAudio = fileName.endsWith('.mp3') || fileName.endsWith('.wav') ||
                     fileName.endsWith('.m4a') || fileName.endsWith('.flac');

      if (!isAudio) {
        toast.error('Please upload MP3, WAV, M4A, or FLAC audio files only.');
        return;
      }
    }

    // Update state with files and processing status
    setUploadState(prev => ({
      ...prev,
      files,
      isProcessing: true,
      uploadResult: null
    }));

    try {
      console.log(`🚀 Starting ${uploadState.type} upload for file:`, file.name);

      const result = await handleUpload(uploadState.type, file);
      console.log('📥 Upload result:', result);

      // Update state with result
      setUploadState(prev => ({
        ...prev,
        isProcessing: false,
        uploadResult: result
      }));

      // Show success/error toast with more specific messages
      if (result.success) {
        const fileType = uploadState.type === 'pdf' ? 'PDF/Document' : 'Audio';
        const cacheMessage = result.message?.includes('cache') ? ' (from cache)' : '';
        toast.success(`${fileType} file processed successfully${cacheMessage}! Content has been indexed.`);

        // Call the original callback if provided
        if (onFileUpload) {
          onFileUpload(files);
        }

        // Auto-close popup after successful upload with a slight delay for user feedback
        autoCloseTimeoutRef.current = setTimeout(() => {
          handleClearUpload();
        }, 2000); // 2 second delay to show success state
      } else {
        const errorMessage = result.error || 'Failed to process file';
        console.error('❌ Upload failed:', errorMessage);
        toast.error(errorMessage);

        // Check if it's a network error and trigger connection test
        if (errorMessage.includes('Network error') || errorMessage.includes('Connection refused')) {
          if (onNetworkError) {
            onNetworkError();
          }
        }
      }
    } catch (error) {
      console.error('❌ Upload error:', error);
      setUploadState(prev => ({
        ...prev,
        isProcessing: false,
        uploadResult: { success: false, error: 'Upload failed due to network error' }
      }));
      toast.error('Upload failed due to network error. Please check your connection.');

      // Call network error callback if provided
      if (onNetworkError) {
        onNetworkError();
      }
    }
  };

  const handleURLChange = (url: string) => {
    setUploadState(prev => ({
      ...prev,
      url
    }));
  };

  const handleURLSubmit = async (url: string) => {
    if (!uploadState.type || (uploadState.type !== 'youtube' && uploadState.type !== 'article')) {
      return;
    }

    // Set processing state
    setUploadState(prev => ({ ...prev, isProcessing: true, uploadResult: null }));

    try {
      // Process the URL using the upload service
      const result = await handleUpload(uploadState.type, url);

      // Update state with result
      setUploadState(prev => ({
        ...prev,
        isProcessing: false,
        uploadResult: result
      }));

      // Show success/error toast
      if (result.success) {
        const cacheMessage = result.message?.includes('cache') ? ' (from cache)' : '';
        toast.success(`${uploadState.type === 'youtube' ? 'YouTube video' : 'Article'} processed successfully${cacheMessage}!`);
        // Call the original callback if provided
        if (onURLSubmit) {
          onURLSubmit(url, uploadState.type);
        }

        // Auto-close popup after successful upload with a slight delay for user feedback
        autoCloseTimeoutRef.current = setTimeout(() => {
          handleClearUpload();
        }, 2000); // 2 second delay to show success state
      } else {
        toast.error(result.error || 'Failed to process URL');

        // Check if it's a network error and trigger connection test
        if (result.error?.includes('Network error') || result.error?.includes('Connection refused')) {
          if (onNetworkError) {
            onNetworkError();
          }
        }
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploadState(prev => ({
        ...prev,
        isProcessing: false,
        uploadResult: { success: false, error: 'Upload failed' }
      }));
      toast.error('Upload failed');

      // Trigger network error callback for any catch block errors
      if (onNetworkError) {
        onNetworkError();
      }
    }
  };

  const handleClearUpload = () => {
    // Clear any pending auto-close timeout
    if (autoCloseTimeoutRef.current) {
      clearTimeout(autoCloseTimeoutRef.current);
      autoCloseTimeoutRef.current = null;
    }

    setUploadState({
      type: null,
      files: [],
      url: '',
      isActive: false,
      isProcessing: false,
      uploadResult: null
    });
  };

  const getIconColor = () => {
    if (disabled) return 'text-gray-400';
    
    switch (selectedLanguage) {
      case 'Tamil':
        return uploadState.isActive ? 'text-purple-600' : 'text-purple-500 hover:text-purple-600';
      case 'Telugu':
        return uploadState.isActive ? 'text-green-600' : 'text-green-500 hover:text-green-600';
      case 'Kannada':
        return uploadState.isActive ? 'text-orange-600' : 'text-orange-500 hover:text-orange-600';
      default:
        return uploadState.isActive ? 'text-blue-600' : 'text-blue-500 hover:text-blue-600';
    }
  };

  const renderUploadContent = () => {
    if (!uploadState.isActive || !uploadState.type) return null;

    const isFileType = uploadState.type === 'pdf' || uploadState.type === 'mp3';
    const isURLType = uploadState.type === 'youtube' || uploadState.type === 'article';

    return (
      <div className="mb-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className={`p-1 rounded text-xs font-medium text-white ${
              uploadState.type === 'pdf' ? 'bg-red-500' :
              uploadState.type === 'mp3' ? 'bg-purple-500' :
              uploadState.type === 'youtube' ? 'bg-red-600' :
              uploadState.type === 'article' ? 'bg-blue-500' : 'bg-gray-500'
            }`}>
              {uploadState.type === 'pdf' ? 'PDF' :
               uploadState.type === 'mp3' ? 'MP3' :
               uploadState.type === 'youtube' ? 'YT' :
               uploadState.type === 'article' ? 'WEB' : '?'}
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {uploadState.type === 'pdf' && 'PDF/Document Upload'}
                {uploadState.type === 'mp3' && 'MP3 Audio Upload'}
                {uploadState.type === 'youtube' && 'YouTube URL'}
                {uploadState.type === 'article' && 'Article URL'}
              </span>
              <span className={`text-xs flex items-center gap-1 ${
                uploadState.uploadResult?.success ? 'text-green-600 dark:text-green-400' :
                uploadState.uploadResult && !uploadState.uploadResult.success ? 'text-red-600 dark:text-red-400' :
                uploadState.isProcessing ? 'text-blue-600 dark:text-blue-400' :
                'text-gray-500 dark:text-gray-400'
              }`}>
                {uploadState.isProcessing && (
                  <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                )}
                {uploadState.uploadResult?.success && (
                  <>
                    <PiCheckCircle className="w-3 h-3" />
                    {uploadState.uploadResult.message?.includes('cache') && (
                      <PiDatabase className="w-3 h-3 text-blue-500" title="Loaded from cache" />
                    )}
                  </>
                )}
                {uploadState.uploadResult && !uploadState.uploadResult.success && (
                  <PiWarning className="w-3 h-3" />
                )}
                {getUploadStatusText()}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* Functional pin icon positioned near close button when upload is active */}
            <div className="relative">
              <button
                ref={iconRef}
                type="button"
                onClick={handleAddMoreContent}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleAddMoreContent();
                  }
                }}
                disabled={disabled}
                className={`p-1.5 rounded-full transition-all duration-200 transform hover:scale-105 border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 ${getIconColor()} ${
                  disabled ? 'cursor-not-allowed opacity-50 bg-gray-100' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 bg-white dark:bg-gray-700 shadow-sm hover:shadow-md'
                } ${showDropdown ? 'ring-2 ring-offset-1 ' + (
                  selectedLanguage === 'Tamil' ? 'ring-purple-300 focus:ring-purple-500' :
                  selectedLanguage === 'Telugu' ? 'ring-green-300 focus:ring-green-500' :
                  selectedLanguage === 'Kannada' ? 'ring-orange-300 focus:ring-orange-500' : 'ring-blue-300 focus:ring-blue-500'
                ) : (
                  selectedLanguage === 'Tamil' ? 'focus:ring-purple-500' :
                  selectedLanguage === 'Telugu' ? 'focus:ring-green-500' :
                  selectedLanguage === 'Kannada' ? 'focus:ring-orange-500' : 'focus:ring-blue-500'
                )}`}
                title={disabled ? 'Upload disabled' : showDropdown ? 'Close upload options (Press Esc)' : 'Add more files or change upload type (Press Enter)'}
                aria-label={disabled ? 'Upload disabled' : showDropdown ? 'Close upload options' : 'Add more files or change upload type'}
                aria-expanded={showDropdown}
                aria-haspopup="menu"
              >
                <PiPaperclip className={`w-4 h-4 transition-transform duration-200 ${showDropdown ? 'rotate-45' : ''}`} />
              </button>

              {/* Badge indicator for active upload type */}
              <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full text-xs flex items-center justify-center text-white font-bold ${
                uploadState.type === 'pdf' ? 'bg-red-500' :
                uploadState.type === 'mp3' ? 'bg-purple-500' :
                uploadState.type === 'youtube' ? 'bg-red-600' :
                uploadState.type === 'article' ? 'bg-blue-500' : 'bg-gray-500'
              }`}>
                {uploadState.type === 'pdf' ? 'P' :
                 uploadState.type === 'mp3' ? '♪' :
                 uploadState.type === 'youtube' ? 'Y' :
                 uploadState.type === 'article' ? 'A' : '?'}
              </div>
            </div>
            <button
              onClick={handleClearUpload}
              className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
              title="Clear upload"
            >
              <PiX className="w-4 h-4" />
            </button>
          </div>
        </div>

        {isFileType && (
          <div className={uploadState.isProcessing ? 'opacity-50 pointer-events-none' : ''}>
            <FileDropZone
              acceptedTypes={uploadState.type === 'pdf' ? '.pdf,.doc,.docx,.txt,.rtf' : '.mp3,.wav,.m4a,.flac'}
              onFilesSelected={handleFileUpload}
              maxFiles={1}
              selectedLanguage={selectedLanguage}
            />
          </div>
        )}

        {isURLType && uploadState.type && (uploadState.type === 'youtube' || uploadState.type === 'article') && (
          <div className={uploadState.isProcessing ? 'opacity-50 pointer-events-none' : ''}>
            <URLInput
              type={uploadState.type}
              value={uploadState.url}
              onChange={handleURLChange}
              onSubmit={handleURLSubmit}
              selectedLanguage={selectedLanguage}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="relative">
      {/* Upload content area */}
      {renderUploadContent()}

      {/* Upload icon with improved alignment - hide when upload is active */}
      {/* {!uploadState.isActive && (
        <button
          ref={iconRef}
          type="button"
          onClick={() => setShowDropdown(!showDropdown)}
          disabled={disabled}
          className={`p-2.5 rounded-full transition-all duration-200 transform hover:scale-105 ${getIconColor()} ${
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm hover:shadow-md'
          }`}
          title={disabled ? 'Upload disabled' : 'Upload file or add URL'}
        >
          <PiPaperclip className="w-5 h-5" />
        </button>
      )} */}

      {/* Dropdown menu - positioned based on upload state */}
      {showDropdown && !disabled && (
        <div
          ref={dropdownRef}
          className={uploadState.isActive ? "absolute top-full right-0 mt-2 z-50" : ""}
        >
          <UploadDropdown
            onSelect={handleUploadTypeSelect}
            selectedLanguage={selectedLanguage}
          />
        </div>
      )}
    </div>
  );
};

export default ChatInputUpload;
