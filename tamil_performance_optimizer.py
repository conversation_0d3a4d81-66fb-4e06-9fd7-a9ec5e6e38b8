"""
Tamil Performance Optimizer
This script optimizes the backend for better Tamil text processing speed and quality.
"""

import os
import sys
import time
import requests
import json
from typing import Dict, List

class TamilPerformanceOptimizer:
    """
    Optimizer for Tamil text processing performance and quality.
    """
    
    def __init__(self, backend_url: str = "http://localhost:5001"):
        self.backend_url = backend_url.rstrip('/')
        self.optimization_results = {}
    
    def test_current_performance(self, test_query: str) -> Dict:
        """Test current performance with a Tamil query."""
        print("🧪 Testing current Tamil processing performance...")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.backend_url}/financial_query",
                json={
                    "query": test_query,
                    "language": "Tamil",
                    "index_name": "default"
                },
                timeout=30
            )
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('ai_response', '')
                
                return {
                    'success': True,
                    'processing_time': processing_time,
                    'response_length': len(ai_response),
                    'response_quality': self.assess_response_quality(ai_response),
                    'full_response': data
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'processing_time': processing_time
                }
                
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                'success': False,
                'error': str(e),
                'processing_time': processing_time
            }
    
    def assess_response_quality(self, response: str) -> Dict:
        """Assess the quality of a Tamil response."""
        import re
        
        # Tamil character pattern
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        tamil_chars = len(tamil_pattern.findall(response))
        
        # Quality metrics
        quality = {
            'length': len(response),
            'tamil_character_count': tamil_chars,
            'tamil_ratio': tamil_chars / len(response) if len(response) > 0 else 0,
            'has_corruption_patterns': bool(re.search(r'__[A-Z_]+__|\.(\d+)\.|\*\.?\d*\*', response)),
            'word_count': len(response.split()),
            'sentence_count': len(re.split(r'[.!?]+', response)),
            'quality_score': 0
        }
        
        # Calculate quality score
        score = 0
        if quality['length'] > 100:
            score += 25
        if quality['tamil_ratio'] > 0.3:
            score += 25
        if not quality['has_corruption_patterns']:
            score += 25
        if quality['word_count'] > 20:
            score += 25
        
        quality['quality_score'] = score
        return quality
    
    def optimize_backend_settings(self) -> Dict:
        """Apply optimization settings to the backend."""
        print("🚀 Applying Tamil processing optimizations...")
        
        optimizations = []
        
        # 1. Disable aggressive corruption detection for Tamil
        try:
            response = requests.post(
                f"{self.backend_url}/api/set-tamil-optimization",
                json={
                    "enable_tamil_optimization": True,
                    "disable_aggressive_cleaning": True,
                    "preserve_content_ratio": 0.9,
                    "fast_processing_mode": True
                },
                timeout=10
            )
            
            if response.status_code == 200:
                optimizations.append("✅ Tamil optimization enabled")
            else:
                optimizations.append("⚠️ Tamil optimization endpoint not available")
                
        except Exception as e:
            optimizations.append(f"⚠️ Could not apply Tamil optimization: {str(e)}")
        
        # 2. Test corruption detection bypass
        try:
            test_text = "நெருக்கமான மதிப்பீடுகள் 1995 இல் கண்கவர் செயல்திறன்"
            response = requests.post(
                f"{self.backend_url}/api/test-corruption-detection",
                json={"text": test_text},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if not data.get('is_corrupted', True):
                    optimizations.append("✅ Corruption detection properly calibrated for Tamil")
                else:
                    optimizations.append("⚠️ Corruption detection may be too aggressive")
            else:
                optimizations.append("⚠️ Could not test corruption detection")
                
        except Exception as e:
            optimizations.append(f"⚠️ Corruption detection test failed: {str(e)}")
        
        return {
            'optimizations_applied': optimizations,
            'timestamp': time.time()
        }
    
    def run_performance_comparison(self) -> Dict:
        """Run a comprehensive performance comparison."""
        print("📊 Running Tamil performance comparison...")
        
        # Test queries of varying complexity
        test_queries = [
            "மின்சாரம் வழங்க வேண்டும்",
            "விவசாய கடன் தேவை எனக்கு",
            "நெருக்கமான மதிப்பீடுகள் 1995 இல் கண்கவர் செயல்திறன் மூலம் வெற்றி நிரூபிக்கப்பட்டுள்ளது",
            "பணம் பற்றி விரிவாக சொல்லுங்கள்"
        ]
        
        results = {
            'before_optimization': [],
            'after_optimization': [],
            'improvement_metrics': {}
        }
        
        # Test before optimization
        print("Testing before optimization...")
        for query in test_queries:
            result = self.test_current_performance(query)
            results['before_optimization'].append({
                'query': query,
                'result': result
            })
            time.sleep(1)  # Brief pause between tests
        
        # Apply optimizations
        optimization_result = self.optimize_backend_settings()
        
        # Test after optimization
        print("Testing after optimization...")
        time.sleep(2)  # Allow optimizations to take effect
        
        for query in test_queries:
            result = self.test_current_performance(query)
            results['after_optimization'].append({
                'query': query,
                'result': result
            })
            time.sleep(1)
        
        # Calculate improvements
        results['improvement_metrics'] = self.calculate_improvements(
            results['before_optimization'],
            results['after_optimization']
        )
        
        results['optimization_details'] = optimization_result
        
        return results
    
    def calculate_improvements(self, before: List, after: List) -> Dict:
        """Calculate performance improvements."""
        if len(before) != len(after):
            return {'error': 'Mismatched test results'}
        
        improvements = {
            'avg_speed_improvement': 0,
            'avg_response_length_improvement': 0,
            'avg_quality_improvement': 0,
            'success_rate_before': 0,
            'success_rate_after': 0
        }
        
        # Calculate averages
        before_times = [r['result']['processing_time'] for r in before if r['result']['success']]
        after_times = [r['result']['processing_time'] for r in after if r['result']['success']]
        
        before_lengths = [r['result']['response_length'] for r in before if r['result']['success']]
        after_lengths = [r['result']['response_length'] for r in after if r['result']['success']]
        
        before_qualities = [r['result']['response_quality']['quality_score'] for r in before if r['result']['success']]
        after_qualities = [r['result']['response_quality']['quality_score'] for r in after if r['result']['success']]
        
        if before_times and after_times:
            avg_before_time = sum(before_times) / len(before_times)
            avg_after_time = sum(after_times) / len(after_times)
            improvements['avg_speed_improvement'] = ((avg_before_time - avg_after_time) / avg_before_time) * 100
        
        if before_lengths and after_lengths:
            avg_before_length = sum(before_lengths) / len(before_lengths)
            avg_after_length = sum(after_lengths) / len(after_lengths)
            improvements['avg_response_length_improvement'] = ((avg_after_length - avg_before_length) / avg_before_length) * 100
        
        if before_qualities and after_qualities:
            avg_before_quality = sum(before_qualities) / len(before_qualities)
            avg_after_quality = sum(after_qualities) / len(after_qualities)
            improvements['avg_quality_improvement'] = ((avg_after_quality - avg_before_quality) / avg_before_quality) * 100
        
        improvements['success_rate_before'] = (len([r for r in before if r['result']['success']]) / len(before)) * 100
        improvements['success_rate_after'] = (len([r for r in after if r['result']['success']]) / len(after)) * 100
        
        return improvements
    
    def generate_optimization_report(self, results: Dict) -> str:
        """Generate a comprehensive optimization report."""
        report = []
        report.append("🔍 TAMIL PROCESSING OPTIMIZATION REPORT")
        report.append("=" * 50)
        report.append("")
        
        # Performance improvements
        metrics = results.get('improvement_metrics', {})
        
        report.append("📈 PERFORMANCE IMPROVEMENTS:")
        report.append(f"   Speed improvement: {metrics.get('avg_speed_improvement', 0):.1f}%")
        report.append(f"   Response length improvement: {metrics.get('avg_response_length_improvement', 0):.1f}%")
        report.append(f"   Quality improvement: {metrics.get('avg_quality_improvement', 0):.1f}%")
        report.append(f"   Success rate before: {metrics.get('success_rate_before', 0):.1f}%")
        report.append(f"   Success rate after: {metrics.get('success_rate_after', 0):.1f}%")
        report.append("")
        
        # Optimizations applied
        opt_details = results.get('optimization_details', {})
        optimizations = opt_details.get('optimizations_applied', [])
        
        report.append("🚀 OPTIMIZATIONS APPLIED:")
        for opt in optimizations:
            report.append(f"   {opt}")
        report.append("")
        
        # Recommendations
        report.append("💡 RECOMMENDATIONS:")
        
        if metrics.get('avg_speed_improvement', 0) < 10:
            report.append("   - Consider caching frequently used Tamil responses")
            report.append("   - Implement direct Tamil processing without translation")
        
        if metrics.get('avg_response_length_improvement', 0) < 20:
            report.append("   - Disable aggressive text cleaning for Tamil content")
            report.append("   - Use conservative corruption detection thresholds")
        
        if metrics.get('avg_quality_improvement', 0) < 15:
            report.append("   - Preserve Tamil character patterns during processing")
            report.append("   - Use Tamil-specific response generation prompts")
        
        report.append("")
        report.append("🎯 NEXT STEPS:")
        report.append("   1. Monitor Tamil response quality in production")
        report.append("   2. Implement user feedback collection for Tamil responses")
        report.append("   3. Consider dedicated Tamil language model fine-tuning")
        report.append("   4. Set up performance monitoring dashboards")
        
        return "\n".join(report)

def main():
    """Main optimization function."""
    print("🚀 Starting Tamil Performance Optimization...")
    print("=" * 50)
    
    # Initialize optimizer
    optimizer = TamilPerformanceOptimizer()
    
    # Run comprehensive performance comparison
    results = optimizer.run_performance_comparison()
    
    # Generate and display report
    report = optimizer.generate_optimization_report(results)
    print(report)
    
    # Save results to file
    timestamp = int(time.time())
    results_file = f"tamil_optimization_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    # Quick test with your example
    print("\n🧪 Testing with your example text...")
    test_query = "நெருக்கமான மதிப்பீடுகள் 1995 இல் கண்கவர் செயல்திறன் மூலம் வெற்றி நிரூபிக்கப்பட்டுள்ளது"
    
    result = optimizer.test_current_performance(test_query)
    
    if result['success']:
        print(f"✅ Processing time: {result['processing_time']:.2f} seconds")
        print(f"✅ Response length: {result['response_length']} characters")
        print(f"✅ Quality score: {result['response_quality']['quality_score']}/100")
        print(f"✅ Tamil character ratio: {result['response_quality']['tamil_ratio']:.2%}")
    else:
        print(f"❌ Test failed: {result['error']}")

if __name__ == "__main__":
    main()