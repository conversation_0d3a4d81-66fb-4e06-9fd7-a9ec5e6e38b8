import React, { useState, useRef, useEffect } from 'react';
import WebpagePreview from './WebpagePreview';
import { PiCaretRight, PiLightbulb, PiLink, PiX, PiInfo } from 'react-icons/pi';
import { motion, AnimatePresence } from 'framer-motion';
import ClientOnly from '@/components/ui/ClientOnly';

type Language = 'english' | 'tamil' | 'telugu' | 'kannada';

interface PerplexityStyleResponseProps {
  text: string;
  sentenceAnalysis?: Array<{
    sentence: string;
    url: string;
    summary?: string;
    source_title?: string;
    source_type?: string;
    file_id?: string;
    page?: string;
  }>;
  relatedQuestions?: string[];
  onSelectQuestion?: (question: string) => void;
  selectedLanguage?: string;
}

// Interface for the preview data
interface PreviewData {
  url: string;
  summary?: string;
  source_title?: string;
  source_type?: string;
  file_id?: string;
  page?: string;
  referenceNumber: number;
  position: {
    x: number;
    y: number;
  } | null;
}

const PerplexityStyleResponse: React.FC<PerplexityStyleResponseProps> = ({
  text,
  sentenceAnalysis,
  relatedQuestions,
  onSelectQuestion,
  selectedLanguage = "English"
}) => {
  // Enhanced custom styles for better text rendering with multilingual support
  const textStyles = {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Noto Sans Tamil", "Noto Sans Telugu", "Noto Sans Kannada", "Helvetica Neue", Arial, sans-serif',
    lineHeight: '1.8',
    letterSpacing: '0.02em',
    wordSpacing: '0.1em',
    textRendering: 'optimizeLegibility' as const,
    fontFeatureSettings: '"liga" 1, "kern" 1',
    WebkitFontSmoothing: 'antialiased' as const,
    MozOsxFontSmoothing: 'grayscale' as const
  };
  // Debug logging for related questions
  console.log("🔍 PerplexityStyleResponse - Related Questions Debug:");
  console.log("📊 relatedQuestions:", relatedQuestions);
  console.log("📊 relatedQuestions length:", relatedQuestions?.length);
  console.log("📊 relatedQuestions type:", typeof relatedQuestions);
  if (relatedQuestions) {
    relatedQuestions.forEach((q, i) => console.log(`📊 Question ${i + 1}:`, q));
  }

  // Debug logging for text processing
  console.log("🔍 PerplexityStyleResponse - Text Processing Debug:");
  console.log("📝 Original text length:", text?.length || 0);
  console.log("📝 Original text preview:", text?.substring(0, 200) + "...");
  console.log("📝 Sentence analysis count:", sentenceAnalysis?.length || 0);
  console.log("📝 Selected language:", selectedLanguage);
  
  // Check if text contains regional language characters
  const hasRegionalChars = /[\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/.test(text || '');
  console.log("🌏 Contains regional language characters:", hasRegionalChars);
  
  if (hasRegionalChars) {
    console.log("🌏 REGIONAL LANGUAGE DETECTED - Text processing will use minimal cleaning to prevent truncation");
  }
  const [activeReference, setActiveReference] = useState<number | null>(null);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [showRelatedModal, setShowRelatedModal] = useState(false);
  const referenceRefs = useRef<Record<number, HTMLButtonElement | null>>({});
  const referenceSpanRefs = useRef<Record<number, HTMLSpanElement | null>>({});
  const previewTimerRef = useRef<NodeJS.Timeout | null>(null);
  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isHoveringPreviewRef = useRef<boolean>(false);

  // Function to detect language from text content
  const detectLanguageFromText = (text: string): Language => {
    // Tamil Unicode range: \u0B80-\u0BFF
    const tamilRegex = /[\u0B80-\u0BFF]/;
    // Telugu Unicode range: \u0C00-\u0C7F
    const teluguRegex = /[\u0C00-\u0C7F]/;
    // Kannada Unicode range: \u0C80-\u0CFF
    const kannadaRegex = /[\u0C80-\u0CFF]/;

    if (tamilRegex.test(text)) {
      return 'tamil';
    } else if (teluguRegex.test(text)) {
      return 'telugu';
    } else if (kannadaRegex.test(text)) {
      return 'kannada';
    } else {
      return 'english';
    }
  };

  // Enhanced sentence matching function for multilingual support
  const findSentenceInText = (text: string, sentence: string): number => {
    if (!text || !sentence) return -1;

    // Detect if this is regional language content
    const isRegionalLanguage = /[\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/.test(sentence);
    
    if (isRegionalLanguage) {
      console.log("🌏 Regional language sentence matching - using simplified approach to prevent text loss");
      
      // For regional languages, use only basic matching to avoid aggressive processing
      const normalizedText = text.normalize('NFC').trim();
      const normalizedSentence = sentence.normalize('NFC').trim();
      
      // Try exact match first
      let index = normalizedText.indexOf(normalizedSentence);
      if (index !== -1) {
        console.log(`✅ Regional language exact match found at index ${index}`);
        return index;
      }
      
      // Try with basic whitespace normalization
      const basicNormalizedText = normalizedText.replace(/\s+/g, ' ');
      const basicNormalizedSentence = normalizedSentence.replace(/\s+/g, ' ');
      index = basicNormalizedText.indexOf(basicNormalizedSentence);
      if (index !== -1) {
        console.log(`✅ Regional language basic normalized match found at index ${index}`);
        return index;
      }
      
      // For regional languages, if no match found, return -1 to avoid aggressive processing
      console.log(`ℹ️ Regional language sentence not found - this is normal and prevents text truncation`);
      return -1;
    }

    // Normalize both texts for comparison (English content)
    const normalizeText = (str: string): string => {
      return str
        .normalize('NFC') // Normalize Unicode
        .replace(/\s+/g, ' ') // Normalize whitespace
        .replace(/[\u200B-\u200D\uFEFF]/g, '') // Remove zero-width characters
        .trim();
    };

    const normalizedText = normalizeText(text);
    const normalizedSentence = normalizeText(sentence);

    // Debug logging for sentence matching
    const detectedLang = detectLanguageFromText(sentence);
    console.log(`🔍 Finding sentence in text (${detectedLang}):`, {
      sentenceLength: sentence.length,
      textLength: text.length,
      sentencePreview: sentence.substring(0, 50) + '...',
      textPreview: text.substring(0, 100) + '...'
    });

    // Try exact match first
    let index = normalizedText.indexOf(normalizedSentence);
    if (index !== -1) {
      console.log(`✅ Exact match found at index ${index}`);
      return index;
    }

    // For English, try more aggressive matching strategies
    console.log(`🌐 Trying enhanced matching strategies for ${detectedLang} language`);
    
    // Strategy 1: Try matching with different Unicode normalizations
    const nfdText = text.normalize('NFD');
    const nfdSentence = sentence.normalize('NFD');
    index = nfdText.indexOf(nfdSentence);
    if (index !== -1) {
      console.log(`✅ NFD normalization match found at index ${index}`);
      return index;
    }

    // Strategy 2: Try matching by removing punctuation and extra spaces
    const cleanText = normalizedText.replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ');
    const cleanSentence = normalizedSentence.replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ');
    index = cleanText.indexOf(cleanSentence);
    if (index !== -1) {
      console.log(`✅ Clean text match found at index ${index}`);
      return index;
    }

    // Strategy 3: Word-based fuzzy matching for English content
    const getSignificantWords = (str: string) => {
      return str.split(/\s+/)
        .filter(word => word.length > 2) // Only significant words for English
        .map(word => word.replace(/[^\w]/g, ''))
        .filter(word => word.length > 0);
    };

    const sentenceWords = getSignificantWords(normalizedSentence);
    const textWords = getSignificantWords(normalizedText);
    
    if (sentenceWords.length > 0) {
      console.log(`🔍 Trying word-based matching with ${sentenceWords.length} words from sentence`);
      
      // Find the best matching position by looking for word sequences
      for (let i = 0; i <= textWords.length - Math.min(2, sentenceWords.length); i++) {
        let matchCount = 0;
        let consecutiveMatches = 0;
        let maxConsecutive = 0;
        
        // Check how many words match in sequence
        for (let j = 0; j < sentenceWords.length && i + j < textWords.length; j++) {
          if (textWords[i + j] === sentenceWords[j] || 
              textWords[i + j].includes(sentenceWords[j]) || 
              sentenceWords[j].includes(textWords[i + j])) {
            matchCount++;
            consecutiveMatches++;
            maxConsecutive = Math.max(maxConsecutive, consecutiveMatches);
          } else {
            consecutiveMatches = 0;
          }
        }
        
        const matchRatio = matchCount / sentenceWords.length;
        console.log(`🔍 Position ${i}: match ratio ${matchRatio} (${matchCount}/${sentenceWords.length}), max consecutive: ${maxConsecutive}`);
        
        // Accept if we have good match ratio or good consecutive matches
        if (matchRatio >= 0.6 || (maxConsecutive >= 3 && matchRatio >= 0.4)) {
          // Find the approximate character position
          const approximateIndex = normalizedText.indexOf(textWords[i]);
          if (approximateIndex !== -1) {
            console.log(`✅ Word-based match found at approximate index ${approximateIndex}`);
            return approximateIndex;
          }
        }
      }
    }

    // Strategy 4: Substring matching for very short sentences or phrases
    if (normalizedSentence.length < 50) {
      const words = sentenceWords.slice(0, 3); // Take first 3 significant words
      for (const word of words) {
        if (word.length > 2) {
          const wordIndex = normalizedText.indexOf(word);
          if (wordIndex !== -1) {
            console.log(`✅ Substring match found for word "${word}" at index ${wordIndex}`);
            return wordIndex;
          }
        }
      }
    }

    console.log(`❌ No match found for ${detectedLang} sentence after all strategies`);
    return -1;
  };

  // Enhanced sentence checking function
  const doesTextContainSentence = (text: string, sentence: string): boolean => {
    const result = findSentenceInText(text, sentence) !== -1;
    console.log(`🔍 doesTextContainSentence result: ${result} for sentence: "${sentence.substring(0, 30)}..."`);
    return result;
  };

  // Handle reference link navigation based on detected language from response
  const handleReferenceClick = (url: string, referenceNumber?: number) => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Find the corresponding sentence analysis item to get source type
    const sentenceItem = sentenceAnalysis?.find((item, index) => (index + 1) === referenceNumber);
    const sourceType = sentenceItem?.source_type;

    // Detect language from the actual response text instead of selectedLanguage prop
    const detectedLanguage = detectLanguageFromText(text);

    // Debug logging
    console.log('Reference click - selectedLanguage prop:', selectedLanguage);
    console.log('Reference click - detected language from text:', detectedLanguage);
    console.log('Reference click - original URL:', url);
    console.log('Reference click - source type:', sourceType);
    console.log('Reference click - sample text for detection:', text.substring(0, 100));

    // Handle file:// URLs (local documents/PDFs/audio files)
    if (url.startsWith('file://')) {
      console.log('Local file detected, cannot open directly in browser');
      // For local files, we could show a message or handle differently
      alert(`This reference points to a local ${sourceType || 'file'}: ${sentenceItem?.source_title || 'Unknown'}`);
      return;
    }

    // Handle "Not found" or invalid URLs
    if (url === 'Not found' || url === 'N/A') {
      console.log('Invalid URL detected');
      alert('This reference does not have a valid URL to open.');
      return;
    }

    if (detectedLanguage === 'english') {
      // For English, open external URL in new tab
      console.log('Opening external URL in new tab');
      window.open(url, '_blank', 'noopener,noreferrer');
    } else {
      // For non-English languages, open language-specific page in NEW TAB with URL parameters
      try {
        const urlObj = new URL(url);
        const domain = urlObj.hostname;

        // Create query parameters for the language page
        const params = new URLSearchParams({
          url: url,
          domain: domain,
          referenceNumber: (referenceNumber || 1).toString(),
          returnUrl: window.location.pathname + window.location.search,
          sourceType: sourceType || 'unknown'
        });

        const languagePage = `/${detectedLanguage}?${params.toString()}`;

        // Open language page in NEW TAB instead of same tab
        console.log('Opening language page in new tab with params:', languagePage);
        window.open(languagePage, '_blank', 'noopener,noreferrer');
      } catch (error) {
        console.error('Error parsing URL for navigation:', error);
        // Fallback to simple navigation in new tab
        const languagePage = `/${detectedLanguage}`;
        window.open(languagePage, '_blank', 'noopener,noreferrer');
      }
    }
  };

  // Handle question selection
  const handleQuestionSelect = (question: string) => {
    console.log("🔄 PerplexityStyleResponse: handleQuestionSelect called with:", question);
    
    if (onSelectQuestion) {
      // Remove markdown formatting from the question
      const cleanQuestion = question.replace(/\*\*/g, '');
      console.log("✅ PerplexityStyleResponse: Calling onSelectQuestion with:", cleanQuestion);
      
      // Call onSelectQuestion with the cleaned question
      onSelectQuestion(cleanQuestion);
      
      // Also try to update the input directly as a fallback
      const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;
      if (inputElement) {
        inputElement.value = cleanQuestion;
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
        inputElement.focus();
      }
    } else {
      console.warn("❌ PerplexityStyleResponse: onSelectQuestion prop is not provided!");
    }
  };

  // Ref callback that properly handles the TypeScript typing
  const setReferenceRef = (referenceNumber: number) => (el: HTMLButtonElement | null) => {
    referenceRefs.current[referenceNumber] = el;
  };

  // Add logging for debugging
  useEffect(() => {
    // Log the props for debugging
    console.log("PerplexityStyleResponse props:", { text, sentenceAnalysis, relatedQuestions, selectedLanguage });

    // Explicitly log related questions
    if (relatedQuestions && relatedQuestions.length > 0) {
      console.log("PerplexityStyleResponse has related questions:", relatedQuestions);
    } else {
      console.warn("PerplexityStyleResponse has no related questions");
    }

    // Log language information
    console.log("PerplexityStyleResponse selectedLanguage:", selectedLanguage);
    console.log("PerplexityStyleResponse detected language from text:", detectLanguageFromText(text));

    // Debug sentence analysis matching
    if (sentenceAnalysis && sentenceAnalysis.length > 0) {
      console.log("🔍 Sentence Analysis Debug:");
      console.log("📊 Total sentences in analysis:", sentenceAnalysis.length);
      
      sentenceAnalysis.forEach((item, index) => {
        const found = doesTextContainSentence(text, item.sentence);
        console.log(`📊 Sentence ${index + 1} found in text:`, found);
        if (!found) {
          console.log(`❌ Missing sentence: "${item.sentence.substring(0, 100)}..."`);
          console.log(`🔍 Text sample: "${text.substring(0, 200)}..."`);
        }
      });
    }
  }, [text, sentenceAnalysis, relatedQuestions, selectedLanguage]);

  // Ref callback for reference number spans
  const setReferenceSpanRef = (referenceNumber: number) => (el: HTMLSpanElement | null) => {
    referenceSpanRefs.current[referenceNumber] = el;
  };

  // Handle mouse enter on reference number with improved debounce
  const handleReferenceMouseEnter = (referenceNumber: number, url: string, summary?: string, source_title?: string, source_type?: string, file_id?: string, page?: string) => {
    // Clear any existing hide timer
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }

    // Set active reference immediately for visual feedback
    setActiveReference(referenceNumber);

    // Clear any existing preview timer
    if (previewTimerRef.current) {
      clearTimeout(previewTimerRef.current);
    }

    // Use a small timeout to prevent flickering when moving mouse quickly
    previewTimerRef.current = setTimeout(() => {
      // Get position of the reference span
      const spanElement = referenceSpanRefs.current[referenceNumber];
      if (spanElement) {
        const rect = spanElement.getBoundingClientRect();
        setPreviewData({
          url,
          summary,
          source_title,
          source_type,
          file_id,
          page,
          referenceNumber,
          position: {
            x: rect.left,
            y: rect.top
          }
        });
      }
      previewTimerRef.current = null;
    }, 100); // Reduced delay for better responsiveness
  };

  // Handle mouse leave with improved stability
  const handleReferenceMouseLeave = () => {
    // Don't hide immediately, wait to see if user is moving to another reference
    // or to the preview itself
    hideTimerRef.current = setTimeout(() => {
      if (!isHoveringPreviewRef.current) {
        setActiveReference(null);
        setPreviewData(null);
      }
      hideTimerRef.current = null;
    }, 200);
  };

  // Removed the auto-scrolling effect when hovering references
  // This prevents the page from jumping around when hovering references

  // Cleanup effect to clear any timers when component unmounts
  useEffect(() => {
    return () => {
      if (previewTimerRef.current) {
        clearTimeout(previewTimerRef.current);
      }
      if (hideTimerRef.current) {
        clearTimeout(hideTimerRef.current);
      }
    };
  }, []);

  // Helper functions for processing markdown elements
  const processBold = (text: string): React.ReactNode[] => {
    const boldRegex = /\*\*(.*?)\*\*/g;
    const parts = text.split(boldRegex);

    if (parts.length === 1) {
      return [text]; // No bold formatting found
    }

    const result: React.ReactNode[] = [];

    // Process the parts - odd indices are the bold text
    parts.forEach((part, index) => {
      if (index % 2 === 0) {
        // Regular text
        if (part) result.push(part);
      } else {
        // Enhanced bold text with better styling
        result.push(
          <strong 
            key={`bold-helper-${index}`} 
            className="font-bold text-gray-900 dark:text-gray-100"
            style={{
              fontWeight: '700',
              letterSpacing: '0.01em'
            }}
          >
            {part}
          </strong>
        );
      }
    });

    return result;
  };

  const processItalic = (nodes: React.ReactNode[]): React.ReactNode[] => {
    const result: React.ReactNode[] = [];

    nodes.forEach((node, nodeIndex) => {
      if (typeof node !== 'string') {
        // If it's already a React element, keep it as is
        result.push(node);
        return;
      }

      const text = node as string;
      const italicRegex = /\*(.*?)\*/g;
      const parts = text.split(italicRegex);

      if (parts.length === 1) {
        result.push(text); // No italic formatting found
        return;
      }

      // Process the parts - odd indices are the italic text
      parts.forEach((part, index) => {
        if (index % 2 === 0) {
          // Regular text
          if (part) result.push(part);
        } else {
          // Enhanced italic text with better styling
          result.push(
            <em 
              key={`italic-helper-${nodeIndex}-${index}`} 
              className="italic text-gray-800 dark:text-gray-200 font-medium"
              style={{
                fontStyle: 'italic',
                letterSpacing: '0.01em'
              }}
            >
              {part}
            </em>
          );
        }
      });
    });

    return result;
  };

  // Enhanced numbered lists with better multilingual support and improved spacing
  const processNumberedList = (paragraph: string, index: number): React.ReactNode => {
    const match = paragraph.trim().match(/^(\d+)\.\s(.*)$/);
    if (match) {
      const [, number, content] = match;
      const boldProcessed = processBold(content);
      const italicProcessed = processItalic(boldProcessed);
      return (
        <li 
          key={`numbered-${index}`} 
          className="mb-6 text-base leading-relaxed text-gray-800 dark:text-gray-200 flex items-start"
          style={{
            wordBreak: 'break-word',
            overflowWrap: 'break-word',
            textAlign: 'justify',
            lineHeight: '1.8'
          }}
        >
          <span className="font-bold text-primaryColor mr-4 text-lg flex-shrink-0 mt-0.5 min-w-[2.5rem] bg-primaryColor/10 rounded-full px-2 py-1 text-center">
            {number}
          </span>
          <span className="flex-1 font-medium leading-relaxed pt-1">{italicProcessed}</span>
        </li>
      );
    }
    return null;
  };

  // Enhanced function to clean and process multilingual text content with better bullet point support
  const cleanTextContent = (content: string): string => {
    if (!content || typeof content !== 'string') return content;
    
    console.log("Cleaning multilingual text content:", content.substring(0, 200) + "...");
    console.log("Original content length:", content.length);
    
    // Detect if this is regional language content
    const isRegionalLanguage = /[\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/.test(content);
    
    if (isRegionalLanguage) {
      console.log("🌏 Regional language content detected - applying minimal cleaning to preserve text integrity");
      
      // For regional languages, apply only essential cleaning to prevent truncation
      let cleaned = content
        // Only fix Unicode normalization
        .normalize('NFC')
        // Only remove excessive whitespace (4+ spaces to 2 spaces)
        .replace(/[ \t]{4,}/g, '  ')
        // Only fix excessive line breaks (4+ to 2)
        .replace(/\n{4,}/g, '\n\n')
        // Only remove problematic placeholders if they exist
        .replace(/__capital_word_\w+__/gi, '[PRESERVED_WORD]')
        .trim();
      
      console.log("🌏 Minimal cleaning applied. Length change:", content.length, "->", cleaned.length);
      return cleaned;
    }
    
    // NOTE: Capital word preservation is handled by the backend translation service
    // Frontend should NOT create any placeholders - backend already handles this correctly
    let cleaned = content;
    
    // Check for any remaining problematic placeholders that shouldn't be there
    const problematicPlaceholders = cleaned.match(/__capital_word_\w+__/gi);
    if (problematicPlaceholders) {
      console.warn("PerplexityStyleResponse: Found problematic placeholders that should have been handled by backend:", problematicPlaceholders);
      // Remove these problematic placeholders as they indicate a system issue
      cleaned = cleaned.replace(/__capital_word_\w+__/gi, '[PRESERVED_WORD]');
    }
    
    // Enhanced cleaning for English text with better Unicode support and bullet point formatting
    cleaned = cleaned
      // Remove excessive repetition of English words (more conservative)
      .replace(/(\b[A-Za-z]{3,}\b)(\s+\1){2,}/g, '$1')
      // Fix broken sentence patterns with standard punctuation
      .replace(/([.!?])\s*([.!?])+/g, '$1')
      // Clean up multiple spaces but preserve intentional spacing for lists
      .replace(/[ \t]{4,}/g, '  ')
      // Fix broken punctuation spacing for English text
      .replace(/([.!?])\s*([^\s\n.!?])/g, '$1 $2')
      // Fix broken line breaks that cut off sentences
      .replace(/([^\s.!?\n])\s*\n\s*([^\s\n])/g, '$1 $2')
      // Enhanced bullet point and list formatting
      // Convert various bullet point formats to standard markdown
      .replace(/^[\s]*[•·▪▫‣⁃]\s*/gm, '- ')
      .replace(/^[\s]*[*]\s+/gm, '- ')
      .replace(/^[\s]*[-]\s*/gm, '- ')
      // Fix numbered lists with proper spacing
      .replace(/^[\s]*(\d+)[\.\)]\s*/gm, '$1. ')
      // Fix broken list items that might be missing content
      .replace(/^([-*]\s*)$/gm, '')
      .replace(/^(\d+\.\s*)$/gm, '')
      // Ensure proper line breaks before headers
      .replace(/([^\n])\n(#{1,6}\s)/g, '$1\n\n$2')
      // Ensure proper line breaks after headers
      .replace(/(#{1,6}[^\n]*)\n([^\n#])/g, '$1\n\n$2')
      // Fix paragraph spacing - ensure double line breaks between paragraphs
      .replace(/([.!?])\s*\n\s*([^\s\n-#\d])/g, '$1\n\n$2')
      // Ensure proper spacing around lists
      .replace(/([^\n])\n([-*]\s)/g, '$1\n\n$2')
      .replace(/([^\n])\n(\d+\.\s)/g, '$1\n\n$2')
      // Fix incomplete sentences or fragments at line endings
      .replace(/([^\s.!?])\s*\n\s*([.!?])/g, '$1$2')
      // Clean up excessive line breaks but preserve intentional spacing
      .replace(/\n{4,}/g, '\n\n\n')
      // Normalize line breaks but preserve paragraph structure
      .replace(/\n\s*\n\s*\n+/g, '\n\n')
      // Fix common Unicode normalization issues
      .normalize('NFC')
      .trim();
    
    // Additional validation for multilingual content
    if (cleaned !== content) {
      console.log("Multilingual text cleaning applied:");
      console.log("Original length:", content.length);
      console.log("Cleaned length:", cleaned.length);
      console.log("Contains Tamil:", /[\u0B80-\u0BFF]/.test(cleaned));
      console.log("Contains Telugu:", /[\u0C00-\u0C7F]/.test(cleaned));
      console.log("Contains Kannada:", /[\u0C80-\u0CFF]/.test(cleaned));
    }
    
    return cleaned;
  };

  // Function to process markdown formatting
  const processMarkdown = (content: string, keyPrefix: string = 'md'): React.ReactNode => {
    if (!content) return content;
    
    // Clean the content first
    const cleanedContent = cleanTextContent(content);

    // Stage 3: Handle headers (lines starting with # or ## or ###)
    const processHeaders = (text: string, keyPrefix: string = 'header'): React.ReactNode => {
      if (!text) return text;

      const trimmedText = text.trim();

      // Enhanced header styling with better multilingual support
      if (trimmedText.startsWith('### ')) {
        return (
          <h3 
            key={`${keyPrefix}-h3`} 
            className="text-lg font-bold mt-8 mb-5 text-gray-900 dark:text-gray-100 leading-tight tracking-wide"
            style={{
              fontWeight: '700',
              letterSpacing: '0.025em',
              wordBreak: 'break-word'
            }}
          >
            {trimmedText.substring(4)}
          </h3>
        );
      } else if (trimmedText.startsWith('## ')) {
        return (
          <h2 
            key={`${keyPrefix}-h2`} 
            className="text-xl font-bold mt-10 mb-6 text-gray-900 dark:text-gray-100 border-b-2 border-primaryColor/20 pb-3 leading-tight tracking-wide"
            style={{
              fontWeight: '700',
              letterSpacing: '0.025em',
              wordBreak: 'break-word'
            }}
          >
            {trimmedText.substring(3)}
          </h2>
        );
      } else if (trimmedText.startsWith('# ')) {
        return (
          <h1 
            key={`${keyPrefix}-h1`} 
            className="text-2xl font-bold mt-12 mb-8 text-gray-900 dark:text-gray-100 border-b-2 border-primaryColor/30 pb-4 leading-tight tracking-wide"
            style={{
              fontWeight: '800',
              letterSpacing: '0.025em',
              wordBreak: 'break-word'
            }}
          >
            {trimmedText.substring(2)}
          </h1>
        );
      }

      // Enhanced bullet point processing with better styling and spacing
      if (trimmedText.startsWith('- ') || trimmedText.startsWith('• ') || trimmedText.startsWith('* ')) {
        const listContent = trimmedText.substring(2);
        const boldProcessed = processBold(listContent);
        const italicProcessed = processItalic(boldProcessed);
        return (
          <li 
            key={`${keyPrefix}-li`} 
            className="mb-5 text-base leading-relaxed text-gray-800 dark:text-gray-200 font-medium pl-2 flex items-start"
            style={{
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              textAlign: 'justify',
              lineHeight: '1.8'
            }}
          >
            <span className="text-primaryColor font-bold mr-3 mt-1 flex-shrink-0 text-lg">•</span>
            <span className="flex-1">{italicProcessed}</span>
          </li>
        );
      }

      // If it's not a header or list item, process other markdown elements
      const boldProcessed = processBold(text);
      const italicProcessed = processItalic(boldProcessed);

      // For regular text, return as a React fragment to prevent unwanted p nesting
      return <React.Fragment key={`${keyPrefix}-fragment`}>{italicProcessed}</React.Fragment>;
    };

    // Check if the content is a header or list item first
    // Handle headers and list items first
    const trimmedContent = cleanedContent.trim();
    if (trimmedContent.startsWith('#') || trimmedContent.startsWith('- ')) {
      return processHeaders(cleanedContent, keyPrefix);
    }

    // Otherwise process other markdown elements
    const boldProcessed = processBold(cleanedContent);
    const italicProcessed = processItalic(boldProcessed);

    return italicProcessed;
  };

  // If there's no sentence analysis data, process the text with proper paragraph formatting
  if (!sentenceAnalysis || !Array.isArray(sentenceAnalysis) || sentenceAnalysis.length === 0) {
    console.log("📝 Processing text without sentence analysis");
    
    // Clean the text first before processing
    const originalLength = text.length;
    const cleanedText = cleanTextContent(text);
    const cleanedLength = cleanedText.length;
    
    // Check for significant text loss during cleaning
    const textLossPercentage = ((originalLength - cleanedLength) / originalLength) * 100;
    if (textLossPercentage > 20) {
      console.warn(`⚠️ Significant text loss detected during cleaning: ${textLossPercentage.toFixed(1)}% (${originalLength} -> ${cleanedLength} chars)`);
      console.warn("🔧 Using original text to prevent truncation");
      // Use original text if too much was lost during cleaning
      const paragraphs = text.split('\n').filter(p => p.trim() !== '');
      return (
        <div className="relative" style={textStyles}>
          <div className="mb-8 space-y-6 text-gray-900 dark:text-gray-100 leading-relaxed">
            {paragraphs.map((paragraph, index) => (
              <p 
                key={`safe-para-${index}`} 
                className="mb-7 leading-relaxed text-base text-gray-800 dark:text-gray-200 text-justify font-normal"
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  lineHeight: '1.8',
                  marginBottom: '1.75rem'
                }}
              >
                {paragraph.trim()}
              </p>
            ))}
          </div>
          
          {/* Related Questions section for safe rendering */}
          {relatedQuestions && relatedQuestions.length > 0 && (
            <div className="mt-8 pt-6 border-t-2 border-primaryColor/15">
              <div className="flex items-center justify-between mb-5">
                <h3 className="text-base font-bold text-gray-900 dark:text-gray-100 flex items-center tracking-wide">
                  <PiLightbulb className="h-5 w-5 mr-3 text-primaryColor" />
                  Related Questions
                </h3>
              </div>
              <div className="flex flex-col gap-4">
                {relatedQuestions.slice(0, 3).map((question, index) => (
                  <motion.div
                    key={`safe-related-question-${index}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="border border-primaryColor/15 rounded-xl p-4 hover:bg-primaryColor/5 hover:border-primaryColor/25 transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    <div
                      className="flex justify-between items-center cursor-pointer group"
                      onClick={() => handleQuestionSelect(question)}
                    >
                      <div 
                        className="text-sm text-gray-800 dark:text-gray-200 flex-1 font-medium leading-relaxed pr-3"
                        style={{
                          wordBreak: 'break-word',
                          overflowWrap: 'break-word',
                          lineHeight: '1.6'
                        }}
                      >
                        {question.replace(/\*\*/g, '')}
                      </div>
                      <div className="text-white p-2 rounded-lg bg-gradient-to-r from-primaryColor to-blue-600 shadow-sm group-hover:shadow-md transition-shadow duration-200 flex-shrink-0">
                        <PiCaretRight className="h-4 w-4" />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    } else {
      console.log(`✅ Text cleaning completed successfully. Length change: ${originalLength} -> ${cleanedLength} chars (${textLossPercentage.toFixed(1)}% change)`);
    }
    
    const paragraphs = cleanedText.split('\n').filter(p => p.trim() !== '');
    const hasNumberedList = paragraphs.some(p => /^\d+\.\s/.test(p.trim()));
    const isList = paragraphs.some(p => {
      const trimmed = p.trim();
      return trimmed.startsWith('- ') || trimmed.startsWith('• ') || trimmed.startsWith('* ') || 
             trimmed.startsWith('▪ ') || trimmed.startsWith('▫ ') || trimmed.startsWith('‣ ');
    });

    const processedContent = paragraphs.map((paragraph, index) => {
      const trimmed = paragraph.trim();
      
      // Handle numbered lists
      if (/^\d+\.\s/.test(trimmed)) {
        return processNumberedList(paragraph, index);
      }
      
      // Handle headers and regular lists
      const processed = processMarkdown(paragraph, `no-ref-${index}`);
      
      if (React.isValidElement(processed) && 
          typeof processed.type === 'string' && 
          ['h1','h2','h3','li'].includes(processed.type)) {
        return processed;
      }
      
      // Regular paragraphs with enhanced multilingual styling
      if (trimmed.startsWith('#') || trimmed.startsWith('- ') || trimmed.startsWith('• ') || 
          trimmed.startsWith('* ') || trimmed.startsWith('▪ ') || trimmed.startsWith('▫ ') || 
          trimmed.startsWith('‣ ')) {
        return processed;
      }
      
      return (
        <p 
          key={`no-ref-para-${index}`} 
          className="mb-7 leading-relaxed text-base text-gray-800 dark:text-gray-200 text-justify font-normal"
          style={{
            wordBreak: 'break-word',
            overflowWrap: 'break-word',
            hyphens: 'auto',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '1.8',
            marginBottom: '1.75rem'
          }}
        >
          {processed}
        </p>
      );
    });

    // Wrap content appropriately
    const wrappedContent = (() => {
      if (hasNumberedList) {
        const groupedContent: React.ReactNode[] = [];
        let currentNumberedItems: React.ReactNode[] = [];
        
        processedContent.forEach((item, index) => {
          if (React.isValidElement(item) && item.key?.toString().includes('numbered-')) {
            currentNumberedItems.push(item);
          } else {
            if (currentNumberedItems.length > 0) {
              groupedContent.push(
                <ol key={`no-ref-numbered-list-${groupedContent.length}`} className="list-none pl-0 my-8 space-y-5 bg-gray-50 dark:bg-gray-800/30 rounded-lg p-6 border-l-4 border-primaryColor/30 shadow-sm">
                  {currentNumberedItems}
                </ol>
              );
              currentNumberedItems = [];
            }
            groupedContent.push(item);
          }
        });
        
        if (currentNumberedItems.length > 0) {
          groupedContent.push(
            <ol key={`no-ref-numbered-list-${groupedContent.length}`} className="list-none pl-0 my-8 space-y-5 bg-gray-50 dark:bg-gray-800/30 rounded-lg p-6 border-l-4 border-primaryColor/30 shadow-sm">
              {currentNumberedItems}
            </ol>
          );
        }
        
        return groupedContent;
      } else if (isList) {
        return (
          <ul className="list-none pl-0 my-8 space-y-3 bg-gray-50 dark:bg-gray-800/30 rounded-lg p-6 border-l-4 border-primaryColor/30 shadow-sm">
            {processedContent}
          </ul>
        );
      } else {
        return processedContent;
      }
    })();

    return (
      <div className="relative" style={textStyles}>
        <div className="mb-8 space-y-6 text-gray-900 dark:text-gray-100 leading-relaxed">
          {wrappedContent}
        </div>
        
        {/* Related Questions section for no-reference case */}
        {relatedQuestions && relatedQuestions.length > 0 && (
          <>
            <div className="mt-8 pt-6 border-t-2 border-primaryColor/15">
              <div className="flex items-center justify-between mb-5">
                <h3 className="text-base font-bold text-gray-900 dark:text-gray-100 flex items-center tracking-wide">
                  <PiLightbulb className="h-5 w-5 mr-3 text-primaryColor" />
                  Related Questions
                </h3>
                <button
                  onClick={() => setShowRelatedModal(true)}
                  className="text-sm text-primaryColor hover:text-primaryColor/80 transition-colors duration-200 flex items-center font-medium px-3 py-1 rounded-md hover:bg-primaryColor/5"
                >
                  View All
                  <PiCaretRight className="ml-1 h-3 w-3" />
                </button>
              </div>
              <div className="flex flex-col gap-4">
                {relatedQuestions.slice(0, 3).map((question, index) => (
                  <motion.div
                    key={`no-ref-related-question-${index}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="border border-primaryColor/15 rounded-xl p-4 hover:bg-primaryColor/5 hover:border-primaryColor/25 transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    <div
                      className="flex justify-between items-center cursor-pointer group"
                      onClick={() => handleQuestionSelect(question)}
                    >
                      <div 
                        className="text-sm text-gray-800 dark:text-gray-200 flex-1 font-medium leading-relaxed pr-3"
                        style={{
                          wordBreak: 'break-word',
                          overflowWrap: 'break-word',
                          lineHeight: '1.6'
                        }}
                      >
                        {question.replace(/\*\*/g, '')}
                      </div>
                      <div className="text-white p-2 rounded-lg bg-gradient-to-r from-primaryColor to-blue-600 shadow-sm group-hover:shadow-md transition-shadow duration-200 flex-shrink-0">
                        <PiCaretRight className="h-4 w-4" />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Enhanced Modal for Related Questions */}
            <AnimatePresence>
              {showRelatedModal && (
                <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: 20 }}
                    transition={{ duration: 0.3, ease: "easeOut" }}
                    className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-3xl w-full max-h-[85vh] overflow-hidden flex flex-col border border-gray-200 dark:border-gray-700"
                  >
                    <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gradient-to-r from-primaryColor/5 to-blue-50 dark:from-primaryColor/10 dark:to-blue-900/20">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                        <PiLightbulb className="h-6 w-6 mr-3 text-primaryColor" />
                        All Related Questions
                      </h3>
                      <button
                        onClick={() => setShowRelatedModal(false)}
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                      >
                        <PiX className="h-6 w-6" />
                      </button>
                    </div>
                    <div className="p-6 overflow-y-auto flex-1">
                      <div className="grid gap-4">
                        {relatedQuestions.map((question, index) => (
                          <motion.div
                            key={`no-ref-modal-question-${index}`}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.2, delay: index * 0.05 }}
                            className="border border-primaryColor/15 rounded-xl p-4 hover:bg-primaryColor/5 hover:border-primaryColor/25 transition-all duration-200 shadow-sm hover:shadow-md"
                          >
                            <div
                              className="flex justify-between items-center cursor-pointer group"
                              onClick={() => {
                                handleQuestionSelect(question);
                                setShowRelatedModal(false);
                              }}
                            >
                              <div 
                                className="text-sm text-gray-800 dark:text-gray-200 flex-1 font-medium leading-relaxed pr-4"
                                style={{
                                  wordBreak: 'break-word',
                                  overflowWrap: 'break-word',
                                  lineHeight: '1.6'
                                }}
                              >
                                {question.replace(/\*\*/g, '')}
                              </div>
                              <div className="bg-gradient-to-r from-primaryColor to-blue-600 text-white p-2 rounded-lg shadow-sm group-hover:shadow-md transition-shadow duration-200 flex-shrink-0">
                                <PiCaretRight className="h-4 w-4" />
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                </div>
              )}
            </AnimatePresence>
          </>
        )}
      </div>
    );
  }

  // Create a map of sentences to their reference numbers
  const sentenceToReferenceMap = new Map<string, number>();
  sentenceAnalysis.forEach((item, index) => {
    sentenceToReferenceMap.set(item.sentence, index + 1);
  });

  // Split the text into paragraphs and handle different content types
  // Clean the text first before processing
  console.log("📝 Processing text with sentence analysis");
  const originalMainLength = text.length;
  const cleanedMainText = cleanTextContent(text);
  const cleanedMainLength = cleanedMainText.length;
  
  // Check for significant text loss during cleaning
  const mainTextLossPercentage = ((originalMainLength - cleanedMainLength) / originalMainLength) * 100;
  if (mainTextLossPercentage > 20) {
    console.warn(`⚠️ Significant text loss detected during main text cleaning: ${mainTextLossPercentage.toFixed(1)}% (${originalMainLength} -> ${cleanedMainLength} chars)`);
    console.warn("🔧 Using original text to prevent truncation");
    
    // Use original text if too much was lost during cleaning
    const safeParagraphs = text.split('\n').filter(p => p.trim() !== '');
    const safeProcessedParagraphs = safeParagraphs.map((paragraph, paragraphIndex) => (
      <p 
        key={`safe-main-para-${paragraphIndex}`} 
        className="mb-7 leading-relaxed text-base text-gray-800 dark:text-gray-200 text-justify font-normal"
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
          lineHeight: '1.8',
          marginBottom: '1.75rem'
        }}
      >
        {paragraph.trim()}
      </p>
    ));
    
    return (
      <div className="relative" style={textStyles}>
        <div className="mb-8 space-y-6 text-gray-900 dark:text-gray-100">
          {safeProcessedParagraphs}
        </div>
        
        {/* Emergency fallback references for regional languages */}
        {sentenceAnalysis && sentenceAnalysis.length > 0 && (
          <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
            <div className="flex items-center mb-3">
              <PiInfo className="h-5 w-5 mr-2 text-amber-600 dark:text-amber-400" />
              <h4 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                Quick References
              </h4>
            </div>
            <div className="flex flex-wrap gap-2">
              {sentenceAnalysis.slice(0, 5).map((item, index) => (
                <button
                  key={`safe-emergency-ref-${index}`}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-amber-100 dark:bg-amber-800/30 text-amber-700 dark:text-amber-300 rounded-md text-xs font-medium hover:bg-amber-200 dark:hover:bg-amber-700/30 transition-colors duration-200"
                  onClick={() => handleReferenceClick(item.url, index + 1)}
                  title={item.source_title || item.url}
                >
                  <span className="font-bold">[{index + 1}]</span>
                  <span className="max-w-[80px] truncate">
                    {item.source_title ? item.source_title.substring(0, 15) + '...' : 'Source'}
                  </span>
                </button>
              ))}
            </div>
            <p className="text-xs text-amber-600 dark:text-amber-400 mt-2">
              Text processing was simplified to prevent truncation. References are available above.
            </p>
          </div>
        )}
        
        {/* Related Questions section for safe rendering */}
        {relatedQuestions && relatedQuestions.length > 0 && (
          <div className="mt-6 pt-4 border-t border-primaryColor/10">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-n700 dark:text-n30 flex items-center">
                <PiLightbulb className="h-4 w-4 mr-2 text-primaryColor" />
                Related Questions
              </h3>
            </div>
            <div className="flex flex-col gap-3">
              {relatedQuestions.slice(0, 3).map((question, index) => (
                <motion.div
                  key={`safe-main-related-question-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="border border-primaryColor/10 rounded-lg p-3 hover:bg-primaryColor/5 transition-colors duration-200"
                >
                  <div
                    className="flex justify-between items-center cursor-pointer"
                    onClick={() => handleQuestionSelect(question)}
                  >
                    <div className="text-sm text-n700 dark:text-n30 flex-1 font-medium">
                      {question.replace(/\*\*/g, '')}
                    </div>
                    <div className="text-white p-1.5 rounded-md bg-gradient-to-r from-primaryColor to-blue-600 shadow-sm">
                      <PiCaretRight />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  } else {
    console.log(`✅ Main text cleaning completed successfully. Length change: ${originalMainLength} -> ${cleanedMainLength} chars (${mainTextLossPercentage.toFixed(1)}% change)`);
  }
  
  const paragraphs = cleanedMainText.split('\n').filter(p => p.trim() !== '');

  // Check if we have a list of items (enhanced detection)
  const isList = paragraphs.some(p => {
    const trimmed = p.trim();
    return trimmed.startsWith('- ') || trimmed.startsWith('• ') || trimmed.startsWith('* ') || 
           trimmed.startsWith('▪ ') || trimmed.startsWith('▫ ') || trimmed.startsWith('‣ ');
  });

  // Check if we have numbered lists (1. 2. 3. etc.)
  const hasNumberedList = paragraphs.some(p => /^\d+\.\s/.test(p.trim()));

  // Process each paragraph to add reference numbers
  const processedParagraphs = paragraphs.map((paragraph, paragraphIndex) => {
    const trimmedParagraph = paragraph.trim();
    
    // Handle numbered lists
    if (/^\d+\.\s/.test(trimmedParagraph)) {
      return processNumberedList(paragraph, paragraphIndex);
    }

    // Check if this paragraph is a header or list item (enhanced detection)
    if (trimmedParagraph.startsWith('#') || trimmedParagraph.startsWith('- ') || 
        trimmedParagraph.startsWith('• ') || trimmedParagraph.startsWith('* ') || 
        trimmedParagraph.startsWith('▪ ') || trimmedParagraph.startsWith('▫ ') || 
        trimmedParagraph.startsWith('‣ ')) {
      // Find all sentences in this paragraph that have references
      const matchingSentences = sentenceAnalysis.filter(item =>
        doesTextContainSentence(paragraph, item.sentence)
      );

      if (matchingSentences.length === 0) {
        // If no matching sentences, process the markdown formatting
        return processMarkdown(paragraph, `para-${paragraphIndex}`);
      }
    }

    // Find all sentences in this paragraph that have references
    const matchingSentences = sentenceAnalysis.filter(item =>
      doesTextContainSentence(paragraph, item.sentence)
    );

    // FALLBACK: If no sentences match but we have sentence analysis data,
    // try to add references at the end of paragraphs for non-English languages
    // BUT be more conservative for regional languages to prevent text truncation
    if (matchingSentences.length === 0 && sentenceAnalysis.length > 0) {
      const detectedLang = detectLanguageFromText(paragraph);
      const isRegionalLanguage = /[\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/.test(paragraph);
      
      if (detectedLang !== 'english' && !isRegionalLanguage) {
        console.log(`🔄 Applying fallback reference strategy for ${detectedLang} paragraph`);
        
        // For non-English languages (but not regional languages), add references at the end of each paragraph
        // This ensures users can still see and access the references
        const processed = processMarkdown(paragraph, `para-content-${paragraphIndex}`);
        
        // Add references at the end of the paragraph
        const fallbackReferences = sentenceAnalysis.slice(0, 3).map((item, index) => (
          <span
            key={`fallback-ref-${paragraphIndex}-${index}`}
            ref={setReferenceSpanRef(index + 1)}
            className="reference-number-hover inline-flex items-center justify-center ml-1 text-[10px] font-medium text-primaryColor bg-primaryColor/10 rounded-full w-[16px] h-[16px] hover:bg-primaryColor/20 transition-all duration-200 cursor-pointer"
            onMouseEnter={() => handleReferenceMouseEnter(index + 1, item.url, item.summary, item.source_title, item.source_type, item.file_id, item.page)}
            onMouseLeave={handleReferenceMouseLeave}
            onClick={() => handleReferenceClick(item.url, index + 1)}
            style={{ verticalAlign: 'super' }}
            title={`Reference ${index + 1}: ${item.source_title || item.url}`}
          >
            {index + 1}
          </span>
        ));

        // Check if the processed content is a header element
        if (React.isValidElement(processed) &&
            typeof processed.type === 'string' &&
            ['h1','h2','h3','li'].includes(processed.type)) {
          // Return header and list elements with references appended
          return (
            <div key={`paragraph-${paragraphIndex}`}>
              {processed}
              {fallbackReferences.length > 0 && (
                <span className="ml-2">
                  {fallbackReferences}
                </span>
              )}
            </div>
          );
        } else {
          // For non-header content
          const paragraphText = paragraph.trim();
          if (paragraphText.startsWith('### ') || paragraphText.startsWith('## ') || paragraphText.startsWith('# ') || paragraphText.startsWith('- ')) {
            return (
              <div key={`paragraph-${paragraphIndex}`}>
                {processMarkdown(paragraph, `para-content-${paragraphIndex}`)}
                {fallbackReferences.length > 0 && (
                  <span className="ml-2">
                    {fallbackReferences}
                  </span>
                )}
              </div>
            );
          } else {
            // Safe to wrap in <p> tag with proper styling and references
            return (
              <p key={`paragraph-${paragraphIndex}`} className="mb-5 leading-relaxed text-base text-gray-700 dark:text-gray-300 text-justify">
                {processed}
                {fallbackReferences.length > 0 && (
                  <span className="ml-2">
                    {fallbackReferences}
                  </span>
                )}
              </p>
            );
          }
        }
      } else if (isRegionalLanguage) {
        console.log(`🌏 Regional language paragraph detected - skipping fallback references to preserve text integrity`);
        // For regional languages, just process the paragraph normally without aggressive reference matching
        // This prevents text truncation issues
      }
    }

    if (matchingSentences.length === 0) {
      // If no matching sentences, return the paragraph with markdown processing
      const processed = processMarkdown(paragraph, `para-content-${paragraphIndex}`);

      // Check if the processed content is a header element
      if (React.isValidElement(processed) &&
          typeof processed.type === 'string' &&
          ['h1','h2','h3','li'].includes(processed.type)) {
        // Return header and list elements directly without wrapping in <p>
        return processed;
      } else {
        // For non-header content, check if it contains header elements
        const paragraphText = paragraph.trim();
        if (paragraphText.startsWith('### ') || paragraphText.startsWith('## ') || paragraphText.startsWith('# ') || paragraphText.startsWith('- ')) {
          // If the paragraph starts with header or list markdown, process it as such
          return processMarkdown(paragraph, `para-content-${paragraphIndex}`);
        } else {
          // Safe to wrap in <p> tag with proper styling
          return <p key={`paragraph-${paragraphIndex}`} className="mb-5 leading-relaxed text-base text-gray-700 dark:text-gray-300 text-justify">{processed}</p>;
        }
      }
    }

    // Sort matching sentences by their position in the paragraph
    matchingSentences.sort((a, b) => {
      return findSentenceInText(paragraph, a.sentence) - findSentenceInText(paragraph, b.sentence);
    });

    // Process the paragraph to add reference numbers
    let lastIndex = 0;
    const parts: React.ReactNode[] = [];

    matchingSentences.forEach((item, index) => {
      const sentenceIndex = findSentenceInText(paragraph.substring(lastIndex), item.sentence);
      const actualIndex = sentenceIndex === -1 ? -1 : lastIndex + sentenceIndex;

      if (actualIndex === -1) {
        console.warn(`Sentence not found in paragraph for reference: "${item.sentence.substring(0, 50)}..."`);
        return; // Skip if sentence not found
      }

      // Add text before the sentence with markdown processing
      if (actualIndex > lastIndex) {
        parts.push(
          <span key={`before-${paragraphIndex}-${index}`}>
            {processMarkdown(paragraph.substring(lastIndex, actualIndex), `before-md-${paragraphIndex}-${index}`)}
          </span>
        );
      }

      // Get the reference number for this sentence
      const referenceNumber = sentenceToReferenceMap.get(item.sentence);

      // Add the sentence with the reference number and markdown processing
      parts.push(
        <span key={`sentence-${paragraphIndex}-${index}`}>
          {processMarkdown(item.sentence, `sentence-md-${paragraphIndex}-${index}`)}
          <span
            ref={setReferenceSpanRef(referenceNumber || 0)}
            className="reference-number-hover inline-flex items-center justify-center ml-0.5 text-[10px] font-medium text-primaryColor bg-primaryColor/10 rounded-full w-[16px] h-[16px] hover:bg-primaryColor/20 transition-all duration-200"
            onMouseEnter={() => handleReferenceMouseEnter(referenceNumber || 0, item.url, item.summary, item.source_title, item.source_type, item.file_id, item.page)}
            onMouseLeave={handleReferenceMouseLeave}
            // Removed onClick handler to prevent navigation
            style={{ verticalAlign: 'super' }}
          >
            {referenceNumber}
          </span>
        </span>
      );

      lastIndex = actualIndex + item.sentence.length;
    });

    // Add any remaining text after the last matched sentence with markdown processing
    if (lastIndex < paragraph.length) {
      parts.push(
        <span key={`after-${paragraphIndex}`}>
          {processMarkdown(paragraph.substring(lastIndex), `after-md-${paragraphIndex}`)}
        </span>
      );
    }

    // Check if any part contains header elements or if the paragraph starts with header markdown
    const hasHeader = parts.some(part =>
      React.isValidElement(part) &&
      typeof part.type === 'string' &&
      ['h1','h2','h3'].includes(part.type)
    );

    // Also check if the original paragraph text contains header markdown
    const paragraphText = paragraph.trim();
    const containsHeaderMarkdown = paragraphText.includes('### ') || paragraphText.includes('## ') || paragraphText.includes('# ');

    // Enhanced paragraph styling with better multilingual support and improved spacing
    return (hasHeader || containsHeaderMarkdown) ? (
      <div 
        key={`paragraph-${paragraphIndex}`} 
        className="mb-7 leading-relaxed text-base text-gray-800 dark:text-gray-200 font-normal"
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
          lineHeight: '1.8',
          marginBottom: '1.75rem'
        }}
      >
        {parts}
      </div>
    ) : (
      <p 
        key={`paragraph-${paragraphIndex}`} 
        className="mb-7 leading-relaxed text-base text-gray-800 dark:text-gray-200 text-justify font-normal"
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '1.8',
          marginBottom: '1.75rem'
        }}
      >
        {parts}
      </p>
    );
  });

  // Check if we need to wrap list items in appropriate containers
  const wrappedProcessedParagraphs = (() => {
    if (hasNumberedList) {
      // Group numbered list items and regular content
      const groupedContent: React.ReactNode[] = [];
      let currentNumberedItems: React.ReactNode[] = [];
      
      processedParagraphs.forEach((paragraph, index) => {
        if (React.isValidElement(paragraph) && paragraph.key?.toString().includes('numbered-')) {
          currentNumberedItems.push(paragraph);
        } else {
          // If we have accumulated numbered items, wrap them in ol
          if (currentNumberedItems.length > 0) {
            groupedContent.push(
              <ol key={`numbered-list-${groupedContent.length}`} className="list-none pl-0 my-8 space-y-5 bg-gray-50 dark:bg-gray-800/30 rounded-lg p-6 border-l-4 border-primaryColor/30 shadow-sm">
                {currentNumberedItems}
              </ol>
            );
            currentNumberedItems = [];
          }
          groupedContent.push(paragraph);
        }
      });
      
      // Handle any remaining numbered items
      if (currentNumberedItems.length > 0) {
        groupedContent.push(
          <ol key={`numbered-list-${groupedContent.length}`} className="list-none pl-0 my-8 space-y-5 bg-gray-50 dark:bg-gray-800/30 rounded-lg p-6 border-l-4 border-primaryColor/30 shadow-sm">
            {currentNumberedItems}
          </ol>
        );
      }
      
      return groupedContent;
    } else if (isList) {
      return (
        <ul className="list-none pl-0 my-8 space-y-4 bg-gray-50 dark:bg-gray-800/30 rounded-lg p-6 border-l-4 border-primaryColor/30 shadow-sm">
          {processedParagraphs.map((paragraph, index) => (
            <React.Fragment key={`list-item-${index}`}>
              {paragraph}
            </React.Fragment>
          ))}
        </ul>
      );
    } else {
      return processedParagraphs.map((paragraph, index) => (
        <React.Fragment key={`paragraph-wrapper-${index}`}>
          {paragraph}
        </React.Fragment>
      ));
    }
  })();

  // Helper function to check if there are any valid references (not N/A or Not found)
  const hasValidReferences = () => {
    if (!sentenceAnalysis || sentenceAnalysis.length === 0) {
      return false;
    }

    return sentenceAnalysis.some(item =>
      item.url &&
      item.url !== 'N/A' &&
      item.url !== 'Not found' &&
      item.url.trim() !== ''
    );
  };

  return (
    <div className="relative" style={textStyles}>
      <div className="mb-8 space-y-6 text-gray-900 dark:text-gray-100">
        {wrappedProcessedParagraphs}
      </div>

      {/* Related Questions section */}
      {relatedQuestions && relatedQuestions.length > 0 && (
        <>
          <div className="mt-6 pt-4 border-t border-primaryColor/10">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-n700 dark:text-n30 flex items-center">
                <PiLightbulb className="h-4 w-4 mr-2 text-primaryColor" />
                Related Questions
              </h3>
              <button
                onClick={() => setShowRelatedModal(true)}
                className="text-xs text-primaryColor hover:text-primaryColor/80 transition-colors duration-200 flex items-center"
              >
                View All
              </button>
            </div>
            <div className="flex flex-col gap-3">
              {relatedQuestions.slice(0, 3).map((question, index) => (
                <motion.div
                  key={`related-question-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="border border-primaryColor/10 rounded-lg p-3 hover:bg-primaryColor/5 transition-colors duration-200"
                >
                  <div
                    className="flex justify-between items-center cursor-pointer"
                    onClick={() => {
                      // Always select the question, no toggling
                      handleQuestionSelect(question);
                    }}
                  >
                    <div className="text-sm text-n700 dark:text-n30 flex-1 font-medium">
                      {/* Remove markdown formatting from the question */}
                      {question.replace(/\*\*/g, '')}
                    </div>
                    <div className="text-white p-1.5 rounded-md bg-gradient-to-r from-primaryColor to-blue-600 shadow-sm">
                      <PiCaretRight />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Modal for Related Questions */}
          <AnimatePresence>
            {showRelatedModal && (
              <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.2 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col"
                >
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <h3 className="text-lg font-medium text-n700 dark:text-n30">Related Questions</h3>
                    <button
                      onClick={() => setShowRelatedModal(false)}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      <PiX className="h-5 w-5" />
                    </button>
                  </div>
                  <div className="p-4 overflow-y-auto flex-1">
                    <div className="grid gap-3">
                      {relatedQuestions.map((question, index) => (
                        <motion.div
                          key={`modal-question-${index}`}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.2, delay: index * 0.05 }}
                          className="border border-primaryColor/10 rounded-lg p-3 hover:bg-primaryColor/5 transition-colors duration-200"
                        >
                          <div
                            className="flex justify-between items-center cursor-pointer"
                            onClick={() => {
                              handleQuestionSelect(question);
                              setShowRelatedModal(false);
                            }}
                          >
                            <div className="text-sm text-n700 dark:text-n30 flex-1">
                              {question.replace(/\*\*/g, '')}
                            </div>
                            <div className="bg-gradient-to-r from-primaryColor to-blue-600 text-white p-1.5 rounded-md shadow-sm">
                              <PiCaretRight />
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </div>
            )}
          </AnimatePresence>
        </>
      )}

      {/* Emergency fallback references for non-English languages */}
      {sentenceAnalysis && sentenceAnalysis.length > 0 && hasValidReferences() && 
       detectLanguageFromText(text) !== 'english' && 
       !text.includes('[1]') && !text.includes('[2]') && !text.includes('[3]') && (
        <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
          <div className="flex items-center mb-3">
            <PiInfo className="h-5 w-5 mr-2 text-amber-600 dark:text-amber-400" />
            <h4 className="text-sm font-medium text-amber-800 dark:text-amber-200">
              Quick References
            </h4>
          </div>
          <div className="flex flex-wrap gap-2">
            {sentenceAnalysis.slice(0, 5).map((item, index) => (
              <button
                key={`emergency-ref-${index}`}
                className="inline-flex items-center gap-1 px-2 py-1 bg-amber-100 dark:bg-amber-800/30 text-amber-700 dark:text-amber-300 rounded-md text-xs font-medium hover:bg-amber-200 dark:hover:bg-amber-700/30 transition-colors duration-200"
                onClick={() => handleReferenceClick(item.url, index + 1)}
                title={item.source_title || item.url}
              >
                <span className="font-bold">[{index + 1}]</span>
                <span className="max-w-[80px] truncate">
                  {item.source_title ? item.source_title.substring(0, 15) + '...' : 'Source'}
                </span>
              </button>
            ))}
          </div>
          <p className="text-xs text-amber-600 dark:text-amber-400 mt-2">
            References may not be perfectly positioned in the text due to language processing differences.
          </p>
        </div>
      )}

      {/* Enhanced References section at the bottom */}
      {sentenceAnalysis && sentenceAnalysis.length > 0 && hasValidReferences() && (
        <div className="mt-8 pt-6 border-t-2 border-primaryColor/15">
          <div className="flex items-center mb-4">
            <PiLink className="h-5 w-5 mr-3 text-primaryColor" />
            <h3 className="text-base font-bold text-gray-900 dark:text-gray-100 tracking-wide">References</h3>
            <span className="ml-3 text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full">
              {sentenceAnalysis.length} source{sentenceAnalysis.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="flex flex-wrap gap-3">
                {sentenceAnalysis.map((item, index) => {
                  const referenceNumber = index + 1;
                  // Extract domain name for display
                  let domain = '';
                  try {
                    const url = new URL(item.url);
                    domain = url.hostname.replace('www.', '');

                    // Clean up common domain patterns
                    if (domain.includes('youtube.com') || domain.includes('youtu.be')) {
                      domain = 'YouTube';
                    } else if (domain.includes('economictimes.indiatimes.com')) {
                      domain = 'Economic Times';
                    } else if (domain.includes('moneycontrol.com')) {
                      domain = 'MoneyControl';
                    } else if (domain.includes('financialexpress.com')) {
                      domain = 'Financial Express';
                    } else {
                      // Capitalize first letter and remove common TLDs for display
                      domain = domain.split('.')[0];
                      domain = domain.charAt(0).toUpperCase() + domain.slice(1);
                    }
                  } catch (error) {
                    // If URL parsing fails, try to extract a readable name
                    domain = item.url.length > 20 ? item.url.substring(0, 20) + '...' : item.url;
                  }

                  return (
                    <motion.button
                      key={`reference-compact-${index}`}
                      ref={setReferenceRef(referenceNumber)}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.2, delay: index * 0.03 }}
                      className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 shadow-sm hover:shadow-md ${
                        activeReference === referenceNumber
                          ? 'bg-gradient-to-r from-primaryColor to-blue-600 text-white'
                          : 'bg-primaryColor/10 text-primaryColor hover:bg-primaryColor/20 border border-primaryColor/20'
                      }`}
                      onMouseEnter={() => handleReferenceMouseEnter(referenceNumber, item.url, item.summary, item.source_title, item.source_type, item.file_id, item.page)}
                      onMouseLeave={handleReferenceMouseLeave}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleReferenceClick(item.url, referenceNumber);
                      }}
                      title={item.url}
                    >
                      <span className="font-bold text-xs bg-white/20 rounded-full w-5 h-5 flex items-center justify-center">
                        {referenceNumber}
                      </span>
                      <span className="max-w-[100px] truncate font-medium">{domain}</span>
                    </motion.button>
                  );
                })}
          </div>


          {/* Detailed reference information - shown on hover via the preview */}
        </div>
      )}

      {/* Floating preview - positioned absolutely to avoid nesting issues */}
      <AnimatePresence>
        {previewData && previewData.position && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 10 }}
            transition={{ duration: 0.2 }}
            className="fixed z-50 preview-container shadow-xl"
            style={{
              left: `${previewData.position.x}px`,
              top: previewData.position.y < 200 ? `${previewData.position.y + 20}px` : `${previewData.position.y - 200}px`
            }}
            // Add mouse enter/leave handlers to the preview container to prevent it from disappearing too quickly
            onMouseEnter={() => {
              // Track that we're hovering over the preview
              isHoveringPreviewRef.current = true;

              // Clear any hide timer
              if (hideTimerRef.current) {
                clearTimeout(hideTimerRef.current);
                hideTimerRef.current = null;
              }

              // Keep the reference active
              setActiveReference(previewData.referenceNumber);
            }}
            onMouseLeave={() => {
              // Track that we're no longer hovering over the preview
              isHoveringPreviewRef.current = false;

              // Use a delay before hiding the preview
              hideTimerRef.current = setTimeout(() => {
                setActiveReference(null);
                setPreviewData(null);
                hideTimerRef.current = null;
              }, 300);
            }}
          >
            <WebpagePreview
              url={previewData.url}
              summary={previewData.summary}
              source_title={previewData.source_title}
              source_type={previewData.source_type}
              file_id={previewData.file_id}
              page={previewData.page}
              referenceNumber={previewData.referenceNumber}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PerplexityStyleResponse;
