#!/usr/bin/env python3
"""
Test script to verify the language detection fix works correctly
"""

import requests
import json
import re
import os

def test_language_detection_scenarios():
    """Test both Tamil and English queries to verify correct language detection"""
    
    url = f"{os.getenv('BACKEND_URL', 'http://localhost:5010')}/financial_query"
    
    # Test scenarios
    scenarios = [
        {
            "name": "Tamil Query with Tamil Index",
            "description": "Should trigger direct Tamil processing",
            "data": {
                "query": "பழைய செயலக இராணுவ தோட்டத்தில் தீ விபத்து",
                "index_name": "tamil",
                "client_email": "<EMAIL>"
            },
            "expected": {
                "direct_tamil_processing": True,
                "query_translated": False,
                "translation_applied": False,
                "response_in_tamil": True,
                "cross_language_applied": False
            }
        },
        {
            "name": "English Query with Tamil Index",
            "description": "Should NOT trigger direct Tamil processing, should use cross-language",
            "data": {
                "query": "How did the bus that killed 11 people flow into the river?",
                "index_name": "tamil",
                "client_email": "<EMAIL>",
                "language": "Tamil"  # Client preference should be ignored
            },
            "expected": {
                "direct_tamil_processing": False,
                "query_translated": False,
                "translation_applied": False,
                "response_in_tamil": False,
                "cross_language_applied": True
            }
        }
    ]
    
    print("🧪 LANGUAGE DETECTION FIX VERIFICATION")
    print("=" * 70)
    
    all_passed = True
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 SCENARIO {i}: {scenario['name']}")
        print(f"📝 Description: {scenario['description']}")
        print(f"🔤 Query: {scenario['data']['query']}")
        print(f"📊 Index: {scenario['data']['index_name']}")
        if 'language' in scenario['data']:
            print(f"🌐 Client Language Preference: {scenario['data']['language']}")
        print("-" * 50)
        
        try:
            response = requests.post(url, json=scenario['data'], headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                
                # Check each expected outcome
                scenario_passed = True
                
                # Check direct Tamil processing
                actual_direct = result.get('direct_tamil_processing', False)
                expected_direct = scenario['expected']['direct_tamil_processing']
                if actual_direct == expected_direct:
                    print(f"✅ Direct Tamil Processing: {actual_direct} (expected: {expected_direct})")
                else:
                    print(f"❌ Direct Tamil Processing: {actual_direct} (expected: {expected_direct})")
                    scenario_passed = False
                
                # Check query translation
                actual_translated = result.get('query_translated', False)
                expected_translated = scenario['expected']['query_translated']
                if actual_translated == expected_translated:
                    print(f"✅ Query Translated: {actual_translated} (expected: {expected_translated})")
                else:
                    print(f"❌ Query Translated: {actual_translated} (expected: {expected_translated})")
                    scenario_passed = False
                
                # Check translation applied
                actual_translation = result.get('translation_applied', False)
                expected_translation = scenario['expected']['translation_applied']
                if actual_translation == expected_translation:
                    print(f"✅ Translation Applied: {actual_translation} (expected: {expected_translation})")
                else:
                    print(f"❌ Translation Applied: {actual_translation} (expected: {expected_translation})")
                    scenario_passed = False
                
                # Check cross-language processing
                cross_lang = result.get('cross_language_processing', {})
                actual_cross = cross_lang.get('applied', False)
                expected_cross = scenario['expected']['cross_language_applied']
                if actual_cross == expected_cross:
                    print(f"✅ Cross-Language Applied: {actual_cross} (expected: {expected_cross})")
                else:
                    print(f"❌ Cross-Language Applied: {actual_cross} (expected: {expected_cross})")
                    scenario_passed = False
                
                # Check response language
                ai_response = result.get('ai_response', '')
                tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
                has_tamil = bool(tamil_pattern.search(ai_response))
                expected_tamil = scenario['expected']['response_in_tamil']
                if has_tamil == expected_tamil:
                    print(f"✅ Response in Tamil: {has_tamil} (expected: {expected_tamil})")
                else:
                    print(f"❌ Response in Tamil: {has_tamil} (expected: {expected_tamil})")
                    scenario_passed = False
                
                # Show response snippet
                print(f"🤖 AI Response (first 100 chars): {ai_response[:100]}...")
                
                if scenario_passed:
                    print(f"🎉 SCENARIO {i}: PASSED")
                else:
                    print(f"💥 SCENARIO {i}: FAILED")
                    all_passed = False
                    
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"📄 Response: {response.text}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            all_passed = False
    
    print("\n" + "=" * 70)
    print("📋 FINAL RESULTS")
    print("=" * 70)
    
    if all_passed:
        print("🎉 ALL SCENARIOS PASSED!")
        print("✅ Language detection fix is working correctly")
        print("✅ Tamil queries trigger direct Tamil processing")
        print("✅ English queries use cross-language processing")
        print("✅ Client language preferences don't override actual language detection")
    else:
        print("💥 SOME SCENARIOS FAILED!")
        print("❌ Language detection fix needs further investigation")
    
    return all_passed

if __name__ == "__main__":
    test_language_detection_scenarios()
