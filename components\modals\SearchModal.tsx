import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Message } from "@/stores/chatList";
import { useMainModal } from "@/stores/modal";
import Link from "next/link";
import React, { useState, useMemo, useEffect } from "react";
import { PiAlignLeft, PiMagnifyingGlass, PiX } from "react-icons/pi";

export default function SearchModal() {
  const { chatList } = useChatHandler();
  const { modalClose } = useMainModal();
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");

  // Function to extract text content from message
  const extractMessageText = (message: Message): string => {
    if (typeof message.text === "string") {
      return message.text;
    } else if (typeof message.text === "object" && message.text?.ai_response) {
      return message.text.ai_response;
    }
    return "";
  };

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Search functionality
  const filteredChats = useMemo(() => {
    if (!debouncedQuery.trim()) {
      return chatList;
    }

    const query = debouncedQuery.toLowerCase().trim();

    return chatList.filter((chat) => {
      // Search in chat title
      if (chat.title.toLowerCase().includes(query)) {
        return true;
      }

      // Search in index used
      if (chat.indexUsed?.toLowerCase().includes(query)) {
        return true;
      }

      // Search in message content
      const hasMatchingMessage = chat.messages.some((message) => {
        const messageText = extractMessageText(message);
        return messageText.toLowerCase().includes(query);
      });

      return hasMatchingMessage;
    });
  }, [chatList, debouncedQuery]);

  // Function to highlight search terms
  const highlightText = (text: string, query: string) => {
    if (!query.trim()) return text;

    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="bg-primaryColor/20 text-primaryColor font-semibold rounded px-1">
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
  };

  // Handle search result click
  const handleSearchResultClick = () => {
    modalClose();
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        modalClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalClose]);

  return (
    <div>
      <div className="border border-primaryColor/20 rounded-lg bg-white dark:bg-n0 relative">
        <div className="flex items-center">
          <PiMagnifyingGlass size={18} className="text-n300 dark:text-n200 ml-3" />
          <input
            type="text"
            placeholder="Search chats, messages, or indexes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="outline-none p-3 bg-transparent text-sm flex-1 text-n700 dark:text-white placeholder:text-n300 dark:placeholder:text-n200"
          />
          {searchQuery && (
            <button
              onClick={clearSearch}
              className="mr-3 p-1 hover:bg-primaryColor/10 rounded-full transition-colors"
              title="Clear search"
            >
              <PiX size={16} className="text-n300 dark:text-n200 hover:text-primaryColor" />
            </button>
          )}
        </div>
      </div>

      <div className="pt-3">
        {searchQuery && (
          <div className="mb-2 text-xs text-n400 dark:text-n200 flex items-center gap-2">
            {searchQuery !== debouncedQuery ? (
              <>
                <div className="w-3 h-3 border border-primaryColor border-t-transparent rounded-full animate-spin"></div>
                <span>Searching...</span>
              </>
            ) : (
              <span>{filteredChats.length} result{filteredChats.length !== 1 ? 's' : ''} found</span>
            )}
          </div>
        )}

        <div className="flex flex-col gap-1 justify-start items-start w-full max-h-[300px] overflow-auto">
          {filteredChats.length > 0 ? (
            filteredChats.map(({ id, title, indexUsed, createdAt }) => (
              <Link
                key={id}
                href={`/chat/${id}`}
                onClick={handleSearchResultClick}
                className="flex justify-between items-center gap-2 hover:text-primaryColor hover:bg-primaryColor/10 rounded-xl duration-300 py-3 px-3 relative w-full group cursor-pointer"
              >
                <PiAlignLeft size={20} className="text-primaryColor flex-shrink-0" />
                <div className="flex flex-col gap-1 flex-1 min-w-0">
                  <span className="text-sm font-medium truncate text-n700 dark:text-white group-hover:text-primaryColor">
                    {highlightText(title.slice(0, 50) + (title.length > 50 ? "..." : ""), debouncedQuery)}
                  </span>
                  {indexUsed && (
                    <span className="text-xs text-n400 dark:text-n200 truncate group-hover:text-primaryColor/80">
                      Index: {highlightText(indexUsed, debouncedQuery)}
                    </span>
                  )}
                  <span className="text-xs text-n300 dark:text-n300 group-hover:text-primaryColor/60">
                    {new Date(createdAt).toLocaleDateString()}
                  </span>
                </div>

                {/* Enhanced tooltip for index information */}
                {indexUsed && (
                  <div className="absolute bottom-full left-0 mb-2 px-3 py-2 bg-n700 dark:bg-n0 border border-n300 dark:border-n500 text-white dark:text-n30 text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-50 shadow-lg">
                    <div className="font-medium">Index: {indexUsed}</div>
                    <div className="text-n200 dark:text-n200 mt-1">
                      Created: {new Date(createdAt).toLocaleString()}
                    </div>
                    <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-n700 dark:border-t-n0"></div>
                  </div>
                )}
              </Link>
            ))
          ) : debouncedQuery ? (
            <div className="flex flex-col items-center justify-center py-8 text-n400 dark:text-n200">
              <PiMagnifyingGlass size={32} className="mb-2 opacity-50" />
              <p className="text-sm font-medium">No results found</p>
              <p className="text-xs text-n300 dark:text-n300 mt-1">
                Try searching with different keywords
              </p>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-n400 dark:text-n200">
              <PiAlignLeft size={32} className="mb-2 opacity-50" />
              <p className="text-sm font-medium">All your chats</p>
              <p className="text-xs text-n300 dark:text-n300 mt-1">
                Start typing to search through your conversations
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
