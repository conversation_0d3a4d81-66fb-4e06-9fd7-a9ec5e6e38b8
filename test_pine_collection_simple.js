// Simple test to debug PINE collection API issues
// Run this in browser console or Node.js

async function testPineCollectionAPI() {
    console.log("🧪 Testing PINE Collection API for Index Fetching");
    console.log("=" .repeat(60));
    
    // Test different email addresses
    const testEmails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        // Add any real email addresses you want to test
    ];
    
    for (const email of testEmails) {
        console.log(`\n📧 Testing email: ${email}`);
        console.log("-".repeat(40));
        
        try {
            // Test FAISS collection
            const faissUrl = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(email.trim())}`;
            console.log(`🔗 FAISS URL: ${faissUrl}`);
            
            const faissResponse = await fetch(faissUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'xxxid': 'FAISS'
                }
            });
            
            console.log(`📡 FAISS Response Status: ${faissResponse.status}`);
            
            if (faissResponse.ok) {
                const faissData = await faissResponse.json();
                console.log(`📋 FAISS Response:`, faissData);
                
                if (faissData.statusCode === 200 && Array.isArray(faissData.source)) {
                    console.log(`📊 Found ${faissData.source.length} records in FAISS collection`);
                    
                    const indexes = [];
                    faissData.source.forEach((jsonStr, index) => {
                        try {
                            const record = JSON.parse(jsonStr);
                            console.log(`📄 Record ${index + 1}:`, record);
                            
                            if (record.index_name) {
                                indexes.push(record.index_name);
                            }
                        } catch (parseError) {
                            console.error(`❌ Parse error for record ${index + 1}:`, parseError);
                        }
                    });
                    
                    console.log(`🎯 Extracted indexes: [${indexes.join(', ')}]`);
                } else {
                    console.log(`ℹ️ No data found for ${email} in FAISS collection`);
                }
            } else {
                console.error(`❌ FAISS API failed: ${faissResponse.status} ${faissResponse.statusText}`);
            }
            
            // Also test PINE collection for comparison
            const pineUrl = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(email.trim())}`;
            console.log(`🔗 PINE URL: ${pineUrl}`);
            
            const pineResponse = await fetch(pineUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'xxxid': 'PINE'
                }
            });
            
            console.log(`📡 PINE Response Status: ${pineResponse.status}`);
            
            if (pineResponse.ok) {
                const pineData = await pineResponse.json();
                console.log(`📋 PINE Response:`, pineData);
                
                if (pineData.statusCode === 200 && Array.isArray(pineData.source)) {
                    console.log(`📊 Found ${pineData.source.length} records in PINE collection`);
                } else {
                    console.log(`ℹ️ No data found for ${email} in PINE collection`);
                }
            } else {
                console.error(`❌ PINE API failed: ${pineResponse.status} ${pineResponse.statusText}`);
            }
            
        } catch (error) {
            console.error(`❌ Error testing ${email}:`, error);
        }
    }
    
    console.log("\n" + "=".repeat(60));
    console.log("🏁 Test completed");
}

// Test different field names in case 'client' is not correct
async function testDifferentFieldNames() {
    console.log("🧪 Testing Different Field Names");
    console.log("=" .repeat(60));
    
    const testEmail = '<EMAIL>';
    const fieldNames = ['client', 'email', 'user_email', 'userEmail', 'user', 'owner'];
    
    for (const fieldName of fieldNames) {
        console.log(`\n🔍 Testing field: ${fieldName}`);
        console.log("-".repeat(30));
        
        try {
            const url = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS&f1_field=${fieldName}&f1_op=eq&f1_value=${encodeURIComponent(testEmail)}`;
            console.log(`🔗 URL: ${url}`);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'xxxid': 'FAISS'
                }
            });
            
            console.log(`📡 Status: ${response.status}`);
            
            if (response.ok) {
                const data = await response.json();
                if (data.statusCode === 200 && Array.isArray(data.source) && data.source.length > 0) {
                    console.log(`✅ Found data with field '${fieldName}': ${data.source.length} records`);
                    console.log(`📋 Sample record:`, JSON.parse(data.source[0]));
                } else {
                    console.log(`❌ No data found with field '${fieldName}'`);
                }
            } else {
                console.log(`❌ API failed for field '${fieldName}': ${response.status}`);
            }
            
        } catch (error) {
            console.error(`❌ Error testing field '${fieldName}':`, error);
        }
    }
}

// Test to see all available data in FAISS collection (without filtering)
async function testAllFAISSData() {
    console.log("🧪 Testing All FAISS Collection Data");
    console.log("=" .repeat(60));
    
    try {
        // Get all records from FAISS collection
        const url = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS`;
        console.log(`🔗 URL: ${url}`);
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'FAISS'
            }
        });
        
        console.log(`📡 Status: ${response.status}`);
        
        if (response.ok) {
            const data = await response.json();
            console.log(`📋 Response:`, data);
            
            if (data.statusCode === 200 && Array.isArray(data.source)) {
                console.log(`📊 Total records in FAISS collection: ${data.source.length}`);
                
                // Show first few records to understand structure
                const sampleSize = Math.min(5, data.source.length);
                console.log(`📄 First ${sampleSize} records:`);
                
                for (let i = 0; i < sampleSize; i++) {
                    try {
                        const record = JSON.parse(data.source[i]);
                        console.log(`Record ${i + 1}:`, record);
                        
                        // Show available fields
                        console.log(`Available fields: [${Object.keys(record).join(', ')}]`);
                    } catch (parseError) {
                        console.error(`Parse error for record ${i + 1}:`, parseError);
                    }
                }
                
                // Extract unique field names
                const allFields = new Set();
                data.source.forEach(jsonStr => {
                    try {
                        const record = JSON.parse(jsonStr);
                        Object.keys(record).forEach(key => allFields.add(key));
                    } catch (e) {
                        // Skip invalid records
                    }
                });
                
                console.log(`🔑 All unique fields in FAISS collection: [${Array.from(allFields).join(', ')}]`);
                
            } else {
                console.log(`❌ No data found in FAISS collection`);
            }
        } else {
            console.error(`❌ API failed: ${response.status} ${response.statusText}`);
        }
        
    } catch (error) {
        console.error(`❌ Error:`, error);
    }
}

// Run all tests
async function runAllTests() {
    await testPineCollectionAPI();
    await testDifferentFieldNames();
    await testAllFAISSData();
}

// Export for use in browser or Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testPineCollectionAPI,
        testDifferentFieldNames,
        testAllFAISSData,
        runAllTests
    };
} else {
    // Browser environment - make functions available globally
    window.testPineCollectionAPI = testPineCollectionAPI;
    window.testDifferentFieldNames = testDifferentFieldNames;
    window.testAllFAISSData = testAllFAISSData;
    window.runAllTests = runAllTests;
    
    console.log("🚀 PINE Collection Test Functions Loaded");
    console.log("Available functions:");
    console.log("- testPineCollectionAPI()");
    console.log("- testDifferentFieldNames()");
    console.log("- testAllFAISSData()");
    console.log("- runAllTests()");
}