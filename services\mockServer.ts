/**
 * Mock server for file upload API
 * This file simulates a backend server for handling file uploads during development
 *
 * In a real application, you would replace this with actual API calls to your backend
 */

/**
 * Simulates uploading a CSV file to Pinecone
 * @param {File} file - The CSV file to upload
 * @returns {Promise} - Promise that resolves with a mock response
 */
export const mockUploadCSVToPinecone = (file: File) => {
  return new Promise<{
    success: boolean;
    indexName: string;
    vectorCount: number;
    message: string;
  }>((resolve, reject) => {
    // Validate file type
    if (file.type !== 'text/csv') {
      reject(new Error('Only CSV files are supported for Pinecone upload'));
      return;
    }

    // Simulate network delay
    setTimeout(() => {
      // Create index name from file name
      const indexName = file.name.replace('.csv', '').replace(/\s+/g, '_').toLowerCase();

      // Randomly succeed or fail (95% success rate)
      const shouldSucceed = Math.random() < 0.95;

      if (shouldSucceed) {
        resolve({
          success: true,
          indexName: indexName,
          vectorCount: Math.floor(Math.random() * 1000) + 100, // Random number of vectors
          message: `CSV data successfully uploaded to Pinecone index: ${indexName}`
        });
      } else {
        reject(new Error('Server error: Failed to upload CSV to Pinecone'));
      }
    }, 2000); // Simulate a 2 second delay for processing
  });
};

/**
 * Simulates a file upload to the server
 * @param {File} file - The file to upload
 * @returns {Promise} - Promise that resolves with a mock response
 */
export const mockUploadFile = (file: File) => {
  return new Promise<{
    success: boolean;
    fileId: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    uploadDate: string;
    url: string;
    message: string;
  }>((resolve, reject) => {
    // Simulate network delay
    setTimeout(() => {
      // Randomly succeed or fail (90% success rate)
      const shouldSucceed = Math.random() < 0.9;

      if (shouldSucceed) {
        resolve({
          success: true,
          fileId: `file-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          uploadDate: new Date().toISOString(),
          url: `https://example.com/files/${file.name}`,
          message: 'File uploaded successfully'
        });
      } else {
        reject(new Error('Server error: Failed to upload file'));
      }
    }, 1500); // Simulate a 1.5 second delay
  });
};

/**
 * Simulates uploading multiple files to the server
 * @param {File[]} files - Array of files to upload
 * @returns {Promise} - Promise that resolves with an array of mock responses
 */
export const mockUploadMultipleFiles = (files: File[]) => {
  const uploadPromises = files.map(file => mockUploadFile(file));
  return Promise.all(uploadPromises);
};

/**
 * Simulates getting a list of uploaded files from the server
 * @returns {Promise} - Promise that resolves with a mock response
 */
export const mockGetUploadedFiles = () => {
  return new Promise<{
    success: boolean;
    files: Array<{
      fileId: string;
      fileName: string;
      fileSize: number;
      fileType: string;
      uploadDate: string;
      url: string;
    }>;
  }>((resolve) => {
    // Simulate network delay
    setTimeout(() => {
      resolve({
        success: true,
        files: [
          {
            fileId: 'file-1234567890',
            fileName: 'example-document.pdf',
            fileSize: 1024 * 1024 * 2.5, // 2.5 MB
            fileType: 'application/pdf',
            uploadDate: '2023-05-15T10:30:00Z',
            url: 'https://example.com/files/example-document.pdf'
          },
          {
            fileId: 'file-0987654321',
            fileName: 'sample-image.jpg',
            fileSize: 1024 * 512, // 512 KB
            fileType: 'image/jpeg',
            uploadDate: '2023-05-14T15:45:00Z',
            url: 'https://example.com/files/sample-image.jpg'
          }
        ]
      });
    }, 800); // Simulate a 0.8 second delay
  });
};

/**
 * Simulates deleting a file from the server
 * @param {string} fileId - ID of the file to delete
 * @returns {Promise} - Promise that resolves with a mock response
 */
export const mockDeleteFile = (fileId: string) => {
  return new Promise<{
    success: boolean;
    fileId: string;
    message: string;
  }>((resolve) => {
    // Simulate network delay
    setTimeout(() => {
      resolve({
        success: true,
        fileId,
        message: 'File deleted successfully'
      });
    }, 500); // Simulate a 0.5 second delay
  });
};
